import { DDMMYYYY_Format } from '.';

// Enhanced version with options
interface ExcelOptions {
	filename?: string;
	sheetName?: string;
	includeHeaders?: boolean;
	headerStyle?: boolean;
}
export const toExcel = async (data: any[], options: ExcelOptions = {}): Promise<void> => {
	const {
		filename = 'export',
		sheetName = 'Sheet1',
		includeHeaders = true,
		headerStyle = true
	} = options;

	if (!data.length) return;

	const headers = Object.keys(data[0]);
	let worksheetXML = '';

	if (includeHeaders) {
		worksheetXML += createRow(headers, headerStyle);
	}

	const dataRows = data
		.map((item) =>
			createRow(
				headers.map((header) => item[header] ?? ''),
				false
			)
		)
		.join('');

	const fullWorksheetXML = `
    <Worksheet ss:Name="${sheetName}">
      <Table>
        ${worksheetXML}
        ${dataRows}
      </Table>
    </Worksheet>`;

	const workbookXML = createWorkbookXML(fullWorksheetXML);
	const blob = new Blob([workbookXML], {
		type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	});

	downloadBlob(
		blob,
		filename.concat(DDMMYYYY_Format(new Date().toISOString(), 'datetime')).concat('.xlsx')
	);
};

export const toCSV = (data: any[], filename: string = 'export.csv'): void => {
	if (!data.length) return;

	const headers = Object.keys(data[0]);
	const csvRows = [
		headers.join(','),
		...data.map((row) =>
			headers.map((header) => `"${(row[header] ?? '').toString().replace(/"/g, '""')}"`).join(',')
		)
	];

	const blob = new Blob([csvRows.join('\n')], { type: 'text/csv;charset=utf-8;' });
	downloadBlob(
		blob,
		filename.concat(DDMMYYYY_Format(new Date().toISOString(), 'datetime')).concat('.csv')
	);
};

// Helpers
const createRow = (values: any[], isHeader = false): string => {
	const cells = values
		.map((value) => {
			const cellType = typeof value === 'number' ? 'Number' : 'String';
			return `
      <Cell${isHeader ? ' ss:StyleID="Header"' : ''}>
        <Data ss:Type="${cellType}">${escapeXML(value)}</Data>
      </Cell>`;
		})
		.join('');

	return `<Row>${cells}</Row>`;
};

const createWorkbookXML = (worksheetXML: string): string => `
<?xml version="1.0"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">

 <Styles>
   <Style ss:ID="Header">
     <Font ss:Bold="1" ss:Color="#FFFFFF"/>
     <Interior ss:Color="#007ACC" ss:Pattern="Solid"/>
     <Alignment ss:Vertical="Center" ss:Horizontal="Center"/>
   </Style>
   <Style ss:ID="Default">
     <Alignment ss:Vertical="Center"/>
   </Style>
 </Styles>

 ${worksheetXML}
</Workbook>`;

const escapeXML = (value: any): string =>
	(value ?? '')
		.toString()
		.replace(/&/g, '&amp;')
		.replace(/</g, '&lt;')
		.replace(/>/g, '&gt;')
		.replace(/"/g, '&quot;')
		.replace(/'/g, '&apos;');

const downloadBlob = (blob: Blob, filename: string) => {
	const url = URL.createObjectURL(blob);
	const link = document.createElement('a');
	link.href = url;
	link.download = filename;
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
	URL.revokeObjectURL(url);
};

// jsonToPdf.ts
export interface PDFOptions {
	filename?: string;
	title?: string;
	fontSize?: number;
	headerColor?: string;
}

export const toPDF = (data: any[], options: PDFOptions = {}): void => {
	if (!data.length) {
		console.warn('No data to export');
		return;
	}

	const htmlContent = generatePDFHtml(data, options);
	downloadHTMLAsPDF(htmlContent);
};

const generatePDFHtml = (data: any[], options: PDFOptions): string => {
	const addNoTodata = data.map((item, i) => ({ no: i + 1, ...item }));
	const headers = Object.keys(addNoTodata[0]);
	const { title = 'Data Export', fontSize = 10, headerColor = '#007ACC' } = options;

	const rows = addNoTodata.map((item, i) => {
		const pageBreak = i > 0 && i % 20 === 0 ? 'page-break' : '';
		const cells = headers.map((h) => `<td>${escapeHTML(item[h] ?? '')}</td>`).join('');
		return `<tr class="${pageBreak}">${cells}</tr>`;
	});

	return `<!DOCTYPE html>
<html>
<head>
  <title>${title} ${DDMMYYYY_Format(new Date().toISOString(), 'datetime')} </title>
  <style>
    body {
      font-family: Arial, sans-serif;
      font-size: ${fontSize}px;
      margin: 0px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
    }
    th {
      background: ${headerColor};
      color: white;
      padding: 10px;
      border: 1px solid #ccc;
      text-align: center;
    }
    td {
      padding: 8px;
      border: 1px solid #ccc;
    }
    tr:nth-child(even) {
      background: #f2f2f2;
  
    }
  
    @media print {
      @page {
        margin: 5mm;
        @bottom-right { content: "Page " counter(page) " of " counter(pages); font-size: ${fontSize - 1}px; padding-bottom: 5mm; color: #666;
      }
    }
  </style>
</head>
<body>
  <h1 style="font-size: ${fontSize + 2}px;padding-top: 0px;padding-bottom: 5px;margin: 0px;text-align: center;">${escapeHTML(title)} ${escapeHTML(DDMMYYYY_Format(new Date().toISOString(), 'datetime'))} </h1>
  <table>
    <thead>
      <tr>${headers.map((h) => `<th>${escapeHTML(h)}</th>`).join('')}</tr>
    </thead>
    <tbody>
      ${rows.join('')}
    </tbody>
  </table>
</body>
</html>`;
};

const escapeHTML = (value: any): string =>
	String(value ?? '')
		.replace(/&/g, '&amp;')
		.replace(/</g, '&lt;')
		.replace(/>/g, '&gt;')
		.replace(/"/g, '&quot;')
		.replace(/'/g, '&#039;');

const downloadHTMLAsPDF = (html: string): void => {
	const blob = new Blob([html], { type: 'text/html' });
	const url = URL.createObjectURL(blob);
	const iframe = document.createElement('iframe');
	iframe.style.display = 'none';
	document.body.appendChild(iframe);

	iframe.onload = () => {
		iframe.contentWindow?.focus();
		setTimeout(() => {
			iframe.contentWindow?.print();
			setTimeout(() => {
				document.body.removeChild(iframe);
				URL.revokeObjectURL(url);
			}, 1000);
		}, 500);
	};

	iframe.src = url;
};

export function toJson(data: any[], title: string) {
	const jsonString = JSON.stringify(data, null, 2);
	const blob = new Blob([jsonString], { type: 'application/json' });
	const url = URL.createObjectURL(blob);

	const a = document.createElement('a');
	a.href = url;
	a.download = `${title}${DDMMYYYY_Format(new Date().toISOString(), 'datetime')}.json`;
	document.body.appendChild(a);
	a.click();
	document.body.removeChild(a);
	URL.revokeObjectURL(url);
}
