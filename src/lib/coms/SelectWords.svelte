<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	interface Props {
		words: string[];
		height?: string;
		placeholder?: string;
		name: string;
		value?: string;
		type?: 'text' | 'textarea';
		rows?: string;
	}

	let {
		words,
		height = '300',
		placeholder = '',
		name,
		value = $bindable(''),
		type = 'text',
		rows = '2'
	}: Props = $props();

	function handleClick(word: string) {
		if (value.includes(`${word},`)) {
			value = value.replace(`${word},`, '');
		} else {
			value = value.concat(`${word},`);
		}
	}
</script>

<div class="dropdown form-control m-0 p-0 shadow-none border-0">
	<div class="input-group">
		<div
			style="cursor: pointer;"
			aria-label="showtext"
			data-bs-toggle="dropdown"
			id="dropdown"
			data-bs-display="static"
			data-bs-auto-close="outsite"
			class="input-group-text"
		>
			<i class="fa-solid fa-list-ul"></i>
		</div>
		{#if type === 'textarea'}
			<textarea
				rows={Number(rows)}
				{placeholder}
				{name}
				bind:value
				autocomplete="off"
				class="form-control"
			></textarea>
		{:else}
			<input {placeholder} {name} bind:value class="form-control" type="text" />
		{/if}

		<ul style="width: 100%;" class="dropdown-menu">
			<div style=" max-height: {height.concat('px')}; overflow-y: auto;">
				{#each words as item, index (index)}
					{@const is_same = value.includes(`${item},`)}
					<li>
						<button
							class:active={is_same}
							type="button"
							onclick={(e) => {
								e.stopPropagation();
								handleClick(item);
							}}
							class="dropdown-item"
						>
							{#if is_same}
								<i class="fa-solid fa-square-check"></i>
							{:else}
								<i class="fa-regular fa-square"></i>
							{/if}
							{item ?? ''}
						</button>
					</li>
				{:else}
					<button type="button" class="dropdown-item">
						<i class="fa-solid fa-magnifying-glass"></i>
						{locale.T('none_data')}
					</button>
				{/each}
			</div>
		</ul>
	</div>
</div>
