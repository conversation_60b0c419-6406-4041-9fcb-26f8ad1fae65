<script lang="ts">
	import { toPDF, toExcel, toJson, toCSV } from '$lib/helper/export';

	let { data, title }: { data: any[]; title: string } = $props();
</script>

<div class="dropdown">
	<div
		style="cursor: pointer;"
		class="form-control dropdown-toggle text-dark"
		data-bs-toggle="dropdown"
		aria-expanded="false"
	>
		<i class="fa-solid fa-upload"></i> Exprot
		<ul class="dropdown-menu">
			<li>
				<button type="button" onclick={() => toJson(data, title)} class="dropdown-item"
					><i class="fa-solid fa-file-code"></i> To JSON
				</button>
			</li>
			<li>
				<button
					type="button"
					onclick={() =>
						toExcel(data, {
							filename: `${title}`,
							headerStyle: true,
							includeHeaders: true
						})}
					class="dropdown-item"
					><i class="fa-solid fa-file-excel"></i> To Excel
				</button>
			</li>
			<li>
				<button type="button" onclick={() => toCSV(data, `${title}`)} class="dropdown-item"
					><i class="fa-solid fa-file-csv"></i> To CSV
				</button>
			</li>
			<li>
				<button
					type="button"
					onclick={() =>
						toPDF(data, {
							filename: `${title}`,
							title: title,
							fontSize: 12
						})}
					class="dropdown-item"
					><i class="fa-solid fa-file-pdf"></i> To PDF
				</button>
			</li>
		</ul>
	</div>
</div>
