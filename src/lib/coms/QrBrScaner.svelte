<script lang="ts">
	import { BrowserMultiFormatReader } from '@zxing/browser';
	import { locale } from '../translations/locales.svelte';
	let videoElement: HTMLVideoElement | null = $state(null);
	interface Props {
		result: string;
		type?: 'barcode' | 'qrcode';
	}
	let { type = 'qrcode', result = $bindable('') }: Props = $props();
	let scanning = $state(false);
	let codeReader: BrowserMultiFormatReader | null = $state(null);
	let controls: any = $state(null);

	async function startScan() {
		result = '';

		scanning = true;
		codeReader = new BrowserMultiFormatReader();
		try {
			controls = await codeReader.decodeFromVideoDevice(
				undefined,
				videoElement!,
				(res, err, controlsInstance) => {
					if (res) {
						result = res.getText();
						scanning = false;
						controlsInstance.stop();
					}
					// if (err && err.name !== 'NotFoundException') {
					// }
				}
			);
			// Clean up on destroy

			$effect(() => {
				return () => {
					controls?.stop();
					alert('destroy');
				};
			});
		} catch (e: any) {
			scanning = false;
			console.log(e);
		}
	}

	function stopScan() {
		scanning = false;
		controls?.stop();
	}
</script>

<button
	data-bs-toggle="modal"
	class="form-control text-success text-start"
	data-bs-target="#staticBackdrop"
	type="button"
	disabled={scanning}
>
	{#if type === 'barcode'}
		<i class="fa-solid fa-barcode"></i> Barcode Scan
	{:else}
		<i class="fa-solid fa-qrcode"></i> QR Scan
	{/if}
</button>
<!-- Button trigger modal -->

<!-- Modal -->
<div
	class="modal fade"
	id="staticBackdrop"
	data-bs-backdrop="static"
	data-bs-keyboard="false"
	tabindex="-1"
	aria-labelledby="staticBackdropLabel"
	aria-hidden="true"
>
	<div class="modal-dialog">
		<div style="width: 385px;" class="modal-content">
			<div class="modal-body">
				<div class="img-thumbnail video-container">
					<video
						bind:this={videoElement}
						height="340"
						width="340"
						autoplay
						muted
						playsinline
						class="video-border"
					></video>

					<div class={type === 'barcode' ? 'scan-bar' : 'scan-qr'}></div>

					{#if scanning && !result}
						<!-- Spinner overlay -->
						<div class="spinner-overlay text-bg-light">
							<i class="fa-solid fa-spinner fa-spin fa-4x"></i>
							<!-- <div class="spinner"></div> -->
						</div>
					{/if}
					{#if !scanning && result}
						<!-- Spinner overlay -->
						<div class="spinner-overlay text-bg-light">
							{result}
							<!-- <div class="spinner"></div> -->
						</div>
					{/if}
				</div>
			</div>
			<div class="modal-footer flex-nowrap p-0">
				<button
					id="close_delete_modal"
					type="button"
					onclick={stopScan}
					class=" btn btn-lg btn-link fs-6 text-decoration-none col-6 py-3 m-0 rounded-0 border-end"
					data-bs-dismiss="modal"
				>
					{#if result}
						{locale.T('save')}
					{:else}
						{locale.T('close')}
					{/if}
				</button>
				<button
					type="button"
					onclick={startScan}
					class="btn btn-lg btn-link fs-6 text-decoration-none text-danger col-6 py-3 m-0 rounded-0"
				>
					<strong>{locale.T('rescan')}</strong>
				</button>
			</div>
		</div>
	</div>
</div>

<style>
	video {
		background: #000;
	}
	.video-border {
		object-fit: cover;
	}
	.video-container {
		position: relative;
		display: inline-block;
	}
	.scan-qr {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 250px;
		height: 250px;
		transform: translate(-50%, -50%);
		border: 2px solid #008cff;
		border-radius: 10px;
		box-sizing: border-box;
		pointer-events: none;
		z-index: 2;
	}
	.scan-bar {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 250px;
		height: 150px;
		transform: translate(-50%, -50%);
		border: 2px solid #008cff;
		border-radius: 10px;
		box-sizing: border-box;
		pointer-events: none;
		z-index: 2;
	}
	.spinner-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;
	}
</style>
