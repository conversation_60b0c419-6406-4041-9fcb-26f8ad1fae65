<script lang="ts">
	import { navigating } from '$app/state';
	import { fade } from 'svelte/transition';
	import { browser } from '$app/environment';
	import { afterNavigate } from '$app/navigation';
	import { store } from '$lib/store/store.svelte';
	let show = $state(false);
	let delayTimer: ReturnType<typeof setTimeout> | null = null;
	let isSidebarToggled = $state(false);

	$effect(() => {
		const isNav = Boolean(
			navigating && ['form', 'goto', 'leave', 'link', 'popstate'].includes(navigating.type ?? '')
		);
		const isBusy = Boolean(store.globalLoading);

		// cancel any pending timer before scheduling a new one
		if (delayTimer) {
			clearTimeout(delayTimer);
			delayTimer = null;
		}

		if (isNav || isBusy) {
			const delay = isNav ? 200 : 400;
			delayTimer = setTimeout(() => {
				show = true;
			}, delay);
		} else {
			show = false;
		}

		return () => {
			if (delayTimer) {
				clearTimeout(delayTimer);
				delayTimer = null;
			}
		};
	});

	// ensure spinner hides after navigations complete
	if (browser) {
		afterNavigate(() => {
			if (!store.globalLoading) {
				if (delayTimer) {
					clearTimeout(delayTimer);
					delayTimer = null;
				}
				show = false;
			}
		});
	}

	// Track sidebar toggle state
	$effect(() => {
		if (!browser) return;

		const checkSidebarState = () => {
			isSidebarToggled = document.body.classList.contains('sb-sidenav-toggled');
		};

		// Check initial state
		checkSidebarState();

		// Create observer to watch for class changes on body
		const observer = new MutationObserver((mutations) => {
			mutations.forEach((mutation) => {
				if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
					checkSidebarState();
				}
			});
		});
		observer.observe(document.body, {
			attributes: true,
			attributeFilter: ['class']
		});

		return () => observer.disconnect();
	});

	// Calculate overlay and spinner positioning based on sidebar state and screen size
	let overlayStyle = $derived(() => {
		if (!browser)
			return 'top: 56px; left: 225px; right: 0; bottom: 0; z-index: 1070; opacity: 0.1;';

		const isDesktop = window.innerWidth >= 992;

		if (isDesktop && isSidebarToggled) {
			// Sidebar is hidden on desktop - make fullscreen
			return 'top: 56px; left: 0; right: 0; bottom: 0; z-index: 1070; opacity: 0.1;';
		} else if (isDesktop) {
			// Sidebar is visible on desktop - exclude sidebar area
			return 'top: 56px; left: 225px; right: 0; bottom: 0; z-index: 1070; opacity: 0.1;';
		} else {
			// Mobile - always fullscreen (sidebar overlays)
			return 'top: 56px; left: 0; right: 0; bottom: 0; z-index: 1070; opacity: 0.1;';
		}
	});

	let spinnerStyle = $derived(() => {
		if (!browser) return 'z-index: 1080; margin-left: 112px;';

		const isDesktop = window.innerWidth >= 992;

		if (isDesktop && isSidebarToggled) {
			// Sidebar is hidden on desktop - center in full screen
			return 'z-index: 1080; margin-left: 0;';
		} else if (isDesktop) {
			// Sidebar is visible on desktop - center in content area (offset by half sidebar width)
			return 'z-index: 1080; margin-left: 112px;';
		} else {
			// Mobile - center in full screen
			return 'z-index: 1080; margin-left: 0;';
		}
	});
</script>

{#if show}
	<!-- Dark overlay -->
	<div
		class="position-fixed bg-dark rounded m-1"
		style={overlayStyle()}
		in:fade={{ duration: 200 }}
		out:fade={{ duration: 100 }}
	></div>

	<!-- Spinner -->
	<div
		class="position-fixed top-50 start-50 translate-middle"
		style={spinnerStyle()}
		in:fade={{ duration: 200 }}
		out:fade={{ duration: 100 }}
	>
		<div class="bg-white rounded-circle p-3 shadow-lg border">
			<i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
		</div>
	</div>
{/if}
