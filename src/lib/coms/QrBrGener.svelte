<script lang="ts">
	import QRCode from 'qrcode';
	import JsBarcode from 'jsbarcode';

	// Props are now more detailed and configurable
	let {
		value,
		type = 'barcode',
		width,
		height
	}: {
		value: string;
		type: 'barcode' | 'qrcode';
		width?: number;
		height?: number;
	} = $props();

	let svg: string = $state('');
	let error: string = $state('');

	async function generate() {
		error = '';
		if (!value) {
			svg = '';
			return;
		}

		try {
			if (type === 'qrcode') {
				svg = await QRCode.toString(value, {
					type: 'svg',

					margin: 0,
					width: 250
				});
			} else {
				// Switched to SVG generation for barcodes for scalability and consistency.
				const svgElement = document.createElementNS('http://www.w3.org/2000/svg', 'svg');

				// JsBarcode populates the SVG element. The final width is determined by the
				// value length and bar width, but CSS will scale it down responsively.
				JsBarcode(svgElement, value, {
					displayValue: false,
					textMargin: 0,
					margin: 0,
					fontSize: 10,
					height: height,
					width: width,
					format: 'CODE39'
				});

				svg = svgElement.outerHTML;
			}
		} catch (e) {
			console.error(`Failed to generate ${type}:`, e);
			error = `Failed to generate ${type}. Check console for details.`;
			svg = '';
		}
	}

	// Effect now correctly re-runs when `type` or other props change.
	$effect(() => {
		generate();
	});
</script>

<div class="qr-br-gener-container">
	{#if error}
		<div style="color:red;">{error}</div>
	{/if}

	{#if svg}
		<div class="img-thumbnail p-1">
			<!-- Unified rendering for both QR and Barcode as they are both SVGs now -->
			{@html svg}
		</div>
	{/if}
</div>

<style>
	.qr-br-gener-container {
		width: 100%;
	}
	.qr-br-gener-container :global(svg) {
		width: 100%;
		height: auto;
		display: block;
	}
</style>
