<script lang="ts">
	import InputDocument from '$lib/coms-form/InputDocument.svelte';
	import type { TAddress, TDocumentSetting, TFields } from '$lib/type';
	import KhDateTime from '$lib/coms-document/KhDateInput.svelte';
	import Header from '$lib/coms-document/Header.svelte';
	interface Prop {
		occupation_list?: string[];
		p_name: string;
		p_nation: string;
		fields: TFields[];
		address?: TAddress;
		title_khm: string;
		get_document_setting?: TDocumentSetting;
		title_eng: string;
		logo: string;
	}
	let {
		p_name,
		p_nation,
		occupation_list,
		address,
		fields,
		logo,
		title_eng,
		title_khm,
		get_document_setting
	}: Prop = $props();
	let default_address = $derived(
		`${address?.village?.type ?? ''} ${address?.village?.name_khmer ?? ''} ${address?.commune?.type ?? ''} ${address?.commune?.name_khmer ?? ''} ${address?.district?.type ?? ''} ${address?.district?.name_khmer ?? ''} ${address?.provice?.type ?? ''} ${address?.provice?.name_khmer ?? ''}`
	);
	let name = $derived(fields.find((e) => e.name === 'name')?.result ?? '');
	let gender = $derived(fields.find((e) => e.name === 'gender')?.result ?? '');
	let age = $derived(fields.find((e) => e.name === 'age')?.result ?? '');
	let address_ = $derived(
		fields.find((e) => e.name === 'address')?.result
			? fields.find((e) => e.name === 'address')?.result
			: default_address
	);
	let occupation = $derived(fields.find((e) => e.name === 'occupation')?.result ?? '');
	let relationship = $derived(fields.find((e) => e.name === 'relationship')?.result ?? '');
	let contact = $derived(fields.find((e) => e.name === 'contact')?.result ?? '');
	let cute = $derived(fields.find((e) => e.name === 'cute')?.result ?? '');
	let n = $derived(fields.find((e) => e.name === 'n')?.result ?? '');
	let date_1 = $derived(fields.find((e) => e.name === 'date_1')?.result ?? '');
	let date_2 = $derived(fields.find((e) => e.name === 'date_2')?.result ?? '');
	let date_3 = $derived(fields.find((e) => e.name === 'date_3')?.result ?? '');
</script>

<input type="hidden" name="title" value="service_contract" />
<main style="max-width: 1200px;">
	<Header {get_document_setting} {logo} {n} {title_eng} {title_khm} />
	<div class="text-center">
		<h4
			style="color: {get_document_setting?.title_color}"
			class="kh_font_muol_light text-decoration-underline"
		>
			កិច្ចសន្យាទទួលសេវា
		</h4>
	</div>
	<br />
	<div class="section fs-5">
		<div style="color: {get_document_setting?.text_body_color};margin-left:5em" class="mb-2">
			ខ្ញុំបាទ/នាងខ្ញុំឈ្មោះ <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="name"
				value={name}
				width="400px"
				type="text"
			/>
			ភេទ <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="gender"
				value={gender}
				width="140px"
				type="text"
				data_list={['ប្រុស', 'ស្រី']}
			/>
			អាយុ <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="age"
				value={age}
				width="110px"
				type="text"
			/> ឆ្នាំ
		</div>
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			អាសយដ្ឋាន ៖
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="address"
				value={address_ ?? ''}
				width="930px"
				type="text"
			/>
		</div>
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			លេខទូរស័ព្ទ ៖
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="contact"
				value={contact}
				width="930px"
				type="text"
			/>
		</div>
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			មុខរបរ ៖
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="occupation"
				value={occupation}
				data_list={occupation_list}
				width="490px"
				type="text"
			/>
			ត្រូវជា ៖
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="relationship"
				value={relationship}
				width="405px"
				type="text"
			/>
		</div>
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			របស់អ្នកជំងឺឈ្មោះ
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				readonly
				name="p_name"
				value={p_name}
				width="720px"
				type="text"
			/>
			សញ្ជាតិ
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				readonly
				name="p_nation"
				value={p_nation}
				width="100px"
				type="text"
			/>
		</div>
	</div>
	<br />
	<div class="text-center">
		<h4 style="color: {get_document_setting?.title_color}" class="kh_font_muol_light">
			អនុញ្ញាតអោយគ្រូពេទ្យព្យាបាលជំងឺដោយប្រើវិធី
		</h4>
	</div>
	<div class="fs-5 checkbox-group">
		<div class="form-check">
			<input
				checked={fields.some((e) => e.result === 'sc_box_1')}
				class="form-check-input"
				type="checkbox"
				value="sc_box_1"
				name="box"
				id="sc_box_1"
			/>
			<label class="form-check-label" for="sc_box_1"> សង្គ្រោះបន្ទាន់</label>
		</div>
		<div class="form-check">
			<input
				checked={fields.some((e) => e.result === 'sc_box_2')}
				class="form-check-input"
				type="checkbox"
				value="sc_box_2"
				name="box"
				id="sc_box_2"
			/>

			<label class="form-check-label" for="sc_box_2"> សម្រាកព្យាបាលតាមដាន</label>
		</div>
		<div class="form-check">
			<input
				checked={fields.some((e) => e.result === 'sc_box_3')}
				class="form-check-input"
				type="checkbox"
				value="sc_box_3"
				name="box"
				id="sc_box_3"
			/>

			<label class="form-check-label" for="sc_box_3"> វះកាត់ </label>
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="cute"
				value={cute}
				width="250px"
				class="text-start"
			/>
		</div>
		<div class="form-check">
			<input
				checked={fields.some((e) => e.result === 'sc_box_4')}
				class="form-check-input"
				type="checkbox"
				value="sc_box_4"
				name="box"
				id="sc_box_4"
			/>
			<label class="form-check-label" for="sc_box_4"> សម្រាលកូនធម្មតា</label>
		</div>
		<div class="form-check">
			<input
				checked={fields.some((e) => e.result === 'sc_box_5')}
				class="form-check-input"
				type="checkbox"
				value="sc_box_5"
				name="box"
				id="sc_box_5"
			/>
			<label class="form-check-label" for="sc_box_5"> សំអាតទ្វាមាស ឬ ស្បួន</label>
		</div>
		<div class="form-check">
			<input
				checked={fields.some((e) => e.result === 'sc_box_6')}
				class="form-check-input"
				type="checkbox"
				value="sc_box_6"
				name="box"
				id="sc_box_6"
			/>
			<label class="form-check-label" for="sc_box_6">
				រំលូត(កូនស្លាប់ ឬ កូនមានភាពមិនប្រក្រតី)
			</label>
		</div>
	</div>
	<br />
	<div style="color: {get_document_setting?.text_body_color}" class="fs-5 mb-2 text-center">
		តាមបច្ចេកទេសវេជ្ជសាស្ត្រដោយពុំគិតពីផលវិបាក ឬ ឧបសគ្គណាមួយ ដែលអាចកើតឡើងជាយថាហេតុ
		ក្នុងការព្យាបាលនេះឡើយ ។
	</div>
	<div style="color: {get_document_setting?.text_body_color}" class="fs-5 mb-2 text-center">
		យើងខ្ញុំសន្យានឹងទទូលខុសត្រូវខ្លួនឯងទាំងស្រុង និងមិនមានការប្តឹងផ្តល់អ្វីឡើយ ។
	</div>
	<div style="color: {get_document_setting?.text_body_color}" class="fs-5 mb-2 text-center">
		ដើម្បីជាសក្ខីភាព ខ្ញុំបាទ/នាងខ្ញុំ សូមផ្តិតស្នាមមេដៃ ឬ ហត្ថលេខាទុកជាភស្តុតាង ។
	</div>
	<br />
	<div class="text-end fs-5" style="margin-right: 5em;">
		<KhDateTime date={date_1} name="date_1" />
		<div style="padding-right: 20px;font-weight: bold;">ហត្ថលេខា ឬ ស្នាមមេដៃស្តាំ</div>
	</div>
	<div class="text-center fs-5">
		<KhDateTime date={date_2} name="date_2" />
		<div>បានឃើញ និងឯកភាព ឈ្មោះខាងលើនេះ</div>
		<div>ពិតជាបានចូលសម្រាកពិតប្រាកដមែន។</div>
		<div style="font-weight: bold;">ប្រធានផ្នែក/គ្រូពេទ្យព្យាបាល</div>
	</div>
	<div style="margin-left: 5em;" class="fs-5">
		<KhDateTime date={date_3} name="date_3" />
		<div style="padding-left: 45px;">បានឃើញ និងឯកភាព</div>
		<div style="padding-left: 20px;font-weight: bold;">ប្រធានគ្លីនិក/ប្រធានមន្ទីរពេទ្យ</div>
	</div>
</main>
