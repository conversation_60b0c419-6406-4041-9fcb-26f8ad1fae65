import { relations } from 'drizzle-orm';
import { boolean, datetime, int, mysqlTable, varchar } from 'drizzle-orm/mysql-core';
import { visit } from './visit';
import { group, product } from './product';
import { patient } from './patient';
import { staff } from './staff';

export const vaccine = mysqlTable('vaccine', {
	id: int().primaryKey().autoincrement(),
	visit_id: int().references(() => visit.id, { onDelete: 'set null' }),
	injection_id: int().references(() => injection.id, { onDelete: 'cascade' }),
	product_id: int().references(() => product.id, { onDelete: 'set null' }),
	discription: varchar({ length: 255 }),
	injecter_id: int().references(() => staff.id),
	datetime_inject: datetime({ mode: 'string' }),
	datetime_appointment: datetime({ mode: 'string' }),
	times: int().notNull().default(1),
	status: boolean().default(false)
});

export const vaccineRelations = relations(vaccine, ({ one }) => ({
	visit: one(visit, {
		fields: [vaccine.visit_id],
		references: [visit.id]
	}),
	injection: one(injection, {
		fields: [vaccine.injection_id],
		references: [injection.id]
	}),
	product: one(product, {
		fields: [vaccine.product_id],
		references: [product.id]
	}),
	injecter: one(staff, {
		fields: [vaccine.injecter_id],
		references: [staff.id]
	})
}));

export const injection = mysqlTable('injection', {
	id: int().primaryKey().autoincrement(),
	patient_id: int().references(() => patient.id),
	datetime: datetime({ mode: 'string' }).notNull(),
	status: boolean().default(false),
	group_id: int().references(() => group.id),
	discription: varchar({ length: 255 }),
	vaccine_dose_id: int().references(() => vaccineDose.id)
});

export const injectionRelations = relations(injection, ({ many, one }) => ({
	vaccine: many(vaccine),
	patient: one(patient, {
		fields: [injection.patient_id],
		references: [patient.id]
	}),
	vaccineDose: one(vaccineDose, {
		fields: [injection.vaccine_dose_id],
		references: [vaccineDose.id]
	}),
	group: one(group, {
		fields: [injection.group_id],
		references: [group.id]
	})
}));

export const appointmentInjection = mysqlTable('appointment_injection', {
	id: int().primaryKey().autoincrement(),
	times: int().notNull().default(1),
	days: int().notNull().default(0),
	vaccine_dose_id: int().references(() => vaccineDose.id, { onDelete: 'cascade' }),
	create_at: datetime({ mode: 'string' }).notNull()
});

export const appointmentInjectionRelations = relations(appointmentInjection, ({ one }) => ({
	vaccineDose: one(vaccineDose, {
		fields: [appointmentInjection.vaccine_dose_id],
		references: [vaccineDose.id]
	})
}));

export const vaccineDose = mysqlTable('vaccine_dose', {
	id: int().primaryKey().autoincrement(),
	times: int().notNull().default(1),
	group_id: int().references(() => group.id),
	create_at: datetime({ mode: 'string' }).notNull()
});

export const vaccineDoseRelations = relations(vaccineDose, ({ many, one }) => ({
	appointmentInjection: many(appointmentInjection),
	injection: many(injection),
	group: one(group, {
		fields: [vaccineDose.group_id],
		references: [group.id]
	})
}));
