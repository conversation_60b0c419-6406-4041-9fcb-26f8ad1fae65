import { relations } from 'drizzle-orm';
import { int, mysqlTable, varchar, datetime, text, boolean } from 'drizzle-orm/mysql-core';
import { patient } from './patient';
import { staff } from './staff';
import { subjective } from './subjective';
import { vitalSign } from './vitalSign';
import { physicalExam } from './physicaleExam';
import { laboratory, laboratoryRequest } from './laboratory';
import { imagerieRequest } from './imagerie';
import { billing } from './billing';
import { presrciption } from './presrciption';
import { service } from './service';
import { document } from './document';
import { vaccine } from './vaccine';
import { activeBed, activeDepartment } from './departmentBed';
import { accessment } from './accessment';
import { adviceTeaching } from './adviceTeaching';
import { appointment } from './appointment';
import { product } from './product';

export const visit = mysqlTable('visit', {
	id: int().primaryKey().autoincrement(),
	date_checkup: datetime({ mode: 'string' }).notNull(),
	patient_id: int()
		.references(() => patient.id, { onDelete: 'cascade' })
		.notNull(),
	department_id: int().references(() => product.id),
	staff_id: int().references(() => staff.id),
	sender_id: int().references(() => staff.id),
	checkin_type: varchar({ length: 10 }).notNull().$type<'IPD' | 'OPD' | 'SERVICE'>(),
	etiology: varchar({ length: 255 }).notNull(),
	transfer: boolean().default(false).notNull(),
	status: varchar('status', { length: 10 })
		.notNull()
		.$type<'CHECKING' | 'LOADING' | 'DONE'>()
		.default('LOADING'),
	progress_note_id: int().references(() => progressNote.id, {
		onDelete: 'cascade'
	})
});
export const nursingProcess = mysqlTable('nursing_process', {
	id: int().primaryKey().autoincrement(),
	datetime: datetime({ mode: 'string' }),
	accessment: varchar({ length: 255 }),
	health_problems: text(),
	actions: varchar({ length: 255 }),
	evolution: varchar({ length: 255 }),
	nursing_sign: int().references(() => staff.id),
	progress_note_id: int().references(() => progressNote.id, {
		onDelete: 'cascade'
	}),
	active_department_id: int().references(() => activeDepartment.id, {
		onDelete: 'cascade'
	})
});

export const visitRelations = relations(visit, ({ one, many }) => ({
	progressNote: one(progressNote, {
		fields: [visit.progress_note_id],
		references: [progressNote.id]
	}),
	sender: one(staff, {
		fields: [visit.sender_id],
		references: [staff.id]
	}),
	staff: one(staff, {
		fields: [visit.staff_id],
		references: [staff.id]
	}),
	patient: one(patient, {
		fields: [visit.patient_id],
		references: [patient.id]
	}),
	department: one(product, {
		fields: [visit.department_id],
		references: [product.id]
	}),
	subjective: one(subjective),
	vitalSign: one(vitalSign),
	physicalExam: many(physicalExam),
	laboratoryRequest: many(laboratoryRequest),
	presrciption: many(presrciption),
	service: many(service),
	imagerieRequest: many(imagerieRequest),
	laboratory: one(laboratory),
	billing: one(billing),
	document: many(document),
	vaccine: many(vaccine),
	accessment: one(accessment),
	adviceTeaching: one(adviceTeaching),
	appointment: one(appointment)
}));

export const progressNote = mysqlTable('progress_note', {
	id: int().primaryKey().autoincrement(),
	date_checkup: datetime({ mode: 'string' }).notNull(),
	date_checkout: datetime({ mode: 'string' }),
	is_discharge: boolean().default(false).notNull(),
	is_emr: boolean().default(false).notNull(),
	staff_id: int().references(() => staff.id),
	etiology: varchar({ length: 255 }).notNull(),
	status: varchar({ length: 10 })
		.notNull()
		.$type<'CHECKING' | 'LOADING' | 'DONE'>()
		.default('LOADING'),
	department_id: int()
		.references(() => product.id)
		.notNull(),
	patient_id: int()
		.references(() => patient.id, { onDelete: 'cascade', onUpdate: 'cascade' })
		.notNull(),
	inclund_pay: varchar({ length: 30 }).$type<
		'treatment' | 'prescription' | 'prescription_treatment'
	>(),
	pres_open_id: int().references(() => staff.id)
});

export const progressNoteRelations = relations(progressNote, ({ one, many }) => ({
	visit: many(visit),
	nursingProcess: many(nursingProcess),
	vitalSign: many(vitalSign),
	patient: one(patient, {
		fields: [progressNote.patient_id],
		references: [patient.id]
	}),
	staff: one(staff, {
		fields: [progressNote.staff_id],
		references: [staff.id]
	}),
	pres_open: one(staff, {
		fields: [progressNote.pres_open_id],
		references: [staff.id]
	}),
	department: one(product, {
		fields: [progressNote.department_id],
		references: [product.id]
	}),
	service: many(service),
	billing: one(billing),
	presrciption: many(presrciption),
	document: many(document),
	activeBed: many(activeBed),
	activeDepartment: many(activeDepartment),
	accessment: one(accessment),
	appointment: one(appointment),
	adviceTeaching: one(adviceTeaching)
}));
export const nursingProcessRelations = relations(nursingProcess, ({ one }) => ({
	nursingSign: one(staff, {
		fields: [nursingProcess.nursing_sign],
		references: [staff.id]
	}),
	activeDepartment: one(activeDepartment, {
		fields: [nursingProcess.active_department_id],
		references: [activeDepartment.id]
	}),
	progressNote: one(progressNote, {
		fields: [nursingProcess.progress_note_id],
		references: [progressNote.id]
	})
}));
