import { db } from '../db';
import { billing, charge } from '../schemas';
import { logError } from '../utils/telegram';
import { YYYYMMDD_Format } from '../utils';

type TPreBilling = {
	visit_id: number | null;
	patient_id: number;
	url: URL;
	body: FormData;
};
export async function billingOPD({ visit_id, patient_id, url, body }: TPreBilling) {
	const created_at = YYYYMMDD_Format.datetime(new Date());
	await db.transaction(async (tx) => {
		const get_tax = await tx.query.tax.findFirst();
		// doing billing
		const billing_id: { id: number }[] = await tx
			.insert(billing)
			.values({
				created_at: created_at,
				visit_id: visit_id,
				billing_type: 'OPD',
				patient_id: patient_id,
				status: 'checking',
				tax: get_tax?.value || 0,
				amount: 0,
				total: 0
			})
			.$returningId()
			.catch((e) => {
				logError({ url, body, err: e });
				return [];
			});

		await tx
			.insert(charge)
			.values({
				billing_id: billing_id[0].id,
				charge_on: 'general',
				created_at: created_at
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await tx
			.insert(charge)
			.values({
				billing_id: billing_id[0].id,
				charge_on: 'imagerie',
				created_at: created_at
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await tx
			.insert(charge)
			.values({
				billing_id: billing_id[0].id,
				charge_on: 'laboratory',
				created_at: created_at
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await tx
			.insert(charge)
			.values({
				billing_id: billing_id[0].id,
				charge_on: 'prescription',
				created_at: created_at
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await tx
			.insert(charge)
			.values({
				billing_id: billing_id[0].id,
				charge_on: 'service',
				created_at: created_at
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await tx
			.insert(charge)
			.values({
				billing_id: billing_id[0].id,
				charge_on: 'vaccine',
				created_at: created_at
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	});
}
