import type { <PERSON>quest<PERSON><PERSON><PERSON> } from '@sveltejs/kit';
export function createSSE<T>(payload: () => Promise<T>): RequestHandler {
	return async ({ locals, request }) => {
		if (!locals.session || !locals.user) {
			return new Response('Unauthorized', { status: 401 });
		}
		const encoder = new TextEncoder();
		let closed = false;
		let interval: ReturnType<typeof setInterval> | null = null;
		let heartbeat: ReturnType<typeof setInterval> | null = null;
		const cleanup = () => {
			if (closed) return;
			closed = true;
			if (interval) clearInterval(interval);
			if (heartbeat) clearInterval(heartbeat);
		};
		const stream = new ReadableStream<Uint8Array>({
			start(controller) {
				const send = (data: T) => {
					if (closed) return;
					try {
						controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}\n\n`));
					} catch {
						cleanup();
					}
				};
				let lastJSON = '';
				const poll = async () => {
					try {
						const result = await payload();
						const next = JSON.stringify(result);
						if (next !== lastJSON) {
							lastJSON = next;
							send(result);
						}
					} catch (e) {
						// swallow errors to keep stream alive
						console.log(e);
					}
				};
				// initial push
				void poll();
				// periodic polling for changes
				interval = setInterval(poll, 5000);
				// heartbeat to keep proxies from closing idle connections
				heartbeat = setInterval(() => {
					if (closed) return;
					try {
						controller.enqueue(encoder.encode(`: keepalive\n\n`));
					} catch {
						cleanup();
					}
				}, 15000);

				const abort = () => {
					if (closed) return;
					cleanup();
					try {
						controller.close();
					} catch {
						console.log('error closing stream');
					}
				};
				request.signal.addEventListener('abort', abort, { once: true });
			},
			cancel() {
				cleanup();
			}
		});
		return new Response(stream, {
			headers: {
				'Content-Type': 'text/event-stream',
				'Cache-Control': 'no-cache, no-transform',
				Connection: 'keep-alive'
			}
		});
	};
}
