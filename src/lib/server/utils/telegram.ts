import { db } from '../db';
import { DDMMYYYY_Format, YYYYMMDD_Format } from '.';
import { errorHandler, telegram, type TTelegram } from '../schemas';
import { eq, inArray } from 'drizzle-orm';
export async function message(text: string, type: TTelegram) {
	return;
	const get_telegram = await db.query.telegram.findMany({
		where: inArray(telegram.type, [type, 'ALL'])
	});
	for (const e of get_telegram || []) {
		if (e.type === 'ALL') {
			await send(e.telegram_send_id, e.telegram_receive_id, text);
		}
		if (e.type === type) {
			await send(e.telegram_send_id, e.telegram_receive_id, text);
		}
	}
}
function escapeMarkdown(text: string): string {
	// Only escape _, *, and ` for legacy Markdown
	return text.replace(/([_*`])/g, (match) => `\\${match}`);
}

async function send(bot_token: string | null, chat_id: string | null, text: string) {
	try {
		const escapedText = escapeMarkdown(text);
		const req = await fetch(`https://api.telegram.org/bot${bot_token}/sendMessage`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				chat_id: chat_id,
				text: escapedText,
				parse_mode: 'Markdown'
			})
		});
		const res = await req.json();
		if (!res.ok) {
			console.log(res);
		}
	} catch (e) {
		console.log(e);
	}
}

export async function logError({
	url,
	body,
	err
}: {
	url: URL;
	body: FormData;
	err: string | unknown;
}) {
	const get_telegram = await db.query.telegram.findFirst({
		where: eq(telegram.type, 'ERROR'),
		columns: {
			telegram_receive_id: true,
			telegram_send_id: true
		}
	});
	const chat_id = get_telegram?.telegram_receive_id;
	const bot_token = get_telegram?.telegram_send_id;
	if (!chat_id || !bot_token) return;
	const arry_body = Array.from(body).map((e) => e);
	const formatted = arry_body
		.map(([key, value]) => {
			const displayValue =
				value === '' ? '(empty)' : typeof value === 'object' ? JSON.stringify(value) : value;
			return `${key.padEnd(15)}: ${displayValue}`;
		})
		.join('\n');

	await db.insert(errorHandler).values({
		log_datetime: YYYYMMDD_Format.datetime(new Date()),
		url: JSON.stringify(url),
		body: formatted,
		log: JSON.stringify(err)
	});
	const khmer_date = DDMMYYYY_Format(new Date().toISOString(), 'datetime');
	const msg = `${khmer_date}
   URL: ${url.href}
   .......................................
   Body:
   ${formatted}
   .......................................
   Error:
   ${JSON.stringify(err)}`;
	await send(bot_token, chat_id, msg);
}
