import { query, getRequestEvent } from '$app/server';
import { db } from '$lib/server/db';
import { diagnosisICD } from '$lib/server/schemas';
import { and, like } from 'drizzle-orm';
export const getIcd10 = query(async () => {
	const { url } = getRequestEvent();
	const icd_number = url.searchParams.get('icd_number') ?? '';
	const icd_en = url.searchParams.get('icd_en') ?? '';
	const icd_kh = url.searchParams.get('icd_kh') ?? '';
	const icd_fr = url.searchParams.get('icd_fr') ?? '';
	const get_diagnosis_icd = await db.query.diagnosisICD.findMany({
		where: and(
			like(diagnosisICD.diagnosis_en, `%${icd_en}%`),
			like(diagnosisICD.diagnosis_kh, `%${icd_kh}%`),
			like(diagnosisICD.diagnosis_fr, `%${icd_fr}%`),
			like(diagnosisICD.ICD_10, `%${icd_number}%`)
		),
		limit: 500
	});
	return get_diagnosis_icd;
});
