<script lang="ts">
	import { invalidateAll } from '$app/navigation';
	import Form from '$lib/coms-form/Form.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { TDiagnosisICD } from '$lib/type';
	import type { EventHandler } from 'svelte/elements';
	interface Props {
		diagnosis_icd: TDiagnosisICD[];
		modal_name: string;
		value?: string;
	}
	let { diagnosis_icd, modal_name, value = $bindable('') }: Props = $props();
	let timeout: number | NodeJS.Timeout | null = $state(null);
	const handleQ: EventHandler<Event, HTMLInputElement> = ({ currentTarget }) => {
		clearTimeout(timeout!);
		const form = currentTarget?.form;
		if (!form) return;
		timeout = setTimeout(() => {
			form.requestSubmit();
		}, 400);
	};
	function handleText(text: string) {
		if (value.includes(' '.concat(text).concat(','))) {
			value = value.replace(' '.concat(text.concat(',')), '');
		} else {
			value = value.concat(' '.concat(text)).concat(',');
		}
	}
	let diagnosis_icd_id: number | null = $state(null);
	let loading = $state(false);
	const delete_diagnosis = async (id: string | number | null) => {
		store.globalLoading = true;
		const formData = new FormData();
		formData.append('diagnosis_icd_id', id?.toString() ?? '');
		await fetch('?/delete_diagnosis_icd', {
			method: 'POST',
			body: formData
		});
		await invalidateAll();
		loading = false;
		store.globalLoading = false;
	};
	let find_diagnosis_icd = $derived(diagnosis_icd.find((e) => e.id === diagnosis_icd_id));
</script>

<div class="modal fade" id={modal_name}>
	<div class="modal-dialog modal-dialog-scrollabl modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">Diagnosis ICD</h4>
				<button
					id="close_diagnosis"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card">
					<Form
						fnSuccess={() => {
							diagnosis_icd_id = null;
						}}
						fnError={() => {
							loading = false;
						}}
						class="card-header"
						bind:loading
						action="?/create_diagnosis_icd"
						method="post"
					>
						<input type="hidden" name="diagnosis_icd_id" value={diagnosis_icd_id ?? ''} />

						<div class="row g-1">
							<div class="col-6">
								<input
									value={find_diagnosis_icd?.diagnosis_en}
									name="diagnosis_en"
									type="text"
									placeholder="Diagnosis English"
									class="form-control"
								/>
							</div>
							<div class="col-6">
								<input
									value={find_diagnosis_icd?.diagnosis_fr}
									name="diagnosis_fr"
									type="text"
									placeholder="Diagnosis French"
									class="form-control"
								/>
							</div>
							<div class="col-6">
								<input
									value={find_diagnosis_icd?.diagnosis_kh}
									name="diagnosis_kh"
									type="text"
									placeholder="Diagnosis Khmer"
									class="form-control"
								/>
							</div>
							<div class="col-4">
								<input
									value={find_diagnosis_icd?.ICD_10}
									required
									name="icd_10"
									type="text"
									placeholder="ICD 10"
									class="form-control"
								/>
							</div>
							<div class="col-2">
								<button disabled={loading} type="submit" class="btn btn-success w-100">
									{#if diagnosis_icd_id}
										{locale.T('update')}
									{:else}
										{locale.T('add')}
									{/if}
								</button>
							</div>
						</div>
					</Form>
					<div style="height: 700px;" class="card-body table-responsive p-0">
						<table class="table table-hover table-bordered table-light">
							<thead class="sticky-top bg-light table-active">
								<tr>
									<td>
										<HeaderQuery class="row g-1">
											<input
												oninput={handleQ}
												type="search"
												placeholder="ICD N"
												class="form-control"
												name="icd_number"
											/>
										</HeaderQuery>
									</td>
									<td>
										<HeaderQuery class="row g-1">
											<input
												oninput={handleQ}
												type="search"
												placeholder=""
												class="form-control"
												name="icd_en"
											/>
										</HeaderQuery>
									</td>
									<td>
										<HeaderQuery class="row g-1">
											<input
												oninput={handleQ}
												type="search"
												placeholder=""
												class="form-control"
												name="icd_kh"
											/>
										</HeaderQuery>
									</td>
									<td>
										<HeaderQuery class="row g-1">
											<input
												oninput={handleQ}
												type="search"
												placeholder=""
												class="form-control"
												name="icd_fr"
											/>
										</HeaderQuery>
									</td>
									<td></td>
								</tr>
								<tr class="text-center">
									<th style="width: 10%;">ICD 10</th>
									<th style="width: 29%;">Diagnosis English</th>
									<th style="width: 29%;">Diagnosis Khmer</th>
									<th style="width: 20%;">Diagnosis French</th>
									<td style="width: 5%;"></td>
								</tr>
							</thead>
							<tbody>
								{#each diagnosis_icd as item (item.id)}
									<tr class={diagnosis_icd_id === item.id ? 'table-primary' : ''}>
										<td>
											<input
												onclick={() => {
													handleText(item?.ICD_10 ?? '');
												}}
												checked={value.includes(' '.concat(item?.ICD_10 ?? '').concat(','))}
												class="form-check-input"
												type="checkbox"
												id={`km${item.id.toString()}`}
												value={item.ICD_10}
											/>
											<label for={`km${item.id.toString()}`} class="custom-control-label"
												>{item.ICD_10}
											</label>
										</td>
										<td class="text-break">
											<input
												onclick={() => {
													handleText(item?.diagnosis_en ?? '');
												}}
												checked={value.includes(' '.concat(item?.diagnosis_en ?? '').concat(','))}
												class="form-check-input"
												type="checkbox"
												id={`km${item.id.toString()}`}
												value={item.diagnosis_en}
											/>
											<label for={`km${item.id.toString()}`} class="custom-control-label"
												>{item.diagnosis_en}
											</label>
										</td>
										<td class="text-break"
											><input
												onclick={() => {
													handleText(item?.diagnosis_kh ?? '');
												}}
												checked={value.includes(' '.concat(item?.diagnosis_kh ?? '').concat(','))}
												class="form-check-input"
												type="checkbox"
												id={`km${item.id.toString()}`}
												value={item.diagnosis_kh}
											/>
											<label for={`km${item.id.toString()}`} class="custom-control-label"
												>{item.diagnosis_kh}
											</label></td
										>
										<td class="text-break"
											><input
												onclick={() => {
													handleText(item?.diagnosis_fr ?? '');
												}}
												checked={value.includes(' '.concat(item?.diagnosis_fr ?? '').concat(','))}
												class="form-check-input"
												type="checkbox"
												id={`km${item.id.toString()}`}
												value={item.diagnosis_fr}
											/>
											<label for={`km${item.id.toString()}`} class="custom-control-label"
												>{item.diagnosis_fr}
											</label></td
										>
										<td>
											<div>
												<button
													aria-label="wahtis"
													type="button"
													class={diagnosis_icd_id === item.id
														? 'btn btn-link m-0 p-0'
														: 'btn btn-link text-secondary m-0 p-0'}
													onclick={() => {
														diagnosis_icd_id = item.id === diagnosis_icd_id ? null : item.id;
													}}><i class="fa-solid fa-file-pen"></i></button
												>
												{#if diagnosis_icd_id === item.id}
													<button
														onclick={(e) => {
															e.stopPropagation();
															if (confirm(locale.T('confirm_delete'))) {
																delete_diagnosis(item.id);
															}
														}}
														aria-label="submit"
														class="btn btn-link text-danger m-0 p-0"
														type="submit"><i class="fa-solid fa-x"></i></button
													>
												{/if}
											</div>
										</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
