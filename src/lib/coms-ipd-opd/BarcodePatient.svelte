<script lang="ts">
	import QrBrGener from '$lib/coms/QrBrGener.svelte';
	import { khmerDate } from '$lib/helper';
	import { locale } from '$lib/translations/locales.svelte';
	interface Info {
		name_khmer: string;
		name_latin: string;
		gender: string;
		dob: string;
		id: number;
	}
	interface Props {
		info: Info;
	}
	let { info }: Props = $props();
</script>

<div style="width: 50mm;height: 25mm;outline: 2px dotted #999" class=" bg-light p-1">
	<div class="text-center" style="font-size: 9px;padding: 0px;">
		{info?.name_khmer ?? ''}
		{`(${info?.name_latin ?? ''})`}
	</div>
	<div class="text-center" style="font-size: 9px;padding: 0px;">
		{locale.T('gender')}
		{info?.gender?.toLowerCase() === 'male' ? locale.T('male') : locale.T('female')}
		DOB {khmerDate(info?.dob, 'date')}
	</div>
	<div
		style="width: auto;"
		class="text-center
		"
	>
		<QrBrGener height={19} width={1} value={info?.id?.toString() ?? ''} type="barcode" />
	</div>
	<div class="text-center" style="font-size: 9px;padding-top: 1px;">
		{locale.T('id')}
		{info?.id ?? ''}
	</div>
</div>
