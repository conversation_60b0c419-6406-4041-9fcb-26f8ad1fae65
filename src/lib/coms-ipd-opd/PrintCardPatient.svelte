<script lang="ts">
	import QrBrGener from '$lib/coms/QrBrGener.svelte';
	import { khmerDate } from '$lib/helper';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from '../../routes/(dash)/patient/histrory/$types';
	interface Props {
		get_clinic_info: PageServerData['get_clinic_info'];
		patient_info: PageServerData['get_patient'];
	}
	let { get_clinic_info, patient_info }: Props = $props();
	let uid = $derived(`id`.concat(patient_info?.id?.toString() ?? ''));
</script>

<div id={uid} style="width: 326px;height: 204px" class="card bg-light p-2">
	<div class="row g-0">
		<div class="col-2 text-start">
			<img style="min-height: 13mm;" class="img-fluid" src={get_clinic_info?.logo0} alt="" />
		</div>
		<div class="col-8 text-center">
			<div class="kh_font_muol_light" style="font-size: 9px;">{get_clinic_info?.title_khm}</div>
			<div class="en_font_times_new_roman" style="font-size: 9px;">
				{get_clinic_info?.title_eng}
			</div>
		</div>
		<div class="col-2 text-end">
			<div style="width: 50px;">
				<QrBrGener value={patient_info?.id?.toString() ?? ''} type="qrcode" />
				<!-- <GenQRcode data={{ text: patient_info?.id?.toString() ?? '' }} /> -->
			</div>
		</div>
	</div>
	<hr class="m-0 my-2" />
	<div
		style="font-size: 12px;"
		class="text-center text-decoration-underline mt-0 mb-1 kh_font_muol_light"
	>
		ប័ណ្ណពិនិត្យសុខភាព
	</div>
	<div class="row g-2">
		<div class="col-2">
			<div>
				<img
					style="max-height: 15mm;"
					class="img-fluid"
					src={patient_info.uploads?.filename || '/no-user.png'}
					alt=""
				/>
			</div>
		</div>
		<div class="col-9 ms-2">
			<div style="font-size: 9px;" class="row g-0">
				<div class="col-3">{locale.T('id')}</div>
				<div class="col">: PT{patient_info?.id}</div>
			</div>
			<div style="font-size: 9px;" class="row g-0">
				<div class="col-3">{locale.T('patient_name')}</div>
				<div class="col">: {patient_info?.name_khmer} {`(${patient_info?.name_latin})`}</div>
			</div>
			<div style="font-size: 9px;" class="row g-0">
				<div class="col-3">{locale.T('gender')}</div>
				<div class="col">
					: {patient_info?.gender?.toLowerCase() === 'male' ? locale.T('male') : locale.T('female')}
				</div>
			</div>
			<div style="font-size: 9px;" class="row g-0">
				<div class="col-3">{locale.T('dob')}</div>
				<div class="col">: {khmerDate(patient_info?.dob, 'date')}</div>
			</div>
			<div style="font-size: 9px;" class="row g-0">
				<div class="col-3">{locale.T('address')}</div>
				<div class="col">
					: {patient_info?.village?.type}
					{patient_info?.village?.name_khmer}
					{patient_info?.commune?.type}
					{patient_info?.commune?.name_khmer}
					{patient_info?.district?.type}
					{patient_info?.district?.name_khmer}
					{patient_info?.provice?.type}
					{patient_info?.provice?.name_khmer}
				</div>
			</div>
		</div>
	</div>
</div>
