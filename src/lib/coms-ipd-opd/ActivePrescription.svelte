<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import DDMMYYYYFormat from '../coms/DDMMYYYYFormat.svelte';
	import type { ActivePresrciption, TStaff, User } from '$lib/type';
	interface IUser extends User {
		staff: TStaff | null;
	}
	interface IActivePrescription extends ActivePresrciption {
		user: IUser | null;
	}
	interface Props {
		active_for: string;
		activePresrciption: IActivePrescription[];
		prescription_id: number;
		children?: import('svelte').Snippet;
	}

	let { active_for, activePresrciption, prescription_id, children }: Props = $props();

	let datetime = $derived(activePresrciption.at(-1)?.datetime);
</script>

<Form class="col-auto mt-2" action="?/active_prescription" method="post">
	<input type="hidden" value={prescription_id} name="prescription_id" />
	<input type="hidden" value={active_for} name="active_for" />
	{#if activePresrciption.some((e) => e.active_for === active_for)}
		<button type="button" class="btn btn-sm btn-primary py-0">
			{@render children?.()}
		</button>
		<button formaction="?/remove" type="submit" class="btn btn-sm btn-primary py-0">-</button>
		<button type="button" class="btn btn-sm btn-info py-0">
			{activePresrciption.filter((e) => e.active_for === active_for).length} times
		</button>
		<button formaction="?/add" type="submit" class="btn btn-sm btn-primary py-0">+</button>
		<br />
		<DDMMYYYYFormat date={datetime} />
		{activePresrciption.find((e) => e.active_for === active_for)?.user?.staff?.name_khmer || ''}
	{:else}
		<button type="submit" class="btn btn-sm btn-warning py-0">
			{@render children?.()}
		</button>
	{/if}
</Form>
