<script lang="ts">
	import type { PageServerData } from '../../routes/(dash)/ipd/[progress_note_id]/progress-note/$types';
	import ActivePrescription from '$lib/coms-ipd-opd/ActivePrescription.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import SendBillingToPayment from '$lib/coms-billing/SendBillingToPayment.svelte';
	import FieldsetBilling from '$lib/coms/FieldsetBilling.svelte';
	type V = PageServerData['get_progress_note']['visit'];
	type VV = NonNullable<V>[number];
	type C = PageServerData['get_currency'];
	interface Props {
		find_old_visit: VV;
		get_currency: C;
	}

	let { find_old_visit, get_currency }: Props = $props();
	let total_laboratory = $derived(
		find_old_visit?.billing?.charge.find((e) => e.charge_on === 'laboratory')?.price || 0
	);
	let total_imagerie = $derived(
		find_old_visit?.billing?.charge.find((e) => e.charge_on === 'imagerie')?.price || 0
	);
	let total_prescription = $derived(
		find_old_visit?.billing?.charge.find((e) => e.charge_on === 'prescription')?.price || 0
	);
	let total_service = $derived(
		find_old_visit?.billing?.charge.find((e) => e.charge_on === 'service')?.price || 0
	);
	let total_bed = $derived(
		find_old_visit?.billing?.charge.find((e) => e.charge_on === 'bed')?.price || 0
	);
	let total_vaccine = $derived(
		find_old_visit?.billing?.charge.find((e) => e.charge_on === 'vaccine')?.price || 0
	);
	let productOrders = $derived(
		find_old_visit?.billing?.charge.find((e) => e.charge_on === 'prescription')?.productOrder
	);
	let all_total = $derived(
		Number(total_bed) +
			Number(total_laboratory) +
			Number(total_prescription) +
			Number(total_service) +
			Number(total_vaccine) +
			Number(total_imagerie)
	);
</script>

<div>
	<FieldsetBilling status={find_old_visit?.billing?.status}>
		{#each find_old_visit?.presrciption || [] as item, index (item?.id)}
			{@const productOrder = productOrders?.find((e) => e.product_id === item?.product_id)}
			<div class="border rounded border-1 p-2 mb-2">
				<span class="fs-6 text-decoration-underline text-primary"
					>{index + 1}
					{item?.product?.products ?? ''}
					{item?.product?.generic_name ? `, (  ${item?.product?.generic_name ?? ''} )` : ''}
				</span><br />
				<span>
					{locale.T('qty')}
					{item?.amount ?? ''}
					{item?.unit?.unit}, {locale.T('duration')}
					{item?.duration ?? ''}
				</span>
				{#if productOrder?.qty}
					<span class="text-danger">
						{locale.T('amount_use')}
						{productOrder?.qty}
						{item?.unit?.unit}
					</span>
				{/if}
				<table class="table table-light">
					<thead>
						<tr>
							<td style="width: 20%;">
								<span>{item?.use ?? ''}</span>
							</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;">
								<div class="col">
									{#if item?.morning !== 0}
										<ActivePrescription
											activePresrciption={item?.activePresrciption.filter(
												(e) => e.active_for === 'morning'
											)}
											active_for="morning"
											prescription_id={item?.id}
										>
											{locale.T('morning')}
											{item?.morning}
											{item?.unit?.unit}
											<input type="hidden" value={item?.morning ?? 1} name="qty" />
										</ActivePrescription>
									{/if}
									{#if item?.noon !== 0}
										<ActivePrescription
											activePresrciption={item?.activePresrciption.filter(
												(e) => e.active_for === 'noon'
											)}
											active_for="noon"
											prescription_id={item?.id}
										>
											{locale.T('noon')}
											{item?.noon}
											{item?.unit?.unit}
											<input type="hidden" value={item?.noon ?? 1} name="qty" />
										</ActivePrescription>
									{/if}
									{#if item?.afternoon !== 0}
										<ActivePrescription
											activePresrciption={item?.activePresrciption.filter(
												(e) => e.active_for === 'afternoon'
											)}
											active_for="afternoon"
											prescription_id={item?.id}
										>
											{locale.T('afternoon')}
											{item?.afternoon}
											<input type="hidden" value={item?.afternoon ?? 1} name="qty" />
											{item?.unit?.unit}
										</ActivePrescription>
									{/if}
									{#if item?.evening !== 0}
										<ActivePrescription
											activePresrciption={item?.activePresrciption.filter(
												(e) => e.active_for === 'evening'
											)}
											active_for="evening"
											prescription_id={item?.id}
										>
											{locale.T('evening')}
											{item?.evening}
											<input type="hidden" value={item?.evening ?? 1} name="qty" />

											{item?.unit?.unit}
										</ActivePrescription>
									{/if}
									{#if item?.night !== 0}
										<ActivePrescription
											activePresrciption={item?.activePresrciption.filter(
												(e) => e.active_for === 'night'
											)}
											active_for="night"
											prescription_id={item?.id}
										>
											{locale.T('night')}
											{item?.night}
											<input type="hidden" value={item?.night ?? 1} name="qty" />

											{item?.unit?.unit}
										</ActivePrescription>
									{/if}
								</div>
							</td>
						</tr>
						<tr class="table-danger">
							<td colspan="2">#{locale.T('advice_or_teaching')}</td>
							<td colspan="1">
								<div class="text-break">
									: {find_old_visit.adviceTeaching?.description ?? ''}
								</div>
							</td>
						</tr>
					</thead>
				</table>
			</div>
		{/each}
	</FieldsetBilling>

	{#if total_imagerie || total_laboratory || total_prescription || total_bed || total_vaccine || total_service}
		<div class="card bg-light">
			<div class="card-header">
				<div class="row">
					<div class="col">
						<span class="fs-5">{locale.T('invoice_daily')}</span>
					</div>
					<div class="col">
						<SendBillingToPayment status={find_old_visit?.billing?.status}>
							<ul class="list-group pb-2">
								<li class="list-group-item text-danger">
									<i class="fa-solid fa-triangle-exclamation"></i>
									{locale.T('please_verify_before_sending')}
								</li>
							</ul>
							<input type="hidden" name="visit_id" value={find_old_visit?.id} />
							<input type="hidden" name="id" value={find_old_visit?.billing?.id} />
							<ul class="list-group">
								<li class="list-group-item">
									<i class="fa-solid fa-comments-dollar"></i>
									{locale.T('daily_payment')}
									{locale.T('price')}
									<Currency class="" amount={all_total} symbol={get_currency?.currency} />
								</li>
							</ul>
						</SendBillingToPayment>
					</div>
				</div>
			</div>
			<div class="card-body">
				<table class="table table-light">
					<thead>
						{#if total_laboratory}
							<tr>
								<td>{locale.T('laboratory')} </td>
								<td>:</td>
								<td>
									<Currency amount={total_laboratory} symbol={get_currency?.currency} />
								</td>
							</tr>
						{/if}
						{#if total_imagerie}
							<tr>
								<td>{locale.T('imagerie')} </td>
								<td>:</td>
								<td>
									<Currency amount={total_imagerie} symbol={get_currency?.currency} />
								</td>
							</tr>
						{/if}
						{#if total_prescription}
							<tr>
								<td>{locale.T('treatment')} </td>
								<td>:</td>
								<td>
									<Currency amount={total_prescription} symbol={get_currency?.currency} />
								</td>
							</tr>
						{/if}
						{#if total_service}
							<tr>
								<td>{locale.T('service')} </td>
								<td>:</td>
								<td>
									<Currency amount={total_service} symbol={get_currency?.currency} />
								</td>
							</tr>
						{/if}
						{#if total_vaccine}
							<tr>
								<td>{locale.T('vaccine')} </td>
								<td>:</td>
								<td>
									<Currency amount={total_vaccine} symbol={get_currency?.currency} />
								</td>
							</tr>
						{/if}
						{#if total_bed}
							<tr>
								<td>{locale.T('bed')} </td>
								<td>:</td>
								<td>
									<Currency amount={total_bed} symbol={get_currency?.currency} />
								</td>
							</tr>
						{/if}
						<tr class="border text-bg-light">
							<td>{locale.T('total_daily')} </td>
							<td>:</td>
							<td>
								<Currency
									amount={+total_laboratory +
										+total_imagerie +
										+total_prescription +
										+total_service +
										+total_bed +
										+total_vaccine}
									symbol={get_currency?.currency}
								/>
							</td>
						</tr>
					</thead>
				</table>
			</div>
		</div>
	{/if}
</div>
