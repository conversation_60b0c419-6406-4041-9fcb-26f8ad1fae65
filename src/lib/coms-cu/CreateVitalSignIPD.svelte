<script lang="ts">
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import { DDMMYYYY_Format, YYYYMMDD_Format } from '$lib/helper';
	import type { TVitalSign, TStaff } from '$lib/type';
	type TVsi = Omit<TVitalSign, 'by'> & { by: TStaff | null };
	interface Props {
		vital_sign: TVsi | null;
	}
	let { vital_sign }: Props = $props();
	let loading = $state(false);
</script>

<!-- @_Add_vital sign -->
<div class="modal fade" id="create_vital_sign_ipd" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<Form
			action="?/create_vital_sign_ipd"
			method="post"
			class="modal-content"
			reset={true}
			bind:loading
			fnSuccess={() => document.getElementById('close_vital_sign')?.click()}
		>
			<input type="hidden" value={vital_sign?.id || ''} name="vital_sign_id" />
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('vital_sign')}</h4>
				<button
					id="close_vital_sign"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card-body pt-0">
					{#if vital_sign?.id}
						<input value={vital_sign?.datetime || ''} type="hidden" name="date" />
						<div class="row pb-2">
							<div class="col">
								<label for="date">Date</label>
								<button type="button" class="form-control"
									>{DDMMYYYY_Format(vital_sign?.datetime, 'date')}</button
								>
							</div>
							<div class="col">
								<label for="time">Time</label>
								<input
									id="time"
									value={YYYYMMDD_Format.time(vital_sign?.datetime).slice(0, 5)}
									name="time"
									type="time"
									class="form-control"
								/>
							</div>
						</div>
					{/if}
					<div class="row pb-2">
						<div class="col">
							<label for="bp">BP (mmHg)</label>
							<input
								value={vital_sign?.sbp?.toFixed(0) ?? ''}
								id="sbp"
								placeholder="Systolic"
								name="sbp"
								type="number"
								step="any"
								class="form-control"
							/>
						</div>
						<div class="col">
							<label for="bp">&nbsp;</label>
							<input
								value={vital_sign?.dbp?.toFixed(0) ?? ''}
								id="dbp"
								placeholder="Diastolic"
								name="dbp"
								type="number"
								step="any"
								class="form-control"
							/>
						</div>
					</div>
					<div class="pb-2 row">
						<div class="col">
							<label for="pulse">Pulse (min)</label>
							<input
								value={vital_sign?.pulse?.toFixed(0) ?? ''}
								id="pulse"
								name="pulse"
								type="number"
								step="any"
								class="form-control"
							/>
						</div>
						<div class="col">
							<label for="t">T (C)</label>
							<input
								value={vital_sign?.t?.toFixed(1) ?? ''}
								id="t"
								name="t"
								type="number"
								step="any"
								class="form-control"
							/>
						</div>
					</div>
					<div class="pb-2 row">
						<div class="col">
							<label for="rr">RR (min)</label>
							<input
								value={vital_sign?.rr?.toFixed(0) ?? ''}
								id="rr"
								name="rr"
								type="number"
								step="any"
								class="form-control"
							/>
						</div>
						<div class="col">
							<label for="sp02">SpO2 (%)</label>
							<input
								value={vital_sign?.sp02?.toFixed(0) ?? ''}
								id="sp02"
								name="sp02"
								type="number"
								step="any"
								class="form-control"
							/>
						</div>
					</div>
					<div class=" row pb-2">
						<div class="col">
							<label for="weight">Weight (kg)</label>
							<input
								value={vital_sign?.weight?.toFixed(0) ?? ''}
								id="weight"
								name="weight"
								type="number"
								step="any"
								class="form-control"
							/>
						</div>
					</div>
					<div class="row pt-2">
						<div class="col-6">
							<div class="card bg-success-subtle">
								<div class="card-body">
									<div class="  pb-2">
										<label for="piv">Piv</label>
										<input
											value={vital_sign?.piv ?? ''}
											id="piv"
											name="piv"
											type="number"
											step="any"
											class="form-control"
										/>
									</div>
									<div class="pb-2">
										<label for="drink">Drink</label>
										<input
											value={vital_sign?.drink ?? ''}
											id="drink"
											name="drink"
											type="number"
											step="any"
											class="form-control"
										/>
									</div>
									<div class="pb-2">
										<label for="nasogastric_tube_in">NG in</label>
										<input
											value={vital_sign?.nasogastric_tube_in ?? ''}
											id="nasogastric_tube_in"
											name="nasogastric_tube_in"
											class="form-control"
											type="number"
											step="any"
										/>
									</div>
									<div class="pb-2">
										<label for="note">Note</label>
										<textarea
											value={vital_sign?.note ?? ''}
											id="note"
											name="note"
											class="form-control"
											rows="4"
										></textarea>
									</div>
								</div>
							</div>
						</div>
						<div class="col-6">
							<div class="card bg-success-subtle">
								<div class="card-body">
									<div class="pb-2">
										<label for="nasogastric_tube_out">NG out</label>
										<input
											value={vital_sign?.nasogastric_tube_out ?? ''}
											id="nasogastric_tube_out"
											name="nasogastric_tube_out"
											class="form-control"
											type="number"
											step="any"
										/>
									</div>
									<div class="pb-2">
										<label for="fluid_out">Fluid out</label>
										<input
											value={vital_sign?.fluid_out ?? ''}
											id="fluid_out"
											name="fluid_out"
											class="form-control"
											type="number"
											step="any"
										/>
									</div>
									<div class="pb-2">
										<label for="vomiting">Vomiting</label>

										<input
											value={vital_sign?.vomiting ?? ''}
											id="vomiting"
											name="vomiting"
											type="number"
											step="any"
											class="form-control"
										/>
									</div>
									<div class="pb-2">
										<label for="stool">Stool</label>
										<input
											value={vital_sign?.stool ?? ''}
											id="stool"
											name="stool"
											type="number"
											step="any"
											class="form-control"
										/>
									</div>
									<div class="pb-2">
										<label for="urine">Urine</label>
										<input
											value={vital_sign?.urine ?? ''}
											id="urine"
											name="urine"
											type="number"
											step="any"
											class="form-control"
										/>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>
