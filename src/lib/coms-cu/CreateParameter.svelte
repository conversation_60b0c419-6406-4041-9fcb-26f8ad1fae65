<script lang="ts">
	import type {
		ActionData,
		PageServerData
	} from '../../routes/(dash)/settup/parameter/group/$types';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	type Data = Pick<PageServerData, 'get_para_units'>;
	interface Props {
		form: ActionData;
		data: Data;
		product_id: number | undefined;
	}

	let { form, data, product_id = $bindable() }: Props = $props();
	let loading = $state(false);
	let { get_para_units } = $derived(data);
</script>

<!-- @_Add_Parameter -->
<div class="modal fade" id="create_parameter" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<Form
			enctype="multipart/form-data"
			action="?/create_parameter"
			method="post"
			bind:loading
			class="modal-content"
			fnSuccess={() => {
				product_id = undefined;
				document.getElementById('close-parameter')?.click();
			}}
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('parameter')}</h4>
				<button
					onclick={() => (product_id = undefined)}
					id="close-parameter"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card-body pt-0">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<input type="hidden" name="product_id" value={product_id} />
								<label for="parameter">{locale.T('parameter')}</label>
								<input name="parameter_" type="text" class="form-control" id="parameter" />
								{#if form?.parameter_}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="description">{locale.T('description')}</label>
								<textarea rows="3" class="form-control" name="description" id="description"
								></textarea>

								{#if form?.description}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
						<div class="col-12">
							<div class="row">
								<div class="col-6">
									<label for="unit">{locale.T('unit')}</label>

									<div class="input-group">
										<SelectParam
											height="200"
											name="unit_id"
											items={get_para_units.map((e) => ({ id: e.id, name: e.unit }))}
										/>
										<a
											aria-label="linkandcloseparamter"
											href="/settup/para-unit"
											type="button"
											class=" btn btn-default ml-1"
											onclick={() => document.getElementById('close-parameter')?.click()}
											><i class="fas fa-share-square"></i>
										</a>
									</div>

									{#if form?.para_unit_id}
										<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
									{/if}
								</div>
								<div class="col-6">
									<div class=" pb-3">
										<label for="gender">{locale.T('gender')}</label>
										<select name="gender" class="form-control" id="gender">
											<option value="Other">{locale.T('none')}</option>
											<option value="Male">{locale.T('male')}</option>
											<option value="Female">{locale.T('female')}</option>
										</select>
										{#if form?.gender}
											<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
										{/if}
									</div>
								</div>
							</div>
						</div>

						<div class="col-12">
							<div class="row">
								<div class="col-5">
									<div class=" text-center">
										<label for="mini">{locale.T('mini')}</label>
										<input name="mini" type="number" step="0.01" class="form-control" id="mini" />
										{#if form?.mini}
											<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
										{/if}
									</div>
								</div>
								<div class="col-2">
									<div class=" text-center">
										<label for="sign">{locale.T('sign')}</label>
										<select class="form-control" name="sign" id="sign">
											<option value=""></option>
											<option value="-">-</option>
											<option value=">">&gt;</option>
											<option value="<">&lt;</option>
										</select>
									</div>
								</div>
								<div class="col-5">
									<div class=" text-center">
										<label for="Maxi">{locale.T('maxi')}</label>
										<input name="maxi" type="number" step="0.01" class="form-control" id="Maxi" />
										{#if form?.maxi}
											<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
										{/if}
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>
