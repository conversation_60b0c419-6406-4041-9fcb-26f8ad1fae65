import * as schemas from '$lib/server/schemas';
export { type User } from '$lib/server/schemas/auth';
export type { related_type } from '$lib/server/schemas/uploads';
export { type Presrciption as TPrescription } from '$lib/server/schemas/presrciption';
export { type ActivePresrciption } from '$lib/server/schemas/presrciption';
export type { TBillingStatus } from '$lib/server/schemas';
export { type TCurrency } from '$lib/server/schemas/setting';
export type { TTelegram } from '$lib/server/schemas/setting';
export type TActiveBed = typeof schemas.activeBed.$inferSelect;
export type PaymentService = typeof schemas.paymentService.$inferSelect;
export type Words = typeof schemas.words.$inferSelect;
export type TStaff = typeof schemas.staff.$inferSelect;
export type TVitalSign = typeof schemas.vitalSign.$inferSelect;
export type TDocument = typeof schemas.document.$inferSelect;
export type TFields = typeof schemas.fields.$inferSelect;
export type TFile = typeof schemas.uploads.$inferSelect;
export type TDiagnosisICD = typeof schemas.diagnosisICD.$inferSelect;
export type TClinicInfo = typeof schemas.clinicinfo.$inferSelect;
export type TDocumentSetting = typeof schemas.documentSetting.$inferSelect;
export type TPatient = typeof schemas.patient.$inferSelect;
export type TTitle = typeof schemas.title.$inferSelect;
export type TProduct = typeof schemas.product.$inferSelect;
export type TSubUnit = typeof schemas.subUnit.$inferSelect;
export type TUnit = typeof schemas.unit.$inferSelect;

type TVillage = typeof schemas.village.$inferSelect;
type TCommune = typeof schemas.commune.$inferSelect;
type TDistrict = typeof schemas.district.$inferSelect;
type TProvice = typeof schemas.provice.$inferSelect;
export type TAddress = {
	village: TVillage | null | undefined;
	commune: TCommune | null | undefined;
	district: TDistrict | null | undefined;
	provice: TProvice | null | undefined;
};

export interface TPatientFull extends TPatient {
	village: TVillage | null | undefined;
	commune: TCommune | null | undefined;
	district: TDistrict | null | undefined;
	provice: TProvice | null | undefined;
	uploads: TFile | null | undefined;
}
