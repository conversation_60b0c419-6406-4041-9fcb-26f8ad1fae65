<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import { page } from '$app/state';
	import QrBrGener from '$lib/coms/QrBrGener.svelte';
	import type { related_type, TFile } from '$lib/type';
	import Confirm from '$lib/coms/Confirm.svelte';
	import { invalidateAll } from '$app/navigation';
	import { ShapeCropper } from '$lib/helper/cropper';
	let sid = $state(`id${Math.random().toString(36).substring(2, 9)}`);
	let fileInput: HTMLInputElement = $state() as HTMLInputElement;
	let originalFileName: string | null = $state(null);
	let {
		name,
		submit = false,
		default_image,
		related_type_,
		related_id,
		label,
		aspect_ratio = false
	}: {
		name: string;
		submit?: boolean;
		label?: string;
		default_image?: string | null;
		related_type_?: related_type;
		related_id?: string | number;
		aspect_ratio?: boolean;
	} = $props();
	let loading = $state(false);

	async function createQrUploads() {
		loadingQr = true;
		const formData = new FormData();
		formData.append('related_id', (related_id as string) ?? '');
		formData.append('related_type', (related_type_ as string) ?? '');
		formData.append('old_filename', default_image ?? '');
		const response = await fetch('/api/upload', {
			method: 'POST',
			body: formData
		});
		const result: {
			status: number;
			message: string;
			uuid: string;
			related_id: string;
			related_type: string;
		} = await response.json();
		uploads = {
			related_id: result.related_id,
			related_type: result.related_type,
			uuid: result.uuid
		};
		message = result.message;
		// reset SSE UI state
		loadingQr = false;
	}
	let loadingQr = $state(false);
	let uploads = $state({ related_id: '', related_type: '', uuid: '' });
	let message = $state('');
	let canvas = $state<HTMLCanvasElement>() as HTMLCanvasElement;
	let cropper = $state<ShapeCropper>() as ShapeCropper;
	let errorMessage: string | null = $state(null);
	let isLoading = $state(false);
	async function handleFileUpload(e: Event) {
		const files = (e.target as HTMLInputElement)?.files;
		if (!files || files.length === 0) return;
		document.getElementById(sid.concat('modal_crop'))?.click();
		try {
			isLoading = true;
			errorMessage = null;
			originalFileName = files[0].name || null;
			await cropper.loadImage(files[0]);
		} catch (err) {
			errorMessage = err instanceof Error ? err.message : 'Failed to load image';
		} finally {
			isLoading = false;
		}
	}

	async function cropImage() {
		try {
			if (!cropper) throw new Error('Cropper not initialized');
			const croppedCanvas = cropper.cropImage();

			// Optional: downscale very large crops to reduce file size further
			const getScaledCanvas = (source: HTMLCanvasElement, maxSize = 1600): HTMLCanvasElement => {
				if (source.width <= maxSize && source.height <= maxSize) return source;
				const scale = Math.min(maxSize / source.width, maxSize / source.height);
				const target = document.createElement('canvas');
				target.width = Math.max(1, Math.round(source.width * scale));
				target.height = Math.max(1, Math.round(source.height * scale));
				const ctx = target.getContext('2d');
				if (!ctx) throw new Error('Failed to get 2D context');
				ctx.imageSmoothingEnabled = true;
				ctx.imageSmoothingQuality = 'high';
				ctx.drawImage(source, 0, 0, target.width, target.height);
				return target;
			};

			const processedCanvas = getScaledCanvas(croppedCanvas, 1600);

			// Prefer JPEG for photos to keep size small
			let outType = 'image/jpeg';
			const quality = 0.85; // adjust 0.7-0.9 as needed

			// Derive a friendly filename with correct extension
			const baseName = (originalFileName || 'cropped').replace(/\.[^.]+$/, '');
			const outExt = outType === 'image/jpeg' ? 'jpg' : 'png';
			const outName = `${baseName}.${outExt}`;

			// Convert canvas to File using toBlob (avoids base64 bloat)
			const file: File = await new Promise((resolve, reject) => {
				processedCanvas.toBlob(
					(blob) => {
						if (!blob) return reject(new Error('Failed to generate image blob'));
						resolve(new File([blob], outName, { type: outType }));
					},
					outType,
					quality
				);
			});

			// Preview via object URL and set the input's FileList
			const blobUrl = URL.createObjectURL(file);
			default_image = blobUrl;

			const dataTransfer = new DataTransfer();
			dataTransfer.items.add(file);
			if (fileInput) fileInput.files = dataTransfer.files;
			errorMessage = null;
		} catch (err) {
			errorMessage = err instanceof Error ? err.message : 'Cropping failed';
		}
	}
	let qr_img = $state(false);
	function reset() {
		cropper?.reset();
		errorMessage = null;
		fileInput.value = '';
	}

	$effect(() => {
		cropper = new ShapeCropper(canvas);
		return () => {
			cropper.destroy();
		};
	});
	$effect(() => {
		if (aspect_ratio) {
			cropper.setAspectRatio(null, true);
		} else {
			cropper.setAspectRatio(1, true);
		}
	});

	// Subscribe to server-sent events for real-time counts
	$effect(() => {
		if (typeof window === 'undefined') return;
		const es = new EventSource(
			`/api/stream/upload?related_id=${encodeURIComponent(String(related_id ?? ''))}&related_type=${encodeURIComponent(String(related_type_ ?? ''))}&old_filename=${encodeURIComponent(String(default_image ?? ''))}`
		);
		es.onmessage = (e) => {
			try {
				const payload: { upload: TFile | null } = JSON.parse(e.data);
				const u = payload.upload;
				// Only react when a QR session (uuid) is active
				if (!uploads.uuid) return;
				// Ensure we only switch when a new file arrives
				if (!u?.filename) return;
				if (default_image === u.filename) return;
				default_image = u.filename;
				// After receiving new data from SSE, switch to result view instead of QR code
				qr_img = false;
			} catch (err) {
				console.log(err);
				// ignore malformed messages
			}
		};
		return () => es.close();
	});
</script>

<Confirm
	method="DELETE"
	action="/api/upload"
	body={[{ name: 'filename', value: default_image ?? '' }]}
	modal_id={'delete_modal'.concat(sid)}
/>

<div class="input-group">
	{#if related_id}
		<button
			style="max-width: fit-content;"
			data-bs-toggle="modal"
			data-bs-target={'#'.concat(sid.concat('qr_uploads'))}
			aria-label="qr-gen"
			type="button"
			class="text-primary form-control bg-light"
		>
			<i class="fa-solid fa-camera-rotate"></i>
		</button>
	{/if}
	<!-- // Modal qr  -->
	<div
		class="modal fade"
		id={sid.concat('qr_uploads')}
		data-bs-keyboard="false"
		tabindex="-1"
		aria-hidden="true"
		aria-labelledby="staticBackdropLabel"
	>
		<div class="modal-dialog">
			<div
				style="width:295px;{qr_img ? 'height: 390px;' : ''}"
				class="modal-content rounded-3 shadow"
			>
				<!-- <div class="modal-header py-2 justify-content-center text-bg-warning">
					<span class="fs-5">
						<i class="fa-solid fa-mobile-screen-button fa-bounce"></i>
						ស្កែន QR ដើម្បីបញ្ជូលរូបភាព
					</span>
				</div> -->
				<div class="modal-body">
					<div class="row g-1 pb-2">
						<div class="col-6">
							<button
								onclick={() => {
									qr_img = false;
								}}
								class:text-primary={!qr_img}
								aria-label="btn-image"
								type="button"
								class="form-control"
							>
								<i class="fa-regular fa-images"></i>
							</button>
						</div>
						<div class="col-6">
							<button
								onclick={() => {
									createQrUploads();
									qr_img = true;
								}}
								class:text-primary={qr_img}
								aria-label="btn-qr"
								type="button"
								class="form-control"
							>
								<i class="fa-solid fa-qrcode"></i>
							</button>
						</div>
					</div>
					{#if qr_img}
						<div class="img-thumbnail">
							{#if loadingQr}
								<div class="spinner-overlay">
									<i class="fa-solid fa-spinner fa-spin fa-4x"></i>
								</div>
							{:else if message === 'success'}
								<QrBrGener
									type="qrcode"
									value={page.url.origin.concat(
										`/fileupload?uuid=${uploads.uuid}&related_id=${uploads.related_id}&related_type=${uploads.related_type}`
									)}
								/>
							{:else}
								<h4 class="text-center text-danger" style=" height: 245px;padding-top: 95px;">
									សូមព្យាយាមម្ដងទៀត!
								</h4>
							{/if}
						</div>
						<button type="button" class="form-control text-warning mt-2">
							<strong>ស្កែន QR ដើម្បីបញ្ជូលរូបភាព</strong>
						</button>
					{/if}
					{#if !qr_img}
						<img src={default_image} class="img-thumbnail" alt="" />
						<button
							data-bs-target={`#delete_modal${sid}`}
							data-bs-toggle="modal"
							disabled={loading}
							type="button"
							class="form-control text-danger mt-2"
						>
							<strong>{locale.T('delete_')}</strong>
						</button>
					{/if}
				</div>
			</div>
		</div>
	</div>
	<label for="" class="input-group-text">
		{#if label}
			{label}
		{:else}
			{locale.T('select')}
		{/if}
	</label>
	<button
		onclick={() => {
			document.getElementById(sid.concat('img_input'))?.click();
		}}
		aria-label="selectimage"
		class="form-control text-start"
		type="button"
	>
		{originalFileName}
	</button>
</div>

<!-- Button trigger modal -->
<!-- Modal crope image -->
<button
	aria-label="selectimage"
	class="d-none"
	type="button"
	data-bs-toggle="modal"
	data-bs-target={`#${sid}`}
	id={sid.concat('modal_crop')}
>
</button>
<div
	class="modal"
	id={sid}
	data-bs-keyboard="false"
	tabindex="-1"
	aria-hidden="true"
	aria-labelledby="staticBackdropLabel_1"
>
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-body">
				{#if isLoading}
					<div class="spinner-overlay">
						<i class="fa-solid fa-spinner fa-spin fa-4x"></i>
					</div>
					<br />
				{:else if errorMessage}
					<div class="error">{errorMessage}</div>
				{/if}
				<!-- svelte-ignore element_invalid_self_closing_tag -->
				<canvas
					style="height: auto;"
					id={sid.concat('canvas')}
					bind:this={canvas}
					class="img-thumbnail text-center"
				/>
				<div class="row">
					<div class="col-6">
						<button
							id={sid.concat('cropBtn')}
							aria-label="cropperimage"
							onclick={(e) => {
								cropImage();
								if (submit) {
									e.currentTarget.form?.requestSubmit();
								}
							}}
							disabled={loading}
							type="button"
							class:btn-light={loading}
							class="btn btn-primary w-100"
							data-bs-dismiss="modal"
						>
							{#if loading}
								<i class="fa-solid fa-spinner fa-spin"></i>
							{:else}
								<i class="fa-solid fa-scissors">...</i>
							{/if}
						</button>
					</div>
					<div class="col-6">
						<button
							aria-label="resetcropper"
							class="btn btn-warning w-100"
							type="button"
							onclick={reset}><i class="fa-solid fa-repeat"></i></button
						>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Button view image  -->
<button
	onclick={() => invalidateAll()}
	type="button"
	class="btn btn-primary d-none"
	data-bs-toggle="modal"
	data-bs-target={'#'.concat(sid.concat('view_img'))}
	id={sid.concat('btn_view_img')}
>
	Launch static backdrop modal
</button>
<input type="hidden" name="old_filename" value={default_image} />
<input type="hidden" name="related_id" value={related_id} />
<input type="hidden" name="related_type" value={related_type_} />
<input type="hidden" name="data" value={page.url.searchParams.get('data')} />
<input
	{name}
	bind:this={fileInput}
	onchange={handleFileUpload}
	type="file"
	accept="image/*"
	class="d-none"
	id={sid.concat('img_input')}
/>

<style>
	.spinner-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 10;
	}
</style>
