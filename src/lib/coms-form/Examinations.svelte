<script lang="ts">
	import type { PageServerData } from '../../routes/(dash)/opd/[id]/objective/$types';
	import { slide } from 'svelte/transition';
	import Words from '$lib/coms-cu/Words.svelte';
	import Form from './Form.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import SelectWords from '$lib/coms/SelectWords.svelte';
	type Data = Pick<PageServerData, 'get_physicals' | 'get_words' | 'get_visit' | 'get_exams'>;
	interface Props {
		data: Data;
		action?: string;
	}

	let { data, action }: Props = $props();
	let { get_exams, get_visit, get_words } = $derived(data);
	let physicalExam_name = $state('');
	let loading = $state(false);
	let type = $state('');
	let name = $state('');
	let worlds = $derived(get_words?.filter((e) => e.type === type));
</script>

<Words category="objective" {name} {type} words={worlds} modal_name_type="word_modal" />
<div class="row">
	<div class="col-sm-12">
		{#each get_exams as exam, index (exam.id)}
			<button
				onclick={() => {
					if (physicalExam_name === exam.examination) {
						physicalExam_name = '';
					} else {
						physicalExam_name = exam.examination ?? '';
					}
				}}
				type="button"
				class={physicalExam_name === exam.examination
					? 'btn w-100 btn-primary  d-flex align-items-start '
					: 'btn w-100 btn-light my-2  d-flex align-items-start'}
			>
				&nbsp;{index + 1}&nbsp;
				{#if physicalExam_name === exam.examination}
					<span><i class="fas fa-angle-down"></i></span>
				{/if}
				{#if physicalExam_name !== exam.examination}
					<span><i class="fas fa-angle-right"></i></span>
				{/if}
				&nbsp;{exam?.examination}</button
			>

			{#if physicalExam_name === exam.examination}
				<div class="pb-2" in:slide={{ duration: 300 }} out:slide={{ duration: 300 }}>
					{#if exam.physical.length}
						<Form
							id={`id${exam.id}`}
							data_sveltekit_keepfocus={true}
							data_sveltekit_noscroll={true}
							method="post"
							action={action ? action : '?/exam_result'}
							class="bg-primary-subtle rounded py-3 mt-2"
							reset={false}
							bind:loading
						>
							<div class="row px-4">
								{#each exam.physical as physical (physical.id)}
									{@const exam_result = get_visit?.physicalExam.find(
										(e) => e.physical_id === physical.id
									)}
									{@const type_ =
										physical?.physical
											?.replaceAll(' ', '_')
											?.replaceAll('/', '_')
											?.replaceAll("'", '_')
											?.concat(physical?.id.toString()) ?? ''}
									<input type="hidden" name="physical_id" value={physical?.id ?? ''} />

									<input type="hidden" name="physical_exam_id" value={exam_result?.id ?? ''} />
									<div class="col-6">
										<button
											onclick={() => {
												type = type_;
												name = physical.physical ?? '';
											}}
											data-bs-toggle="modal"
											data-bs-target="#word_modal"
											type="button"
											class="btn btn-link"
											>{physical.physical ?? ''}
										</button>

										<!-- <p class="pb-0 mb-0">{element?.physical ?? ''}</p> -->
										<!-- <input
											autocomplete="off"
											bind:value={obj[physical.id]}
											name="physical"
											class="form-control"
											type="text"
										/> -->
										<SelectWords
											name="physical"
											value={exam_result?.result ?? ''}
											words={get_words.filter((e) => e.type === type_).map((e) => e.text)}
										/>
									</div>
								{/each}
								<div
									class="text-end pt-2
								"
								>
									<SubmitButton {loading} />
								</div>
							</div>
						</Form>
					{/if}
				</div>
			{/if}
		{/each}
	</div>
</div>
