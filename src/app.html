<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.ico" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<title>Hospital Management System</title>
		<meta
			name="description"
			content="Comprehensive hospital management system for healthcare providers"
		/>

		<!-- Preload critical CSS -->
		<link
			rel="preload"
			href="%sveltekit.assets%/plugins/bootstrap/css/bootstrap.min.css"
			as="style"
			onload="this.onload=null;this.rel='stylesheet'"
		/>
		<noscript
			><link rel="stylesheet" href="%sveltekit.assets%/plugins/bootstrap/css/bootstrap.min.css"
		/></noscript>

		<!-- Load critical app CSS immediately -->
		<link href="%sveltekit.assets%/plugins/style/app.min.css" rel="stylesheet" />

		<!-- Defer non-critical CSS -->
		<link
			rel="preload"
			href="%sveltekit.assets%/plugins/fontawesome-free/css/all.min.css"
			as="style"
			onload="this.onload=null;this.rel='stylesheet'"
		/>
		<noscript
			><link rel="stylesheet" href="%sveltekit.assets%/plugins/fontawesome-free/css/all.min.css"
		/></noscript>

		<!-- CSS loading optimization script -->
		<script>
			// Polyfill for browsers that don't support rel="preload"
			!(function (e) {
				'use strict';
				var t = function (t, n, r) {
					function o(e) {
						return i.body
							? e()
							: void setTimeout(function () {
									o(e);
								});
					}
					function a() {
						(d.addEventListener && d.removeEventListener('load', a), (d.media = r || 'all'));
					}
					var i = e.document,
						d = i.createElement('link');
					if (n) d.href = n;
					else {
						if (!t) return;
						((d.href = t.href), (d.media = t.media || 'all'));
					}
					((d.rel = 'stylesheet'), d.addEventListener && d.addEventListener('load', a));
					var s = function (e) {
						for (var t = i.querySelectorAll('link[data-href]'), n = 0; n < t.length; n++) {
							var r = t[n];
							if (r.getAttribute('data-href') === e) return r;
						}
					};
					if (s(d.href)) return;
					(d.setAttribute('data-href', d.href),
						o(function () {
							i.head.appendChild(d);
						}));
				};
				'undefined' != typeof module ? (module.exports = t) : (e.loadCSS = t);
			})('undefined' != typeof global ? global : this);
		</script>

		<!-- Preload and defer JavaScript -->
		<script
			src="%sveltekit.assets%/plugins/bootstrap/js/bootstrap.bundle.min.js"
			defer
			crossorigin="anonymous"
		></script>
		<script
			src="%sveltekit.assets%/plugins/bootstrap/js/bs5-toast.min.js"
			defer
			crossorigin="anonymous"
		></script>

		%sveltekit.head%
	</head>

	<body class="sb-nav-fixed" data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>
