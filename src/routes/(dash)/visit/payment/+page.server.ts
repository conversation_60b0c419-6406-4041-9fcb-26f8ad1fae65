import { db } from '$lib/server/db';
import { billing, paymentService } from '$lib/server/schemas';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { logError, message } from '$lib/server/utils/telegram';
import { fileHandle } from '$lib/server/upload';
import { eq } from 'drizzle-orm';
import { DDMMYYYY_Format } from '$lib/server/utils';

export const load = (async ({ url, parent }) => {
	await parent();
	const get_service_types = await db.query.serviceType.findMany({});
	const billing_id = url.searchParams.get('billing_id') ?? '';
	return {
		billing_id,
		get_service_types
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_payment_service: async ({ request, url }) => {
		const body = await request.formData();
		const { code, billing_id, service_type_id, visit_type } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		if (!billing_id || !service_type_id || !visit_type) return fail(400, { errId: true });
		const file = body.get('file') as File;
		const create_payment_service: { id: number }[] = await db
			.insert(paymentService)
			.values({
				code: code ?? '',
				status: 'debt',
				service_type_id: +service_type_id,
				billing_id: +billing_id
			})
			.$returningId()
			.catch((e) => {
				logError({ url, body, err: e });
				return [];
			});
		await db
			.update(billing)
			.set({
				service_type_id: +service_type_id
			})
			.where(eq(billing.id, +billing_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await fileHandle.insert(file, create_payment_service[0].id, 'paymentService');
		const get_billing = await db.query.billing.findFirst({
			where: eq(billing.id, +billing_id),
			with: {
				patient: true,
				paymentService: true,
				visit: {
					with: {
						department: true
					}
				},
				progressNote: {
					with: {
						department: true
					}
				},
				serviceType: true
			}
		});
		let department = '';
		let symptom = '';
		if (visit_type === 'opd') {
			department = get_billing?.visit?.department?.products ?? '';
			symptom = get_billing?.visit?.etiology ?? '';
		}
		if (visit_type === 'ipd') {
			department = get_billing?.progressNote?.department?.products ?? '';
			symptom = get_billing?.progressNote?.etiology ?? '';
		}

		const get_patient = get_billing?.patient;
		const get_service_type = get_billing?.serviceType;
		const get_payment_service = get_billing?.paymentService;
		const khmer_date = 'កាលបរិច្ឆេទ៖ '.concat(DDMMYYYY_Format(get_billing?.created_at, 'datetime'));
		const text = khmer_date
			.concat('\n')
			.concat('ឈ្មោះអ្នកជំងឺ៖ ')
			.concat(`${get_patient?.name_khmer}(${get_patient?.name_latin})`)
			.concat('\n')
			.concat('បញ្ជួនទៅផ្នែក៖ ')
			.concat(department)
			.concat('\n')
			.concat('រោគសញ្ញា៖ ')
			.concat(symptom)
			.concat('\n')
			.concat('សេវាកម្មបង់ប្រាក់៖ ')
			.concat(`${get_service_type?.by} ${get_payment_service?.code}`);
		await message(`${text}`, 'ADMIT');
		redirect(303, `/patient/dialy`);
	}
};
