import { db } from '$lib/server/db';
import { uploads, imagerieRequest } from '$lib/server/schemas';
import { redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';
import { and, desc, eq } from 'drizzle-orm';

export const load = (async ({ parent, url }) => {
	await parent();
	const imagerie_request_id = url.searchParams.get('imagerie_request_id') || '';
	const get_imagerie_request = await db.query.imagerieRequest.findFirst({
		where: eq(imagerieRequest.id, +imagerie_request_id),
		with: {
			visit: {
				with: {
					department: true,
					progressNote: true,
					patient: {
						with: {
							commune: true,
							district: true,
							provice: true,
							village: true
						}
					},
					staff: true
				}
			},
			product: true,
			resultImagerie: true
		},
		orderBy: desc(imagerieRequest.visit_id)
	});

	if (!get_imagerie_request) redirect(303, '/imagerie');
	const get_visit = get_imagerie_request?.visit;

	let patient_info;
	if (get_visit) {
		patient_info = {
			...get_visit?.patient,
			date_checkup: get_visit?.date_checkup
		};
	}
	const get_uploads = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_type, 'imagerieRequest'),
			eq(uploads.related_id, +imagerie_request_id)
		)
	});
	const get_patient_upload = await db.query.uploads.findFirst({
		where: and(
			eq(uploads.related_type, 'patient'),
			eq(uploads.related_id, Number(patient_info?.id) || 0)
		)
	});
	return {
		get_imagerie_request: {
			...get_imagerie_request,
			uploads: get_uploads
		},
		patient_info: {
			...patient_info,
			uploads: get_patient_upload
		}
	};
}) satisfies LayoutServerLoad;
