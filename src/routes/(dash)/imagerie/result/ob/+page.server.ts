import { db } from '$lib/server/db';
import {
	imagerieRequest,
	template,
	resultForm,
	type TType,
	options,
	resultImagerie,
	uploads
} from '$lib/server/schemas';
import type { Actions, PageServerLoad } from './$types';
import { eq, and, ne, asc, desc } from 'drizzle-orm';
import { DDMMYYYY_Format, YYYYMMDD_Format } from '$lib/server/utils';
import { logError, message } from '$lib/server/utils/telegram';
import { fail, redirect } from '@sveltejs/kit';
import { fileHandle } from '$lib/server/upload';
export const load = (async ({ parent, url }) => {
	await parent();
	const imagerie_request_id = url.searchParams.get('imagerie_request_id') || '';
	const group_id = url.searchParams.get('group_id') || '';
	const get_imagerie_templates = await db.query.template.findMany({
		where: eq(template.group_id, +group_id)
	});
	const get_imagerie_request = await db.query.imagerieRequest.findFirst({
		where: eq(imagerieRequest.id, +imagerie_request_id),
		with: {
			visit: {
				with: {
					progressNote: true,
					patient: {
						with: {
							commune: true,
							district: true,
							provice: true,
							village: true
						}
					},
					staff: true
				}
			},
			product: true,
			resultImagerie: true
		},
		orderBy: desc(imagerieRequest.visit_id)
	});
	if (!get_imagerie_request) redirect(303, '/imagerie');
	// if (!get_imagerie_request.is_ob_form) redirect(300, `/imagerie/result/general?imagerie_request_id=${get_imagerie_request.id}`);
	const get_uploads = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_type, 'imagerieRequest'),
			eq(uploads.related_id, get_imagerie_request?.id || 0)
		)
	});
	const get_result_forms = await db.query.resultForm.findMany({
		with: {
			options: true
		},
		orderBy: asc(resultForm.index)
	});
	return {
		get_imagerie_request: {
			...get_imagerie_request,
			uploads: get_uploads
		},
		get_imagerie_templates,
		get_result_forms
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	update_img_result: async ({ request, locals, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		const result_form = body.getAll('result_form');
		const result_form_id = body.getAll('result_form_id');
		const get_imagerie_request = await db.query.imagerieRequest.findFirst({
			where: eq(imagerieRequest.id, +id),
			with: { resultImagerie: true }
		});
		for (let i = 0; i < result_form.length; i++) {
			const result_form_ = String(result_form[i]) || '';
			const result_form_id_ = result_form_id[i];
			if (get_imagerie_request?.resultImagerie.some((e) => e.result_form_id === +result_form_id_)) {
				const find_result_img = get_imagerie_request.resultImagerie.find(
					(e) => e.result_form_id === +result_form_id_
				);
				await db
					.update(resultImagerie)
					.set({
						result: result_form_,
						result_form_id: +result_form_id[i]
					})
					.where(eq(resultImagerie.id, +find_result_img!.id))
					.catch((e) => {
						logError({ url, body, err: e });
					});
			} else {
				await db
					.insert(resultImagerie)
					.values({
						result: result_form_,
						result_form_id: +result_form_id[i],
						imagerie_request_id: +id
					})
					.catch((e) => {
						logError({ url, body, err: e });
					});
			}
		}
		const get_img_result = await db.query.imagerieRequest.findFirst({
			where: eq(imagerieRequest.id, +id),
			with: {
				visit: {
					with: {
						patient: true,
						department: true
					}
				},
				product: true,
				inputBy: {
					with: {
						title: true
					}
				}
			}
		});
		if (result_form.length) {
			const get_visit = get_img_result?.visit;
			const get_product = get_img_result?.product;
			const get_inputer = get_img_result?.inputBy;
			const khemr_date = 'កាលបរិច្ឆេទ៖ '.concat(
				DDMMYYYY_Format(new Date().toISOString(), 'datetime')
			);
			const text = khemr_date
				.concat('\n')
				.concat('ឈ្មោះអ្នកជំងឺ៖ ')
				.concat(`${get_visit?.patient?.name_khmer}(${get_visit?.patient?.name_latin})`)
				.concat('\n')
				.concat('គ្រូពេទ្យពិនិត្យ៖ ')
				.concat(get_inputer?.title?.kh ?? '')
				.concat(' ')
				.concat(get_inputer?.name_khmer ?? '')
				.concat('\n')
				.concat('ផ្នែក៖ ')
				.concat(get_visit?.department?.products ?? '')
				.concat('\n')
				.concat('សេវាកម្ម៖ ')
				.concat(get_product?.products ?? '')
				.concat('\n')
				.concat('ចំណាំ៖ ')
				.concat(get_img_result?.note ?? '')
				.concat('\n')
				.concat('ID៖ ')
				.concat(`IM${String(get_img_result?.id)}`)
				.concat('\n')
				.concat('*បានពិនិត្យរួចរាល់ហើយ*');
			await message(text, 'IMAGERIE');
		}
		await checkImageResult(+id, locals?.user?.staff_id, body, url);
	},
	uploads_img_result: async ({ request, url, locals }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		const file = body.get('file') as File;
		if (file.size) {
			await fileHandle.insert(file, +id, 'imagerieRequest');
		}
		await checkImageResult(+id, locals?.user?.staff_id, body, url);

		if (!file.size) {
			redirect(300, '/imagerie');
		} else {
			redirect(300, `/imagerie/result/ob?imagerie_request_id=${id}`);
		}
	},
	delete_picture: async ({ request, locals, url }) => {
		const body = await request.formData();
		const { file_name, id: imagerie_request_id } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		await fileHandle.drop(file_name);
		await checkImageResult(+imagerie_request_id, locals?.user?.staff_id, body, url);
	},
	update_option: async ({ request, url }) => {
		const body = await request.formData();
		const { name, id } = Object.fromEntries(body) as Record<string, string>;
		if (!name || !id) return fail(400, { optErr: true });
		await db
			.update(options)
			.set({
				name: name
			})
			.where(eq(options.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_option: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (!id) return fail(400, { optErr: true });
		await db
			.delete(options)
			.where(eq(options.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	create_option: async ({ request, url }) => {
		const body = await request.formData();
		const { name, result_form_id } = Object.fromEntries(body) as Record<string, string>;
		if (!name || !result_form_id) return fail(400, { optErr: true });
		await db
			.insert(options)
			.values({
				name: name,
				result_form_id: +result_form_id
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	create_form: async ({ request, url }) => {
		const body = await request.formData();
		const { name, type, id } = Object.fromEntries(body) as Record<string, string>;
		const check_type = ['number', 'text', 'datetime-local', 'option', 'textarea'];
		if (!type || !check_type.includes(type) || !name)
			return fail(400, { message: 'invalid type of form' });
		const count = await db.$count(resultForm);
		if (id) {
			await db
				.update(resultForm)
				.set({
					name: name,
					type: type as TType
				})
				.where(eq(resultForm.id, +id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		if (!id) {
			await db
				.insert(resultForm)
				.values({
					name: name,
					type: type as TType,
					index: count + 1
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	down: async ({ request }) => {
		const body = await request.formData();
		const { id, index } = Object.fromEntries(body) as Record<string, string>;
		if (!index || !id) return fail(400, { message: 'invalid index' });
		await db
			.update(resultForm)
			.set({ index: +index + 1 })
			.where(eq(resultForm.index, +index));
		await db
			.update(resultForm)
			.set({ index: +index })
			.where(and(eq(resultForm.index, +index + 1), ne(resultForm.id, +id)));
	},
	up: async ({ request }) => {
		const body = await request.formData();
		const { id, index } = Object.fromEntries(body) as Record<string, string>;
		if (!index || !id) return fail(400, { indexErr: 'invalid index' });
		await db
			.update(resultForm)
			.set({ index: +index - 1 })
			.where(eq(resultForm.index, +index));
		await db
			.update(resultForm)
			.set({ index: +index })
			.where(and(eq(resultForm.index, +index - 1), ne(resultForm.id, +id)));
	},
	delete_form: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (!id) return fail(400, { indexErr: 'invalid id' });
		await db
			.delete(resultForm)
			.where(eq(resultForm.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};

async function checkImageResult(
	id: number,
	staff_id: number | null | undefined,
	body: FormData,
	url: URL
) {
	const get_imagerie_request_ = await db.query.imagerieRequest.findFirst({
		where: eq(imagerieRequest.id, +id),
		with: {
			resultImagerie: true
		}
	});
	const get_uploads = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_type, 'imagerieRequest'),
			eq(uploads.related_id, get_imagerie_request_?.id || 0)
		)
	});
	const is_image = get_uploads[0]?.filename ? true : false;
	const is_result = get_imagerie_request_?.resultImagerie.some((e) => e.result) ? true : false;
	await db
		.update(imagerieRequest)
		.set({
			status: true,
			is_ob_form: true,
			finish_datetime: is_result && is_image ? YYYYMMDD_Format.datetime(new Date()) : null,
			input_by_id: staff_id
		})
		.where(eq(imagerieRequest.id, +id))
		.catch((e) => {
			logError({ url, body, err: e });
		});
}
