import { db } from '$lib/server/db';
import { imagerieRequest, patient } from '$lib/server/schemas';
import { betweenHelper, pagination } from '$lib/server/utils';
import { logError } from '$lib/server/utils/telegram';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { desc, eq, like, or, and } from 'drizzle-orm';
export const load = (async ({ parent, url }) => {
	await parent();
	const q = url.searchParams.get('q') ?? '';
	const patient_id = Number(url.searchParams.get('patient_id'));
	const status = url.searchParams.get('status') || '';
	const get_imagires = await db.query.imagerieRequest.findMany({
		where: and(
			betweenHelper(url, imagerieRequest.request_datetime),
			patient_id ? eq(imagerieRequest.patient_id, patient_id) : undefined,
			status ? eq(imagerieRequest.status, Boolean(JSON.parse(status))) : undefined
		),
		with: {
			inputBy: {
				with: {
					title: true
				}
			},
			scanBy: {
				with: {
					title: true
				}
			},
			patient: true,
			product: true,
			resultImagerie: true,
			visit: {
				with: {
					patient: true,
					staff: {
						with: {
							title: true
						}
					}
				}
			}
		},
		orderBy: desc(imagerieRequest.id),
		...pagination(url)
	});
	const count = await db.$count(
		imagerieRequest,
		and(
			betweenHelper(url, imagerieRequest.request_datetime),
			patient_id ? eq(imagerieRequest.patient_id, patient_id) : undefined,
			status ? eq(imagerieRequest.status, Boolean(JSON.parse(status))) : undefined
		)
	);
	const get_patients = await db.query.patient.findMany({
		where: or(
			like(patient.name_latin, `%${q}%`),
			like(patient.name_khmer, `%${q}%`),
			like(patient.telephone, `%${q}%`),
			like(patient.id, `%${q}%`)
		),
		limit: 200
	});
	return {
		get_patients,
		items: count,
		get_imagires
	};
}) satisfies PageServerLoad;
export const actions: Actions = {
	assign_inputer: async ({ request, url, locals }) => {
		const body = await request.formData();
		const staff_id = locals.user?.staff_id ?? '';
		const {
			id: imagerie_request_id,
			is_scan,
			is_input
		} = Object.fromEntries(body) as Record<string, string>;
		if (!is_scan && !is_input) return fail(400, { message: 'Check all of id ' });
		if (!imagerie_request_id || !staff_id) return fail(400, { message: 'Check all of id ' });
		const get_imagerie_request = await db.query.imagerieRequest.findFirst({
			where: eq(imagerieRequest.id, +imagerie_request_id)
		});
		const input_by_id = get_imagerie_request?.input_by_id;
		const scan_by_id = get_imagerie_request?.scan_by_id;
		if (scan_by_id && input_by_id) {
			return fail(400, { message: 'Assign already done' });
		}
		if (is_input && !scan_by_id && !is_scan)
			return fail(400, { message: 'Assign Scaner first or both' });
		if (is_input) {
			await db
				.update(imagerieRequest)
				.set({
					input_by_id: input_by_id ? input_by_id : Number(staff_id)
				})
				.where(eq(imagerieRequest.id, +imagerie_request_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		if (is_scan) {
			await db
				.update(imagerieRequest)
				.set({
					scan_by_id: scan_by_id ? scan_by_id : Number(staff_id)
				})
				.where(eq(imagerieRequest.id, +imagerie_request_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	}
};
