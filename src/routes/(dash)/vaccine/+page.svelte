<script lang="ts">
	import type { PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import { store } from '$lib/store/store.svelte';
	import ConfirmModal from '$lib/coms-form/ConfirmModal.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import GenderAge from '$lib/coms/GenderAge.svelte';
	import SetBack from '$lib/coms/SetBack.svelte';
	import Export from '$lib/coms/Export.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_injection, get_patients, items, get_vaccine_groups } = $derived(data);
	let appointment_injection_id = $state<number | null>(null);
	let total_male = $derived(
		get_injection.filter((e) => e.patient?.gender.toLowerCase() === 'male').length
	);
	let n = $state(1);
</script>

<DeleteModal action="?/delete_appionment_inject" bind:id={appointment_injection_id} />
<ConfirmModal action="?/update_appointment_inject" id={appointment_injection_id} />
<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('vaccine')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/vaccine" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-syringe nav-icon"></i>
					{locale.T('vaccine')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<HeaderQuery class="row g-1">
			<div class="col-auto">
				<Export
					title="Vaccine list"
					data={get_injection.map((e) => {
						return {
							id: e.id,
							patient: e.patient?.name_khmer,
							date: e.datetime,
							group: e.group?.name,
							vaccine: e.vaccine.map((i) => i.product?.products).join(','),
							times: e.vaccine.length,
							injected: e.vaccine.filter((i) => i.status === true).length,
							status: e.status
						};
					})}
				/>
			</div>
			<div class="col-sm-3">
				<SelectParam
					q_name="q"
					placeholder={locale.T('patient')}
					name="patient_id"
					items={get_patients.map((e) => ({
						id: e.id,
						name: e.name_khmer?.concat(` ${e.name_latin}`)
					}))}
				/>
			</div>
			<div class="col-sm-3">
				<SelectParam
					placeholder={locale.T('vaccine_type')}
					name="group_id"
					items={get_vaccine_groups.map((e) => ({
						id: e.id,
						name: e.name
					}))}
				/>
			</div>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('status')}</span>
					<select class="form-control" name="status" id="status">
						<option value="">{locale.T('all')}</option>
						<option value="true">{locale.T('done')}</option>
						<option value="false">{locale.T('not_done')}</option>
					</select>
				</div>
			</div>
			<div class="col-auto ms-auto">
				<SetBack href="/vaccine/dose" class="btn btn-success">
					<i class="fa-solid fa-vial-virus"></i>
					{locale.T('vaccine_dose')}
				</SetBack>
			</div>
		</HeaderQuery>
	</div>

	<div style="height: {store.inerHight};" class="card-body table-responsive p-0 m-0">
		<table class="table table-bordered table-hover text-nowrap table-light">
			<thead class="sticky-top top-0 bg-light table-active">
				<tr class="text-center">
					<th class="text-center" style="width: 5%;">{locale.T('n')}</th>
					<th class="text-center">{locale.T('date')}</th>
					<th class="text-center">{locale.T('injection_date')}</th>
					<th>{locale.T('id')} </th>
					<th>{locale.T('patient')}</th>
					<!-- <th>{locale.T('doctor')}</th> -->
					<th>{locale.T('vaccine')}</th>
					<th>{locale.T('times_injection')}</th>
					<th>{locale.T('times_injected')}</th>
					<th>{locale.T('status')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_injection as item, index}
					{@const is_active = item.vaccine.some(
						(e) => e.status === false && e.visit_id && e.product_id
					)}
					{@const filter_vaccine = item.vaccine.filter(
						(e) => e.status === true && e.visit_id && e.product_id
					)}
					{@const biges_times = Math.max(...filter_vaccine.map((e) => e.times))}
					{@const last_inject = filter_vaccine?.find((e) => e.times === biges_times)}
					<tr class="text-center">
						<td class="text-center">{index + n}</td>
						<td class="text-center">
							<DDMMYYYYFormat style="date" date={item.datetime} />
							<br />
							<DDMMYYYYFormat style="time" date={item.datetime} />
						</td>
						<td class="text-center">
							{#if last_inject?.datetime_inject}
								<DDMMYYYYFormat style="date" date={last_inject?.datetime_inject} />
								<br />
								<DDMMYYYYFormat style="time" date={last_inject?.datetime_inject} />
							{/if}
						</td>
						<td class="text-center">
							PT{item?.patient?.id}
							<br />
							VC{item?.id}
						</td>
						<td>
							<a
								class:text-danger={is_active}
								href="/vaccine/injection?vaccine_id={item.id}"
								class="btn btn-link"
							>
								{item?.patient?.name_khmer}
								<br />
								{item?.patient?.name_latin}
								<GenderAge
									dob={item?.patient?.dob}
									date={new Date()}
									gender={item?.patient?.gender}
								/>
							</a>
						</td>

						<td>
							{item.group?.name}
						</td>
						<td>
							{item.vaccine.length}
							{locale.T('times_')}
						</td>
						<td>
							{item.vaccine.filter((e) => e.status === true).length}
							{locale.T('times_')}
						</td>
						<td>
							{#if item.status}
								<button class="btn btn-primary btn-sm">
									<i class="fa-regular fa-circle-check"></i>
									{locale.T('done')}
								</button>
							{:else}
								<button class="btn btn-danger btn-sm">
									<i class="fa-regular fa-circle"></i>
									{locale.T('not_done')}
								</button>
							{/if}
						</td>
					</tr>
				{/each}
				<tr class="table-success">
					<td colspan="9" class="text-center">
						{locale.T('total')}: {get_injection.length}
						{locale.T('people')},
						{locale.T('male')}: {total_male}
						{locale.T('female')}: {get_injection.length - total_male}
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations bind:n {items} />
	</div>
</div>
