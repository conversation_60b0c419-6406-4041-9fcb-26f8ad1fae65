import { db } from '$lib/server/db';
import { appointmentInjection, group, vaccineDose } from '$lib/server/schemas';
import { eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { pagination, YYYYMMDD_Format } from '$lib/server/utils';
import { logError } from '$lib/server/utils/telegram';
import { fail, redirect } from '@sveltejs/kit';
export const load = (async ({ url }) => {
	const vaccine_dose_id = url.searchParams.get('vaccine_dose_id') ?? '';
	const vaccine_group_id = url.searchParams.get('vaccine_group_id') ?? '';
	const get_vaccine_dose = await db.query.vaccineDose.findFirst({
		where: eq(vaccineDose.id, +vaccine_dose_id),
		with: {
			appointmentInjection: true
		}
	});
	const get_vaccine_doses = await db.query.vaccineDose.findMany({
		with: {
			group: true,
			appointmentInjection: true
		},
		where: vaccine_group_id ? eq(vaccineDose.group_id, +vaccine_group_id) : undefined,
		...pagination(url)
	});
	const get_vaccine_groups = await db.query.group.findMany({
		where: eq(group.category_id, 3)
	});
	const count = await db.$count(
		vaccineDose,
		vaccine_group_id ? eq(vaccineDose.group_id, +vaccine_group_id) : undefined
	);
	return {
		get_vaccine_groups,
		get_vaccine_doses,
		items: count,
		get_vaccine_dose
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_vaccine_dose: async ({ request, url }) => {
		const body = await request.formData();
		const { times, group_id } = Object.fromEntries(body) as Record<string, string>;
		if (!times || !group_id) return fail(400, { message: 'Times is required' });
		const id: { id: number }[] = await db
			.insert(vaccineDose)
			.values({
				times: +times,
				create_at: YYYYMMDD_Format.datetime(new Date()),
				group_id: +group_id
			})
			.$returningId()
			.catch((e) => {
				logError({ url, body, err: e });
				return [];
			});

		const days = body.getAll('days') as string[];
		for (let index = 0; index < days.length; index++) {
			await db
				.insert(appointmentInjection)
				.values({
					days: +days[index],
					vaccine_dose_id: id[0]?.id,
					create_at: YYYYMMDD_Format.datetime(new Date()),
					times: index + 1
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	update_vaccine_dose: async ({ request, url }) => {
		const body = await request.formData();
		const { id, times, group_id } = Object.fromEntries(body) as Record<string, string>;
		if (!times || !id) return fail(400, { message: 'Times is required' });
		const get_vaccine_dose = await db.query.vaccineDose.findFirst({
			where: eq(vaccineDose.id, +id),
			with: {
				appointmentInjection: true
			}
		});
		await db
			.update(vaccineDose)
			.set({
				times: +times,
				group_id: +group_id ? +group_id : undefined
			})
			.where(eq(vaccineDose.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		const days = body.getAll('days') as string[];
		for (const element of get_vaccine_dose?.appointmentInjection ?? []) {
			await db
				.delete(appointmentInjection)
				.where(eq(appointmentInjection.id, element.id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		for (let index = 0; index < days.length; index++) {
			await db
				.insert(appointmentInjection)
				.values({
					days: +days[index],
					vaccine_dose_id: +id,
					create_at: YYYYMMDD_Format.datetime(new Date()),
					times: index + 1
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		redirect(303, `?`);
	},
	delete_vaccine_dose: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (!id) return fail(400, { message: 'ID is required' });
		await db
			.delete(vaccineDose)
			.where(eq(vaccineDose.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		redirect(303, `?`);
	}
};
