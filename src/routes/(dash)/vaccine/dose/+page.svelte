<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import GetBack from '$lib/coms/GetBack.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_vaccine_doses, get_vaccine_groups, items, get_vaccine_dose } = $derived(data);
	let n = $state(1);
	let times = $derived(get_vaccine_dose?.times || 0);
</script>

<DeleteModal action="?/delete_vaccine_dose" id={get_vaccine_dose?.id} />

<div class="row">
	<div class="col-sm-6">
		<GetBack href="/vaccine" />
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/vaccine" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-syringe nav-icon"></i>
					{locale.T('vaccine')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/vaccine" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-vial-virus"></i>
					{locale.T('vaccine_dose')}
				</a>
			</li>
		</ol>
	</div>
</div>
<div class="row">
	<div class="col-md-6">
		<div class="card bg-light">
			<div class="card-header">
				<div class="row gap-1">
					<div class="col">
						<HeaderQuery>
							<div class="col-sm-6">
								<SelectParam
									q_name="q"
									placeholder={locale.T('vaccine_type')}
									name="vaccine_group_id"
									items={get_vaccine_groups?.map((e) => ({
										id: e?.id ?? '',
										name: e?.name ?? ''
									}))}
								/>
							</div>
						</HeaderQuery>
					</div>
				</div>
			</div>
			<div style="height: {store.inerHight};" class="card-body table-responsive p-0 m-0">
				<table class="table table-bordered table-hover text-nowrap table-light">
					<thead class="sticky-top top-0 bg-light table-active">
						<tr class="text-center">
							<th class="text-center" style="width: 5%;">{locale.T('n')}</th>
							<th class="text-center">{locale.T('vaccine_type')}</th>
							<th colspan="2"> {locale.T('times')} </th>
						</tr>
					</thead>
					<tbody>
						{#each get_vaccine_doses as item, index}
							<tr class:table-primary={item.id === get_vaccine_dose?.id}>
								<td class="text-center">{index + n}</td>
								<td class="text-start">{item?.group?.name}</td>
								<td
									>{item.times}
									{locale.T('times_')}

									{#each item?.appointmentInjection || [] as i}
										<button type="button" class="btn btn-success ms-3 btn-sm py-0">
											{i.days}
											{locale.T('day')}
										</button>
									{/each}
								</td>
								<td class="text-center">
									<a
										aria-label="editvaccinedose"
										href="?vaccine_dose_id={get_vaccine_dose?.id === item.id ? '' : item.id}"
									>
										<i class="fa-solid fa-file-pen"></i>
									</a>
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>
			<div class="card-footer">
				<Paginations bind:n {items} />
			</div>
		</div>
	</div>
	<div class="col-md-6">
		<div class="card bg-light">
			<Form
				class="p-3"
				method="post"
				action={get_vaccine_dose?.id ? '?/update_vaccine_dose' : '?/create_vaccine_dose'}
			>
				<input type="hidden" name="id" value={get_vaccine_dose?.id ?? ''} />
				<div class="row g-2">
					<div class="col-sm-6">
						<label for="group_id">{locale.T('vaccine_type')}</label>
						<SelectParam
							name="group_id"
							value={Number(get_vaccine_dose?.group_id)}
							items={get_vaccine_groups?.map((e) => ({
								id: e?.id ?? '',
								name: e?.name ?? ''
							}))}
						/>
					</div>
					<div class="col-sm-6">
						<label for="times">{locale.T('times')}</label>
						<input
							bind:value={times}
							min="0"
							type="number"
							name="times"
							class="form-control"
							id="times"
						/>
					</div>

					{#each Array.from({ length: times }, (_, i) => i + 1) || [] as i (i)}
						{@const day = get_vaccine_dose?.appointmentInjection.find((e) => e.times === i)?.days}
						<div class="col-6">
							<button type="button" class="form-control text-success shadow-none">លើកទី {i}</button>
						</div>
						<div class="col-6">
							<div class="input-group">
								<input min="0" value={day} name="days" type="number" class="form-control" />
								<span class="input-group-text">ថ្ងៃ</span>
							</div>
						</div>
					{/each}
					{#if times || get_vaccine_dose?.id}
						<div class="col-6">
							<button
								data-bs-target="#delete_modal"
								data-bs-toggle="modal"
								type="button"
								disabled={!get_vaccine_dose?.id}
								class="btn w-100 btn-danger">{locale.T('delete_')}</button
							>
						</div>
						<div class="col-6">
							<button disabled={!times} type="submit" class="btn btn-primary w-100">
								{#if get_vaccine_dose?.id}
									{locale.T('update')}
								{:else}
									{locale.T('save')}
								{/if}
							</button>
						</div>
					{/if}
				</div>
			</Form>
		</div>
	</div>
</div>
