import { db } from '$lib/server/db';
import { injection, uploads, vaccine } from '$lib/server/schemas';
import { and, asc, desc, eq, ne } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { YYYYMMDD_Format } from '$lib/server/utils';
import { logError } from '$lib/server/utils/telegram';
import { fail } from '@sveltejs/kit';

export const load = (async ({ parent, url }) => {
	await parent();
	const injection_id = url.searchParams.get('vaccine_id') ?? '';
	const get_injection = await db.query.injection.findFirst({
		where: eq(injection.id, +injection_id),
		with: {
			group: true,
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			vaccine: {
				with: {
					injecter: {
						with: {
							title: true
						}
					},
					product: true,
					visit: {
						with: {
							staff: {
								with: {
									title: true
								}
							},
							patient: {
								with: {
									commune: true,
									district: true,
									provice: true,
									village: true
								}
							}
						}
					}
				},
				orderBy: asc(vaccine.id)
			}
		},
		orderBy: desc(injection.datetime)
	});
	const get_visit = get_injection?.vaccine?.find((e) => e.times === 1)?.visit;
	const get_upload = await db.query.uploads.findFirst({
		where: and(
			eq(uploads.related_type, 'patient'),
			eq(uploads.related_id, Number(get_visit?.patient_id))
		)
	});
	const patient_info = {
		...get_visit?.patient,
		date_checkup: get_visit?.date_checkup
	};
	const get_old_injection = await db.query.injection.findMany({
		where: and(
			eq(injection.patient_id, get_injection?.patient_id ?? 0),
			eq(injection.group_id, get_injection?.group_id ?? 0),
			ne(injection.id, +injection_id)
		),
		with: {
			group: true,
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			vaccine: {
				with: {
					injecter: {
						with: {
							title: true
						}
					},
					product: true,
					visit: {
						with: {
							staff: {
								with: {
									title: true
								}
							},
							patient: {
								with: {
									commune: true,
									district: true,
									provice: true,
									village: true
								}
							}
						}
					}
				},
				orderBy: asc(vaccine.id)
			}
		},
		orderBy: desc(injection.datetime)
	});
	return {
		get_injection,
		patient_info: {
			...patient_info,
			uploads: get_upload
		},
		get_old_injection
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	update_injection: async ({ request, url, locals }) => {
		const body = await request.formData();
		const { id, injection_id } = Object.fromEntries(body) as Record<string, string>;
		const { user } = locals;
		if (!id || !user?.staff_id || !injection_id) return fail(400, { message: 'Times is required' });
		await db
			.update(vaccine)
			.set({
				datetime_inject: YYYYMMDD_Format.datetime(new Date()),
				injecter_id: user?.staff_id,
				status: true
			})
			.where(eq(vaccine.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		const get_injection = await db.query.injection.findFirst({
			where: eq(injection.id, +injection_id),
			with: {
				vaccine: true
			}
		});
		if (get_injection?.vaccine?.every((e) => e.status === true)) {
			await db
				.update(injection)
				.set({
					status: true
				})
				.where(eq(injection.id, +injection_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	}
};
