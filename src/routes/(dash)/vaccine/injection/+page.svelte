<script lang="ts">
	import type { PageServerData } from './$types';
	import ConfirmModal from '$lib/coms-form/ConfirmModal.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Name from '$lib/coms/Name.svelte';
	import GetBack from '$lib/coms/GetBack.svelte';
	import PatientInfo from '$lib/coms-ipd-opd/PatientInfo.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let vaccin_id = $state<number>();
	let { get_injection, patient_info, get_old_injection } = $derived(data);
	let times = $state(0);
</script>

<!-- <DeleteModal action="?/delete_appionment_inject" bind:id={appointment_injection_id} /> -->
<ConfirmModal action="?/update_injection" id={vaccin_id}>
	<input type="hidden" name="injection_id" value={get_injection?.id} />
	<ul class="list-group">
		<li class="list-group-item">
			<i class="fas fa-syringe nav-icon"></i>
			ចាក់លើកទី {times}
			{locale.T('date')}
			<DDMMYYYYFormat style="date" date={new Date().toJSON()} />
		</li>
	</ul>
</ConfirmModal>
<div class="row">
	<div class="col-sm-6">
		<GetBack href="/vaccine" />
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/vaccine" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-syringe nav-icon"></i>
					{locale.T('vaccine')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/vaccine/injection" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-book-bookmark nav-icon"></i>
					{locale.T('injection')}
				</a>
			</li>
		</ol>
	</div>
</div>
<PatientInfo {patient_info} />
<br />
<div class="row">
	<div class="col-md-12">
		<div class="table-responsive">
			<table class="table table-bordered text-nowrap table-light text-center">
				<thead>
					<tr class="table-primary">
						<th colspan="7">
							{locale.T('schedule')} - {get_injection?.group?.name}
							<a
								target="_blank"
								class="btn btn-success btn-sm float-end"
								href="/report/{get_injection?.id}/vaccine"
							>
								<i class="fa-solid fa-print"></i>
								{locale.T('print')}
							</a>
						</th>
					</tr>
					<tr class="table-active">
						<th>{locale.T('times')}</th>
						<th>{locale.T('appintment_date')}</th>
						<th>{locale.T('injection_date')}</th>
						<th>{locale.T('doctor_request')}</th>
						<th>{locale.T('doctor_injection')}</th>
						<th>{locale.T('vaccine_name')}</th>
						<th style="width: fit-content">{locale.T('status')}</th>
					</tr>
				</thead>
				<tbody>
					{#each get_injection?.vaccine || [] as iitem}
						<tr class="table-warning">
							<td>លើកទី {iitem.times}</td>
							<td>
								<DDMMYYYYFormat style="date" date={iitem.datetime_appointment} />
							</td>
							<td>
								<DDMMYYYYFormat style="date" date={iitem.datetime_inject} />
							</td>

							<td>
								<Name both name={iitem?.visit?.staff} title={iitem?.visit?.staff?.title} />
							</td>
							<td>
								<Name both name={iitem?.injecter} title={iitem?.injecter?.title} />
							</td>
							<td>
								{iitem?.product?.products}
							</td>
							<td style="width: 100px;" class="text-start">
								{#if iitem.status}
									<button class="btn btn-primary btn-sm">
										<i class="fa-regular fa-circle-check"></i>
										{locale.T('done')}
									</button>
								{:else}
									<button class="btn btn-danger btn-sm">
										<i class="fa-regular fa-circle"></i>
										{locale.T('not_done')}
									</button>
								{/if}
								{#if iitem.status === false && iitem.visit_id}
									<button
										onclick={() => {
											vaccin_id = iitem.id;
											times = iitem.times;
										}}
										data-bs-toggle="modal"
										data-bs-target="#confirm_modal"
										aria-label="inject"
										type="button"
										class="btn btn-primary btn-sm"
									>
										<i class="fa-solid fa-syringe"></i>
									</button>
								{/if}
							</td>
						</tr>
					{/each}
				</tbody>
			</table>
		</div>
	</div>
	{#if get_old_injection.length}
		{#each get_old_injection as item (item.id)}
			<div class="col-md-12">
				<div class="table-responsive">
					<table class="table table-bordered text-nowrap table-light text-center">
						<thead>
							<tr class="table-primary">
								<th colspan="7">{locale.T('schedule')} - {item?.group?.name} </th>
							</tr>
							<tr class="table-active">
								<th>{locale.T('times')}</th>
								<th>{locale.T('appintment_date')}</th>
								<th>{locale.T('injection_date')}</th>
								<th>{locale.T('doctor_request')}</th>
								<th>{locale.T('doctor_injection')}</th>
								<th>{locale.T('vaccine_name')}</th>
								<th style="width: fit-content">{locale.T('status')}</th>
							</tr>
						</thead>
						<tbody>
							{#each item?.vaccine || [] as iitem}
								<tr class="table-warning">
									<td>លើកទី {iitem.times}</td>
									<td>
										<DDMMYYYYFormat style="date" date={iitem.datetime_appointment} />
									</td>
									<td>
										<DDMMYYYYFormat style="date" date={iitem.datetime_inject} />
									</td>

									<td>
										<Name both name={iitem?.visit?.staff} title={iitem?.visit?.staff?.title} />
									</td>
									<td>
										<Name both name={iitem?.injecter} title={iitem?.injecter?.title} />
									</td>
									<td>
										{iitem?.product?.products}
									</td>
									<td style="width: 100px;" class="text-start">
										{#if iitem.status}
											<button class="btn btn-primary btn-sm">
												<i class="fa-regular fa-circle-check"></i>
												{locale.T('done')}
											</button>
										{:else}
											<button class="btn btn-danger btn-sm">
												<i class="fa-regular fa-circle"></i>
												{locale.T('not_done')}
											</button>
										{/if}
										{#if iitem.status === false && iitem.visit_id}
											<button
												onclick={() => {
													vaccin_id = iitem.id;
													times = iitem.times;
												}}
												data-bs-toggle="modal"
												data-bs-target="#confirm_modal"
												aria-label="inject"
												type="button"
												class="btn btn-primary btn-sm"
											>
												<i class="fa-solid fa-syringe"></i>
											</button>
										{/if}
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			</div>
		{/each}
	{/if}
</div>
