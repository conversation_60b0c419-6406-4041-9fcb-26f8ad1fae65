import { db } from '$lib/server/db';
import { group, injection, patient, vaccine, vaccineDose } from '$lib/server/schemas';
import { and, asc, eq, like, or } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { betweenH<PERSON>per, YYYYMMDD_Format, pagination } from '$lib/server/utils';
import { logError } from '$lib/server/utils/telegram';
import { fail } from '@sveltejs/kit';

export const load = (async ({ parent, url }) => {
	await parent();
	const patient_id = url.searchParams.get('patient_id') ?? '';
	const group_id = url.searchParams.get('group_id') ?? '';
	const status = url.searchParams.get('status') ?? '';
	const q = url.searchParams.get('q') ?? '';
	const get_patients = await db.query.patient.findMany({
		where: or(
			like(patient.name_latin, `%${q}%`),
			like(patient.name_khmer, `%${q}%`),
			like(patient.telephone, `%${q}%`),
			like(patient.id, `%${q}%`)
		),
		limit: 200
	});
	const get_injection = await db.query.injection.findMany({
		where: and(
			betweenHelper(url, injection.datetime),
			patient_id ? eq(injection.patient_id, +patient_id) : undefined,
			status ? eq(injection.status, Boolean(JSON.parse(status))) : undefined,
			group_id ? eq(injection.group_id, +group_id) : undefined
		),
		with: {
			group: true,
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			vaccine: {
				with: {
					product: true,
					visit: true,
					injecter: {
						with: {
							title: true
						}
					}
				},
				orderBy: asc(vaccine.id)
			}
		},
		orderBy: asc(injection.status),
		...pagination(url)
	});
	const count = await db.$count(
		injection,
		and(
			betweenHelper(url, injection.datetime),
			patient_id ? eq(injection.patient_id, +patient_id) : undefined,
			status ? eq(injection.status, Boolean(JSON.parse(status))) : undefined,
			group_id ? eq(injection.group_id, +group_id) : undefined
		)
	);
	const get_vaccine_groups = await db.query.group.findMany({
		where: eq(group.category_id, 3)
	});
	const get_vaccine_doses = await db.query.vaccineDose.findMany();
	return {
		get_injection,
		get_patients,
		items: count,
		get_vaccine_groups,
		get_vaccine_doses
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_vaccine_dose: async ({ request, url }) => {
		const body = await request.formData();
		const { times, group_id } = Object.fromEntries(body) as Record<string, string>;
		if (!times) return fail(400, { message: 'Times is required' });

		await db
			.insert(vaccineDose)
			.values({
				times: +times,
				create_at: YYYYMMDD_Format.datetime(new Date()),
				group_id: +group_id
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_vaccine_dose: async ({ request, url }) => {
		const body = await request.formData();
		const { id, times } = Object.fromEntries(body) as Record<string, string>;
		if (!times || !id) return fail(400, { message: 'Times is required' });
		await db
			.update(vaccineDose)
			.set({
				times: +times
			})
			.where(eq(vaccineDose.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_vaccine_dose: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (!id) return fail(400, { message: 'ID is required' });
		await db
			.delete(vaccineDose)
			.where(eq(vaccineDose.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
