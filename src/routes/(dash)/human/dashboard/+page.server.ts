import { db } from '$lib/server/db';
import { and, eq, inArray } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { staff, uploads } from '$lib/server/schemas';
import { YYYYMMDD_Format } from '$lib/server/utils';
export const load = (async () => {
	const get_salaries = await db.query.salary.findMany({});
	const get_currency = await db.query.currency.findFirst({});
	const get_staffs = await db.query.staff.findMany({
		with: {
			user: true,
			district: true,
			commune: true,
			village: true,
			provice: true,
			staffToDemartment: {
				with: {
					department: true
				}
			},
			staffToRole: {
				with: {
					role: true
				}
			},
			title: true
		}
	});
	const items = await db.$count(staff);

	const get_roles = await db.query.role.findMany({});
	const get_uploads = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_type, 'staff'),
			inArray(
				uploads.related_id,
				get_staffs.map((e) => e.id)
			)
		)
	});
	const get_this_month = YYYYMMDD_Format.date(new Date()).slice(0, 7);
	return {
		get_staffs: get_staffs.map((e) => {
			return {
				...e,
				uploads: get_uploads.find((ee) => ee.related_id === e.id)
			};
		}),
		get_roles,
		get_salaries,
		get_currency,
		get_this_month,
		items
	};
}) satisfies PageServerLoad;
