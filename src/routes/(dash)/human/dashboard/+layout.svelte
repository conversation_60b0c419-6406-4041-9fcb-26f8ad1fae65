<script lang="ts">
	import Currency from '$lib/coms/Currency.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { Snippet } from 'svelte';
	import type { LayoutServerData } from './$types';
	import { page } from '$app/state';
	let { data, children }: { data: LayoutServerData; children: Snippet } = $props();
	let { get_staffs, get_salaries, get_currency } = $derived(data);
	let total_employees = $derived(get_staffs.length);
	let total_active_employees = $derived(get_staffs.filter((e) => !e.datetime_stop).length);
	let total_inactive_employees = $derived(get_staffs.filter((e) => e.datetime_stop).length);
	let employee_male = $derived(get_staffs.filter((e) => e.gender === 'Male').length);
	let employee_female = $derived(get_staffs.filter((e) => e.gender === 'Female').length);
	let employee_male_active = $derived(
		get_staffs.filter((e) => e.gender === 'Male' && !e.datetime_stop).length
	);
	let employee_female_active = $derived(
		get_staffs.filter((e) => e.gender === 'Female' && !e.datetime_stop).length
	);
	let employee_male_inactive = $derived(
		get_staffs.filter((e) => e.gender === 'Male' && e.datetime_stop).length
	);
	let employee_female_inactive = $derived(
		get_staffs.filter((e) => e.gender === 'Female' && e.datetime_stop).length
	);
	let total_base_salary = $derived(get_salaries.reduce((s, e) => s + Number(e.base_salary), 0));
	let total_allowance = $derived(get_salaries.reduce((s, e) => s + Number(e.allowance), 0));
	let total_bonus = $derived(get_salaries.reduce((s, e) => s + Number(e.bonus), 0));
	let total_deduction = $derived(get_salaries.reduce((s, e) => s + Number(e.deduction), 0));
	
</script>

<div class="row">
	<div class="col-sm-6">
		<a href="/human" class="btn btn-link p-0"
			><i class="fa-solid fa-rotate-left"></i>
			{locale.T('back')}
		</a>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/human" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-users nav-icon"></i>
					{locale.T('staff')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/human/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-users nav-icon"></i>
					{locale.T('dashboard')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="row g-2">
	<div class="col-md-4">
		<div class="row g-1 pb-2">
			<div class="col-md-6 col-sm-12">
				<div class="card bg-light text-primary-emphasis">
					<div class="card-body row g-0 justify-content-between">
						<div class="col-auto">
							<div style="font-size: 20px;">{locale.T('total_base_salary')}</div>
							<div style="font-size: 20px;">
								<Currency class="" amount={total_base_salary} symbol={get_currency?.currency} />
							</div>
						</div>
						<div class="col-auto">
							<i class="fa-solid fa-wallet fa-4x"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-6 col-sm-12">
				<div class="card bg-light text-success-emphasis">
					<div class="card-body row g-0 justify-content-between">
						<div class="col-auto">
							<div style="font-size: 20px;">{locale.T('total_allowance')}</div>
							<div style="font-size: 20px;">
								<Currency class="" amount={total_allowance} symbol={get_currency?.currency} />
							</div>
						</div>
						<div class="col-auto">
							<i class="fa-solid fa-sack-dollar fa-4x"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-6 col-sm-12">
				<div class="card bg-light text-warning-emphasis">
					<div class="card-body row g-0 justify-content-between">
						<div class="col-auto">
							<div style="font-size: 20px;">{locale.T('total_bonus')}</div>
							<div style="font-size: 20px;">
								<Currency class="" amount={total_bonus} symbol={get_currency?.currency} />
							</div>
						</div>
						<div class="col-auto">
							<i class="fa-solid fa-money-check-dollar fa-4x"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-6 col-sm-12">
				<div class="card bg-light text-danger-emphasis">
					<div class="card-body row g-0 justify-content-between">
						<div class="col-auto">
							<div style="font-size: 20px;">{locale.T('total_deduction')}</div>
							<div style="font-size: 20px;">
								<Currency class="" amount={total_deduction} symbol={get_currency?.currency} />
							</div>
						</div>
						<div class="col-auto">
							<i class="fa-solid fa-money-bill-trend-up fa-4x"></i>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col pb-2">
			<div class="card bg-light">
				<div class="card-header text-center bg-success-subtle">
					{locale.T('total_employeeses')}
				</div>
				<div class="card-body">
					<div class="row g-0">
						<div class="col-md-4">
							<div class="text-center">
								<div style="font-size: 20px;">
									{locale.T('male')}
									{employee_male}
									{locale.T('people')}
								</div>
								<img style="height: 91px;" src="/m.ico" alt="" />
							</div>
						</div>
						<div class="col-md-4">
							<div class="text-center">
								<div style="font-size: 20px;">
									{locale.T('female')}
									{employee_female}
									{locale.T('people')}
								</div>
								<img style="height: 91px;" src="/f.ico" alt="" />
							</div>
						</div>
						<div class="col-md-4">
							<div class="text-center">
								<div style="font-size: 20px;">
									{locale.T('total')}
									{total_employees}
									{locale.T('people')}
								</div>
								<img style="height: 91px;" src="/fm.ico" alt="" />
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col pb-2">
			<div class="card bg-light">
				<div class="card-header text-center bg-primary-subtle">
					{locale.T('active_employees')}
				</div>
				<div class="card-body">
					<div class="row g-0">
						<div class="col-md-4">
							<div class="text-center">
								<div style="font-size: 20px;">
									{locale.T('male')}
									{employee_male_active}
									{locale.T('people')}
								</div>

								<img style="height: 91px;" src="/m.ico" alt="" />
							</div>
						</div>
						<div class="col-md-4">
							<div class="text-center">
								<div style="font-size: 20px;">
									{locale.T('female')}
									{employee_female_active}
									{locale.T('people')}
								</div>
								<img style="height: 91px;" src="/f.ico" alt="" />
							</div>
						</div>
						<div class="col-md-4">
							<div class="text-center">
								<div style="font-size: 20px;">
									{locale.T('total')}
									{total_active_employees}
									{locale.T('people')}
								</div>
								<img style="height: 91px;" src="/fm.ico" alt="" />
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col pb-2">
			<div class="card bg-light">
				<div class="card-header text-center bg-danger-subtle">
					{locale.T('inactive_employees')}
				</div>
				<div class="card-body">
					<div class="row g-0">
						<div class="col-md-4">
							<div class="text-center">
								<div style="font-size: 20px;">
									{locale.T('male')}
									{employee_male_inactive}
									{locale.T('people')}
								</div>
								<img style="height: 91px;" src="/m.ico" alt="" />
							</div>
						</div>
						<div class="col-md-4">
							<div class="text-center">
								<div style="font-size: 20px;">
									{locale.T('female')}
									{employee_female_inactive}
									{locale.T('people')}
								</div>
								<img style="height: 91px;" src="/f.ico" alt="" />
							</div>
						</div>
						<div class="col-md-4">
							<div class="text-center">
								<div style="font-size: 20px;">
									{locale.T('total')}
									{total_inactive_employees}
									{locale.T('people')}
								</div>
								<img style="height: 91px;" src="/fm.ico" alt="" />
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="col-md-8">
		{@render children?.()}
	</div>
</div>
