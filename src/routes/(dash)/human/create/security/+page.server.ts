import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { hash } from '@node-rs/argon2';
import { encodeBase32LowerCase } from '@oslojs/encoding';
import { uploads, staff, user } from '$lib/server/schemas';
import { logError } from '$lib/server/utils/telegram';
import { and, eq } from 'drizzle-orm';
import { deleteFile } from '$lib/server/upload/fileHandle';
import { permision } from '$lib/server/auth/permision';
export const load: PageServerLoad = async ({ parent, url, locals }) => {
	await parent();
	const staff_id = url.searchParams.get('staff_id') ?? '';
	permision.go({
		locals,
		staff_id: +staff_id,
		redirect_: '/staff'
	});
	const get_staff = await db.query.staff.findFirst({
		where: eq(staff.id, +staff_id),
		with: {
			user: true
		}
	});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'staff'), eq(uploads.related_id, +staff_id))
	});
	const get_upload_sign = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'staffSign'), eq(uploads.related_id, +staff_id))
	});
	return {
		get_staff: {
			...get_staff,
			uploads: get_upload,
			sign: get_upload_sign
		},
		locals: locals
	};
};

export const actions: Actions = {
	register: async ({ request, url }) => {
		const body = await request.formData();
		const username = body.get('username') as Record<string, string>['username'];
		const password = body.get('password');
		const staff_id = body.get('staff_id');
		const find_user = await db.query.user.findFirst({
			where: eq(user.username, username)
		});
		if (!validateUsername(username) || find_user) {
			return fail(400, { message: 'Invalid username' });
		}
		if (!validatePassword(password)) {
			return fail(400, { message: 'Invalid password' });
		}
		if (!staff_id) {
			return fail(400, { message: 'Invalid role or staff' });
		}
		// if (locals.user?.staff !== 'ADMIN' && locals.user?.staff_id !== +staff_id)
		// 	return fail(400, { message: 'Permission denied' });
		const userId = generateUserId();
		const passwordHash = await hash(password, {
			// recommended minimum parameters
			memoryCost: 19456,
			timeCost: 2,
			outputLen: 32,
			parallelism: 1
		});
		try {
			await db.insert(user).values({
				id: userId,
				username,
				password_hash: passwordHash,
				staff_id: +staff_id
			});
			// const sessionToken = auth.generateSessionToken();
			// const session = await auth.createSession(sessionToken, userId);
			// auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);
		} catch (e) {
			logError({ url, body, err: e });
			return fail(500, { message: 'An error has occurred' });
		}
	},
	update_user: async ({ request, url }) => {
		const body = await request.formData();
		const username = body.get('username')?.toString();
		const password = body.get('password')?.toString() ?? '';
		const staff_id = body.get('staff_id')?.toString() ?? '';
		if (!validateUsername(username)) {
			return fail(400, { username: 'Invalid username' });
		}
		// if (!locals?.roles?.some((e) => e.role?.toLowerCase()?.includes('admin'))) {
		// 	const validPassword = await verify(password_hash, confirm_password, {
		// 		memoryCost: 19456,
		// 		timeCost: 2,
		// 		outputLen: 32,
		// 		parallelism: 1
		// 	});
		// 	if (!validPassword) {
		// 		return fail(400, { confirm_password: 'wrong password' });
		// 	}
		// }
		// if (locals.user?.staff !== 'ADMIN' && locals.user?.staff_id !== +staff_id)
		// 	return fail(400, { message: 'Permission denied' });
		const passwordHash = await hash(password, {
			// recommended minimum parameters
			memoryCost: 19456,
			timeCost: 2,
			outputLen: 32,
			parallelism: 1
		});
		try {
			await db
				.update(user)
				.set({
					username,
					password_hash: passwordHash
				})
				.where(eq(user.staff_id, +staff_id));
			// const sessionToken = auth.generateSessionToken();
			// const session = await auth.createSession(sessionToken, userId);
			// auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);
		} catch (e) {
			logError({ url, body, err: e });
			return fail(500, { message: 'An error has occurred' });
		}
	},
	delete_staff: async ({ request, url }) => {
		const body = await request.formData();
		const { id, image } = Object.fromEntries(body) as Record<string, string>;
		try {
			await db.delete(staff).where(eq(staff.id, Number(id)));
			await deleteFile(image);
		} catch (e) {
			logError({ url, body, err: e });
			return fail(500, { message: 'An error has occurred' });
		}
		redirect(303, '/staff');
	}
};

function generateUserId() {
	// ID with 120 bits of entropy, or about the same as UUID v4.
	const bytes = crypto.getRandomValues(new Uint8Array(15));
	const id = encodeBase32LowerCase(bytes);
	return id;
}

function validateUsername(username: unknown): username is string {
	return (
		typeof username === 'string' &&
		username.length >= 3 &&
		username.length <= 31 &&
		/^[a-z0-9_-]+$/.test(username)
	);
}

function validatePassword(password: unknown): password is string {
	return typeof password === 'string' && password.length >= 6 && password.length <= 255;
}
