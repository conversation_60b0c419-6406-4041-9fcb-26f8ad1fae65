<script lang="ts">
	import type { Snippet } from 'svelte';
	import type { LayoutServerData } from './$types';
	import { locale } from '$lib/translations/locales.svelte';
	import { page } from '$app/state';
	import GetBack from '$lib/coms/GetBack.svelte';

	let { data, children }: { data: LayoutServerData; children: Snippet } = $props();
	let { get_staff } = $derived(data);
</script>

<div class="row">
	<div class="col-sm-6">
		<GetBack href="/human" />
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/staff" class="btn btn-link p-0 text-secondary">
					<i class="fa-solid fa-user"></i>
					{locale.T('staff')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/user" class="btn btn-link p-0 text-secondary">
					<i class="fa-solid fa-user-lock"></i>
					{locale.T('user')}
				</a>
			</li>
		</ol>
	</div>
</div>

<!-- Profile Header -->
<div class="alert {get_staff?.datetime_stop ? 'alert-danger' : 'alert-primary'} ">
	<div class="text-center">
		<div class="position-relative d-inline-block">
			<!-- svelte-ignore a11y_img_redundant_alt -->
			<img
				style="width: 150px;height: 150px;"
				src={get_staff?.uploads?.filename ? `${get_staff?.uploads?.filename}` : '/no-user.webp'}
				class="rounded-circle img-thumbnail me-3"
				alt="Profile Picture"
			/>
			<!-- svelte-ignore a11y_img_redundant_alt -->
			<img
				style="height: 150px;"
				src={get_staff?.sign?.filename ? `${get_staff?.sign?.filename}` : '/no-user.webp'}
				class="img-thumbnail rounded-2"
				alt="Profile Picture"
			/>
		</div>
		<h3 class="mt-3 mb-1">{get_staff?.name_khmer} ({get_staff?.name_latin})</h3>
		<p class="text-muted mb-3">
			{get_staff?.specialist}
		</p>

		<div class="d-flex justify-content-center gap-2 pb-2">
			<a
				class:active={page.url.pathname.includes('profile')}
				class="btn btn-outline-primary"
				href="/human/create/profile?staff_id={get_staff?.id}&province_id={get_staff?.province_id}&district_id={get_staff?.district_id}&commune_id={get_staff?.commune_id}&village_id={get_staff?.village_id}"
				><i class="fas fa-user me-2"></i>{locale.T('personal_info')}</a
			>
			<fieldset disabled={!get_staff?.id}>
				<a
					class:active={page.url.pathname.includes('titles')}
					class="btn btn-outline-primary"
					href="/human/create/titles?staff_id={get_staff?.id}"
					><i class="fas fa-user me-2"></i>{locale.T('role_title')}</a
				>
				<a
					class:active={page.url.pathname.includes('security')}
					class="btn btn-outline-primary"
					href="/human/create/security?staff_id={get_staff?.id}"
					><i class="fas fa-lock me-2"></i>{locale.T('security')}</a
				>
				<a
					class:active={page.url.pathname.includes('payroll')}
					class="btn btn-outline-primary"
					href="/human/create/payroll?staff_id={get_staff?.id}"
					><i class="fas fa-credit-card me-2"></i>{locale.T('payroll')}</a
				>
				<a
					class:active={page.url.pathname.includes('leave')}
					class="btn btn-outline-primary"
					href="/human/create/leave?staff_id={get_staff?.id}"
					><i class="fa-regular fa-calendar-check me-2"></i>{locale.T('leave')}</a
				>
				<a
					class:active={page.url.pathname.includes('activity')}
					class="btn btn-outline-primary"
					href="/human/create/activity?staff_id={get_staff?.id}"
					><i class="fas fa-chart-line me-2"></i>{locale.T('activity')}</a
				>
			</fieldset>
		</div>
	</div>
</div>
<!-- Main Content -->

{@render children()}
<br />
