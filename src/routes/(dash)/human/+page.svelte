<script lang="ts">
	import type { PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import HandleQ from '$lib/coms-form/HandleQ.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import { dobToAge } from '$lib/helper';
	import Export from '$lib/coms/Export.svelte';
	import { page } from '$app/state';

	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let staff_id = $state<number>();
	let { get_staffs, items, get_roles } = $derived(data);
	let find_staff = $derived(get_staffs.filter((e) => e.id === staff_id));
	let n: number = $state(1);
	let get_role = $derived(page.url.searchParams.get('role') || '');
	let q = $derived(page.url.searchParams.get('q') || '');
	let status = $derived(page.url.searchParams.get('status') || '');
	let filter_staffs = $derived.by(() => {
		const result = get_staffs.filter(
			(e) =>
				e.staffToRole.find((i) => i.role?.role.toLowerCase().includes(get_role.toLowerCase())) &&
				(status === 'active' ? !e.datetime_stop : status === 'inactive' ? e.datetime_stop : true)
		);
		return result.filter((e) => {
			return (
				e?.name_khmer?.toLowerCase().includes(q.toLowerCase()) ||
				e?.name_latin?.toLowerCase().includes(q.toLowerCase()) ||
				e.id?.toString().includes(q)
			);
		});
	});
</script>

<DeleteModal action="?/delete_staff" id={find_staff[0]?.id} />

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('staff')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/staff" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-users nav-icon"></i>
					{locale.T('staff')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<HeaderQuery class="row g-1">
			<div class="col-auto">
				<Export
					title="Staff list"
					data={filter_staffs?.map((e) => {
						return {
							id: e.id,
							name_khmer: e.name_khmer,
							name_latin: e.name_latin,
							dob: e.dob,
							gender: e.gender,
							specialist: e.specialist,
							role: e.staffToRole.map((i) => i.role?.role).join(',')
						};
					})}
				/>
			</div>
			<div class="col-sm-auto">
				<HandleQ />
			</div>
			<div class="col-sm-auto">
				<select class="form-control" name="role" id="role">
					<option selected value="">ALL</option>
					{#each get_roles as item}
						<option value={item.role}>{item.role}</option>
					{/each}
				</select>
			</div>
			<div class="col-sm-auto">
				<select class="form-control" name="status" id="status">
					<option selected value="">{locale.T('status')}</option>
					<option value="active">{locale.T('active_employees')}</option>
					<option value="inactive">{locale.T('inactive_employees')}</option>
				</select>
			</div>
			<div class="col-auto ms-auto">
				<a href="/human/dashboard" type="button" class="btn btn-success"
					><i class="fa-solid fa-chart-line"></i>
					{locale.T('dashboard')}
				</a>
			</div>
		</HeaderQuery>
	</div>

	<div style="height: {store.inerHight};" class="card-body table-responsive">
		<div class="row g-3">
			{#each filter_staffs as item (item.id)}
				<div class="col-12 col-sm-6 col-lg-6 col-xl-4">
					<div class="card shadow-sm h-100 staff-card">
						<div class="row g-0 h-100">
							<div
								class="col-12 col-md-4 p-3 text-center d-flex flex-column justify-content-center"
							>
								<!-- svelte-ignore a11y_img_redundant_alt -->
								<img
									src={item?.uploads?.filename ? `${item?.uploads?.filename}` : '/no-user.webp'}
									class="rounded-circle img-thumbnail staff-avatar mx-auto"
									alt="Profile Picture"
								/>
								<div class="mt-2">
									{#if item?.datetime_stop}
										<span class="badge bg-danger">{locale.T('stop_working')}</span>
									{:else}
										<span class="badge bg-success">{locale.T('working')}</span>
									{/if}
								</div>
							</div>
							<div class="col-12 col-md-8 d-flex flex-column">
								<div class="card-body flex-grow-1 d-flex flex-column">
									<a
										href="/human/create/profile?staff_id={item.id}&province_id={item.province_id}&district_id={item.district_id}&commune_id={item.commune_id}&village_id={item.village_id}"
										class="text-decoration-none text-primary mb-2 staff-name"
									>
										<span>
											{item?.title?.kh}
											{item?.name_khmer}
											({item?.name_latin})
										</span>
									</a>
									<p class="card-text text-muted mb-2 small">
										<i class="fas fa-briefcase me-1"></i>
										{item?.specialist}
									</p>
									<p class="card-text text-muted mb-auto small">
										<i class="fa-solid fa-user-doctor me-1"></i>
										{#each item?.staffToDemartment as department (department.department_id)}
											{department?.department?.products ?? ''}
										{/each}
									</p>
									<div class="border-top pt-2 mt-2">
										<div class="row text-center g-0">
											<div class="col-4">
												<div class="small text-muted">{locale.T('id')}</div>
												<strong class="small">ST{item?.id}</strong>
											</div>
											<div class="col-4 border-start">
												<div class="small text-muted">{locale.T('age')}</div>
												<strong class="small">{dobToAge(item?.dob, null)}</strong>
											</div>
											<div class="col-4 border-start">
												<div class="small text-muted">{locale.T('gender')}</div>
												<strong class="small">
													{item?.gender?.toLowerCase() === 'male'
														? locale.T('male')
														: item?.gender?.toLowerCase() === 'female'
															? locale.T('female')
															: locale.T('none')}
												</strong>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			{/each}
		</div>
	</div>
	<div class="card-footer">
		<fieldset disabled>
			<Paginations bind:n {items} />
		</fieldset>
	</div>
</div>

<style>
	.staff-card {
		transition:
			transform 0.2s ease-in-out,
			box-shadow 0.2s ease-in-out;
		min-height: 200px;
	}

	.staff-card:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
	}

	.staff-avatar {
		width: 80px;
		height: 80px;
		object-fit: cover;
	}

	/* Responsive adjustments */
	@media (min-width: 768px) {
		.staff-avatar {
			width: 100px;
			height: 100px;
		}

		.staff-name {
			font-size: 1rem;
		}
	}

	@media (min-width: 992px) {
		.staff-avatar {
			width: 120px;
			height: 120px;
		}

		.staff-name {
			font-size: 1.1rem;
		}
	}

	/* Mobile layout adjustments */
	@media (max-width: 767px) {
		.staff-card .row {
			flex-direction: column;
		}

		.staff-card .col-12:first-child {
			border-bottom: 1px solid #dee2e6;
			padding-bottom: 1rem;
		}

		.staff-card .col-12:last-child {
			padding-top: 1rem;
		}
	}
</style>
