import { db } from '$lib/server/db';
import {
	currency,
	documentSetting,
	setting,
	telegram,
	telegramTypeArr,
	type TTelegram
} from '$lib/server/schemas';
import { eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { fail } from '@sveltejs/kit';
import { fileHandle } from '$lib/server/upload';
import { logError } from '$lib/server/utils/telegram';
export const load = (async () => {
	const get_currency = await db.query.currency.findFirst({});
	const get_document_setting = await db.query.documentSetting.findFirst({});
	const get_setting = await db.query.setting.findFirst();
	const get_telegram = await db.query.telegram.findMany({});
	return {
		get_currency,
		get_document_setting,
		get_setting,
		get_telegram,
		telegramTypeArr
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_currency: async ({ request, url }) => {
		const body = await request.formData();
		const {
			currency: _currency,
			currency_rate,
			exchang_to,
			exchang_rate,
			currency_id
		} = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			_currency: false,
			currency_rate: false,
			exchang_to: false,
			exchang_rate: false
		};
		if (!_currency || _currency.length > 5) validErr._currency = true;
		if (!currency_rate) validErr.currency_rate = true;
		if (!exchang_to || exchang_to.length > 5) validErr.exchang_to = true;
		if (!exchang_rate) validErr.exchang_rate = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		if (currency_id) {
			await db
				.update(currency)
				.set({
					currency: _currency,
					currency_rate: +currency_rate,
					exchang_to: exchang_to,
					exchang_rate: +exchang_rate
				})
				.where(eq(currency.id, 1))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		} else {
			await db
				.insert(currency)
				.values({
					currency: _currency,
					currency_rate: +currency_rate,
					exchang_to: exchang_to,
					exchang_rate: +exchang_rate
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	setting: async ({ request }) => {
		const body = await request.formData();
		const { print_bill, setting_id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(setting)
			.set({
				print_bill: print_bill ? true : false
			})
			.where(eq(setting.id, +setting_id));
	},
	setting_document: async ({ request }) => {
		const body = await request.formData();
		const {
			logo_size,
			clinic_title_en_size,
			clinic_title_en_color,
			clinic_title_kh_color,
			clinic_title_kh_size,
			header_size,
			header_color,
			title_size,
			title_color,
			footer_color,
			footer_size,
			text_body_color,
			text_input_color
		} = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(documentSetting)
			.set({
				logo_size: logo_size,
				clinic_title_en_size: clinic_title_en_size,
				clinic_title_en_color: clinic_title_en_color,
				clinic_title_kh_color: clinic_title_kh_color,
				clinic_title_kh_size: clinic_title_kh_size,
				header_size: header_size,
				header_color: header_color,
				title_size: title_size,
				title_color: title_color,
				footer_color: footer_color,
				footer_size: footer_size,
				text_body_color: text_body_color,
				text_input_color: text_input_color
			})
			.where(eq(documentSetting.id, 1));
	},
	delete_uploads: async ({ request, locals }) => {
		const body = await request.formData();
		if (locals.user === null) return fail(401, { message: 'Unauthorized' });
		const { filename } = Object.fromEntries(body) as Record<string, string>;
		if (!filename) return fail(400, { message: 'filename is required' });
		await fileHandle.drop(filename);
	},
	telegram: async ({ request, url }) => {
		const body = await request.formData();
		const { telegram_send_id, telegram_receive_id } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		const type = body.get('type') as TTelegram;
		if (!telegramTypeArr.includes(type) || !telegram_send_id || !telegram_receive_id)
			return fail(400, { message: 'invalid type' });
		const validate_telegram = await db.query.telegram.findFirst({
			where: eq(telegram.type, type)
		});
		if (validate_telegram) {
			await db
				.update(telegram)
				.set({
					telegram_send_id: telegram_send_id,
					telegram_receive_id: telegram_receive_id
				})
				.where(eq(telegram.id, validate_telegram.id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		} else {
			await db
				.insert(telegram)
				.values({
					type: type,
					telegram_send_id: telegram_send_id,
					telegram_receive_id: telegram_receive_id
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	}
};
