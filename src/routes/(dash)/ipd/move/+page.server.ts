import { db } from '$lib/server/db';
import {
	product,
	staff,
	visit,
	progressNote,
	activeBed,
	activeDepartment,
	uploads
} from '$lib/server/schemas';
import type { Actions, PageServerLoad } from './$types';
import { and, asc, eq, isNull } from 'drizzle-orm';
import { YYYYMMDD_Format } from '$lib/server/utils';
import { logError } from '$lib/server/utils/telegram';
import { fail, redirect } from '@sveltejs/kit';
import { billingService } from '$lib/server/models';

export const load = (async ({ url, parent }) => {
	await parent();
	const progress_note_id = url.searchParams.get('progress_note_id') ?? '';
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +progress_note_id || 0),
		with: {
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			activeDepartment: true,
			activeBed: {
				with: {
					bed: {
						with: {
							ward: true,
							room: {
								with: {
									department: true,
									product: true
								}
							}
						}
					}
				}
			}
		}
	});
	if (get_progress_note?.status === 'DONE') redirect(303, '/patient/ipd');
	const get_progress_notes = await db.query.progressNote.findMany({
		where: isNull(progressNote.date_checkout),
		with: {
			activeBed: {
				with: {
					bed: {
						with: {
							room: {
								with: {
									product: true
								}
							}
						}
					}
				}
			},
			patient: true
		}
	});
	if (!url.searchParams.has('patient_id')) redirect(303, '/patient/ipd');
	const visit_id = url.searchParams.get('visit_id') ?? '';
	const get_visit = await db.query.visit.findFirst({
		where: eq(visit.id, +visit_id),
		with: {
			vitalSign: true,
			billing: {
				with: {
					charge: true
				}
			}
		}
	});
	const get_departments = await db.query.product.findMany({
		where: eq(product.category_id, 11)
	});
	const get_staffs = await db.query.staff.findMany({
		orderBy: asc(staff.name_latin)
	});
	const get_wards = await db.query.ward.findMany({
		with: {
			room: {
				with: {
					product: true,
					bed: {
						with: {
							room: {
								with: {
									product: true
								}
							},
							ward: true
						}
					}
				}
			}
		}
	});
	const get_beds = await db.query.bed.findMany({
		with: {
			room: {
				with: {
					product: true
				}
			},
			ward: true
		}
	});
	const get_words = await db.query.words.findMany();
	let patient_info;
	if (get_progress_note) {
		patient_info = {
			...get_progress_note?.patient,
			date_checkup: get_progress_note?.date_checkup
		};
	}
	const get_patient_upload = await db.query.uploads.findFirst({
		where: and(
			eq(uploads.related_type, 'patient'),
			eq(uploads.related_id, Number(patient_info?.id) || 0)
		)
	});
	return {
		get_patient: get_progress_note?.patient,
		patient_info: {
			...patient_info,
			uploads: get_patient_upload
		},
		get_staffs,
		get_departments,
		get_words,
		get_wards,
		get_visit,
		get_progress_note,
		get_progress_notes,
		get_beds
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	move_department: async ({ request, locals, url }) => {
		const body = await request.formData();
		const created_at = YYYYMMDD_Format.datetime(new Date());
		const {
			staff_id,
			department_id,
			bed_id,
			active_bed_id,
			patient_id,
			active_department_id,
			progress_note_id,
			remark_
		} = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			progress_note_id: false,
			department_id: false,
			active_bed_id: false,
			bed_id: false,
			sender_id: false,
			patient_id: false
		};
		if (!bed_id) validErr.bed_id = true;
		if (!progress_note_id) validErr.progress_note_id = true;
		if (!active_bed_id) validErr.active_bed_id = true;
		if (!department_id) validErr.department_id = true;
		if (!patient_id) validErr.patient_id = true;
		if (!locals.user?.staff_id) validErr.sender_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		const get_active_department = await db.query.activeDepartment.findFirst({
			where: eq(activeDepartment.id, +active_department_id)
		});
		if (get_active_department?.department_id === +department_id) validErr.department_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		await db
			.update(progressNote)
			.set({
				department_id: +department_id,
				staff_id: staff_id ? +staff_id : null,
				status: 'LOADING'
			})
			.where(eq(progressNote.id, +progress_note_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await db
			.update(activeDepartment)
			.set({
				datetime_out: created_at,
				active: false
			})
			.where(eq(activeDepartment.id, +active_department_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		const create_adepartment: { id: number }[] = await db
			.insert(activeDepartment)
			.values({
				datetime_in: created_at,
				department_id: +department_id,
				remark: remark_,
				progress_note_id: +progress_note_id,
				sender_id: locals.user?.staff_id
			})
			.$returningId()
			.catch((e) => {
				logError({ url, body, err: e });
				return [];
			});
		await db
			.update(activeBed)
			.set({
				datetime_out: created_at
			})
			.where(eq(activeBed.id, +active_bed_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await db
			.insert(activeBed)
			.values({
				datetime_in: created_at,
				bed_id: +bed_id,
				active_department_id: create_adepartment[0].id,
				progress_note_id: +progress_note_id
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await billingService({
			progress_id: +progress_note_id,
			patient_id: +patient_id,
			product_id: +department_id,
			body: body,
			url: url
		});
		redirect(303, '/patient/ipd');
	}
};
