<script lang="ts">
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import type { PageServerData, ActionData } from './$types';
	import Words from '$lib/coms-cu/Words.svelte';
	import AddBedToIpd from '$lib/coms-ipd-opd/AddBedToIPD.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import PatientInfo from '$lib/coms-ipd-opd/PatientInfo.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	let loading = $state(false);
	interface Props {
		data: PageServerData;
		form: ActionData;
	}
	let { data, form }: Props = $props();
	// let etiology = $state(data.get_visit?.etiology || data.get_progress_note?.etiology || '');
	let {
		get_staffs,
		get_patient,
		get_departments,
		get_words,
		get_progress_notes,
		get_wards,
		get_progress_note,
		get_beds,
		patient_info
	} = $derived(data);
	let find_active_bed = $state(data.get_progress_note?.activeBed.find((e) => e.active === true));
	let find_active_department = $state(
		data.get_progress_note?.activeDepartment.find((e) => e.active === true)
	);
	let bed_id: number | null = $state(null);
	let find_bed = $derived(get_beds?.find((e) => e.id === bed_id));
	let department_id: number | null = $state(null);
</script>

<div class="row">
	<div class="col-sm-6">
		<a href="/patient/ipd" class="btn btn-link p-0"
			><i class="fa-solid fa-rotate-left"></i>
			{locale.T('back')}
		</a>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-restroom"></i>
					{locale.T('patient')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/ipd" class="btn btn-link p-0 text-secondary"
					><i class="nav-icon fas fa-procedures"></i>
					{locale.T('ipd')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="#/" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-hospital"></i>
					{locale.T('department')}
				</a>
			</li>
		</ol>
	</div>
</div>
<PatientInfo {patient_info} />
<Words
	name="Symptoms"
	words={get_words.filter((e) => e.type === 'etiology')}
	modal_name_type="etiology"
	category="objective"
/>
<AddBedToIpd bind:department_id bind:bed_id data={{ get_progress_notes, get_wards }} />

<div class="card bg-light mt-3">
	<div class="card-header fs-5">
		<span>#{get_patient?.name_khmer}, </span>
		<span>{get_patient?.name_latin} </span>
	</div>
	<Form method="post" action="?/move_department" bind:loading>
		<div class="card-body">
			<div class="row">
				<div class="col-sm-5">
					<div class="row pb-3">
						<div class="col-sm-2">
							<label for="staff_label" class="col-form-label"> {locale.T('doctor')}</label>
						</div>
						<div class="col">
							<span class="form-control">Dortor's name</span>
						</div>
					</div>
					<div class=" row pb-3">
						<div class="col-sm-2">
							<label for="department_id_label" class="col-form-label">
								{locale.T('department')}</label
							>
						</div>
						<div class="col">
							<span class="form-control"
								>{find_active_bed?.bed?.room?.department?.products ?? ''}</span
							>
						</div>
					</div>
					<div class=" row pb-3">
						<div class="col-sm-2">
							<label for="ward_id_label" class="col-form-label"> {locale.T('bed')}</label>
						</div>
						<div class="col">
							<span class="form-control"
								>{find_active_bed?.bed?.ward?.ward ?? ''},{find_active_bed?.bed?.room?.room ??
									''},{find_active_bed?.bed?.room?.product?.products ?? ''},{find_active_bed?.bed
									?.bed ?? ''}</span
							>
						</div>
					</div>
				</div>

				<div class="col-sm-auto">
					<div class="pb-3">
						<span class="form-control text-bg-light"
							><i class="fa-solid fa-arrow-right-arrow-left"></i> {locale.T('move_to')}
						</span>
					</div>
					<div class="pb-3">
						<span class="form-control text-bg-light"
							><i class="fa-solid fa-arrow-right-arrow-left"></i> {locale.T('move_to')}</span
						>
					</div>
					<div class="pb-3">
						<span class="form-control text-bg-light"
							><i class="fa-solid fa-arrow-right-arrow-left"></i> {locale.T('move_to')}</span
						>
					</div>
				</div>
				<div class="col">
					<div class="row pb-3">
						<div class="col-sm-2">
							<label for="staff" class="col-form-label"> {locale.T('doctor')}</label>
						</div>
						<div class="col">
							<SelectParam
								placeholder={locale.T('select')}
								name="staff_id"
								items={get_staffs.map((e) => ({ id: e.id, name: e.name_latin }))}
							/>
						</div>
					</div>
					<div class="row pb-3">
						<div class="col-sm-2">
							<label for="department_id" class="col-form-label"> {locale.T('department')}</label>
						</div>
						<div class="col">
							<SelectParam
								placeholder={locale.T('select')}
								bind:value={department_id}
								name="department_id"
								items={get_departments.map((e) => ({ id: e.id, name: e.products }))}
							/>
							{#if form?.department_id}
								<div class="text-danger">
									{locale.T('input_data')}
								</div>
							{/if}
						</div>
					</div>
					<div class=" row pb-3">
						<div class="col-sm-2">
							<label for="ward_id" class="col-form-label"> {locale.T('bed')}</label>
						</div>
						<div class="col">
							<input type="hidden" name="bed_id" value={bed_id} />
							<input type="hidden" name="patient_id" value={get_patient?.id} />
							<input type="hidden" name="active_bed_id" value={find_active_bed?.id ?? ''} />
							<input
								type="hidden"
								name="active_department_id"
								value={find_active_department?.id ?? ''}
							/>
							<input type="hidden" name="progress_note_id" value={get_progress_note?.id ?? ''} />
							<button
								disabled={!department_id}
								type="button"
								data-bs-toggle="modal"
								data-bs-target="#add_bed_ipd"
								class="form-control text-start"
								>{#if find_bed}
									{find_bed?.ward?.ward},
									{find_bed?.room?.room}
									{find_bed?.room?.product?.products},
									{find_bed?.bed}
								{:else}
									{locale.T('select')}
								{/if}
							</button>
							{#if form?.bed_id}
								<div class="text-danger">
									{locale.T('input_data')}
								</div>
							{/if}
						</div>
					</div>
				</div>
			</div>
			<!-- <label for="remake">{locale.T('etiology')}</label> -->
			<textarea name="remark_" class="form-control" rows="3" id="remark_"></textarea>
		</div>
		<div class="card-footer text-end">
			<SubmitButton {loading} />
		</div>
	</Form>
</div>
