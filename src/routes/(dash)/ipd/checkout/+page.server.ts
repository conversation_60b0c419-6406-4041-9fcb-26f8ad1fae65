import { YYYYMMDD_Format } from '$lib/server/utils';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions } from './$types';
import { db } from '$lib/server/db';
import { activeBed, activeDepartment, billing, progressNote } from '$lib/server/schemas';
import { eq } from 'drizzle-orm';
import { logError } from '$lib/server/utils/telegram';
import { createProductOrder, deleteProductOrder } from '$lib/server/models';
import { pushAmountOPDToIPD } from '$lib/server/models/calulatorBillingIPD';

export const actions: Actions = {
	check_out: async ({ request, url }) => {
		const body = await request.formData();
		const { progress_note_id, active_department_id, active_bed_id } = Object.fromEntries(
			body
		) as Record<string, string>;
		if (!progress_note_id || !active_department_id || !active_bed_id)
			return fail(400, { progress_note_id: true });
		const get_progress_note = await db.query.progressNote.findFirst({
			where: eq(progressNote.id, +progress_note_id),
			with: {
				visit: {
					with: {
						billing: true
					}
				},
				billing: {
					with: {
						charge: true
					}
				},
				activeBed: {
					with: {
						bed: {
							with: {
								room: {
									with: {
										product: true
									}
								}
							}
						}
					}
				},
				activeDepartment: {
					with: {
						department: true
					}
				},
				presrciption: true,
				document: true
			}
		});
		const charge_on_bed = get_progress_note?.billing?.charge.find((e) => e.charge_on === 'bed');
		const status = get_progress_note?.billing?.status === 'checking' ? 'paying' : 'checking';
		let handle_errr = false;
		try {
			await db.transaction(async (tx) => {
				await tx
					.update(progressNote)
					.set({
						date_checkout: YYYYMMDD_Format.datetime(new Date()),
						status: status === 'checking' ? 'CHECKING' : 'DONE'
					})
					.where(eq(progressNote.id, +progress_note_id));

				await tx
					.update(activeBed)
					.set({
						datetime_out: YYYYMMDD_Format.datetime(new Date())
					})
					.where(eq(activeBed.id, +active_bed_id));
				await tx
					.update(activeDepartment)
					.set({
						datetime_out: YYYYMMDD_Format.datetime(new Date())
					})
					.where(eq(activeDepartment.id, +active_department_id));
				await tx
					.update(billing)
					.set({
						status: 'paying',
						created_at: YYYYMMDD_Format.datetime(new Date())
					})
					.where(eq(billing.id, Number(get_progress_note?.billing?.id)));
			});
		} catch (e) {
			if (e) {
				handle_errr = true;
				logError({ url, body, err: e });
			}
		}
		if (handle_errr === false) {
			if (charge_on_bed?.id) {
				for (const e of get_progress_note?.activeBed || []) {
					if (e?.bed?.room?.product_id) {
						await createProductOrder({
							charge_id: +charge_on_bed?.id,
							price: Number(e?.bed?.room?.product?.price),
							product_id: e?.bed?.room?.product_id,
							qty: 1,
							body: body,
							url: url
						});
					}
				}
			}
			await pushAmountOPDToIPD(+progress_note_id);
		}
		if (!get_progress_note?.document.some((e) => e.title === 'accept_leaving'))
			redirect(302, `/ipd/${progress_note_id}/document?title=accept_leaving`);
	},
	dis_check_out: async ({ request }) => {
		const body = await request.formData();
		const { progress_note_id, active_department_id, active_bed_id } = Object.fromEntries(
			body
		) as Record<string, string>;

		if (!progress_note_id || !active_department_id || !active_bed_id)
			return fail(400, { progress_note_id: true });
		const get_progress_note = await db.query.progressNote.findFirst({
			where: eq(progressNote.id, +progress_note_id),
			with: {
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				},
				activeBed: {
					with: {
						bed: {
							with: {
								room: {
									with: {
										product: true
									}
								}
							}
						}
					}
				},
				activeDepartment: {
					with: {
						department: true
					}
				}
			}
		});
		const charge_on_bed = get_progress_note?.billing?.charge.find((e) => e.charge_on === 'bed');
		await db.transaction(async (tx) => {
			await tx
				.update(progressNote)
				.set({
					date_checkout: null,
					status: 'CHECKING',
					inclund_pay: null
				})
				.where(eq(progressNote.id, +progress_note_id));

			await tx
				.update(activeBed)
				.set({
					datetime_out: null
				})
				.where(eq(activeBed.id, +active_bed_id));
			await tx
				.update(activeDepartment)
				.set({
					datetime_out: null
				})
				.where(eq(activeDepartment.id, +active_department_id));
			await tx
				.update(billing)
				.set({
					status: 'checking'
				})
				.where(eq(billing.id, Number(get_progress_note?.billing?.id)));
		});

		if (charge_on_bed?.id) {
			for (const e of charge_on_bed?.productOrder || []) {
				if (e?.id) {
					await deleteProductOrder(e.id);
				}
			}
		}
	}
};
