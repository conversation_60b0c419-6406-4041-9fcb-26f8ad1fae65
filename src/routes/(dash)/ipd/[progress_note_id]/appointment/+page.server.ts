import { db } from '$lib/server/db';
import { appointment } from '$lib/server/schemas';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { eq } from 'drizzle-orm';
import { logError } from '$lib/server/utils/telegram';
import { words } from '$lib/server/schemas/words';

export const load = (async ({ params }) => {
	const { progress_note_id } = params;
	const get_appointment = await db.query.appointment.findFirst({
		where: eq(appointment.progress_note_id, +progress_note_id)
	});
	const get_words = await db.query.words.findMany({
		where: eq(words.category, 'appointment')
	});
	return {
		get_appointment,
		get_words
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_appointment: async ({ request, params, url }) => {
		const { progress_note_id } = params;
		const body = await request.formData();
		const { datetime, description } = Object.fromEntries(body) as Record<string, string>;
		const get_appointment = await db.query.appointment.findFirst({
			where: eq(appointment.progress_note_id, +progress_note_id)
		});
		if (!datetime) return fail(400, { datetimeErr: true });
		if (!get_appointment) {
			await db
				.insert(appointment)
				.values({
					datetime: datetime,
					description: description,
					progress_note_id: +progress_note_id
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		if (get_appointment) {
			await db
				.update(appointment)
				.set({
					datetime: datetime,
					description: description
				})
				.where(eq(appointment.progress_note_id, +progress_note_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		redirect(303, '?');
	},
	delete_appointment: async ({ request, url }) => {
		const id = (await request.formData()).get('id');
		const body = await request.formData();
		if (!id || isNaN(+id)) return fail(400, { idErr: true });
		await db
			.delete(appointment)
			.where(eq(appointment.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
