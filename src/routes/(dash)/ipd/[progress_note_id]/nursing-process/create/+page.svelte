<script lang="ts">
	import { invalidateAll } from '$app/navigation';
	import Words from '$lib/coms-cu/Words.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import SelectWords from '$lib/coms/SelectWords.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_nursing_process, progress_note_id, get_words } = $derived(data);
	let loading = $state(false);
	let datetime = $state('');
	let accessment = $state('');
	let health_problems = $state('');
	let actions = $state('');
	let evolution = $state('');
	$effect(() => {
		if (get_nursing_process) {
			datetime = get_nursing_process?.datetime ?? '';
			accessment = get_nursing_process?.accessment ?? '';
			health_problems = get_nursing_process?.health_problems ?? '';
			actions = get_nursing_process?.actions ?? '';
			evolution = get_nursing_process?.evolution ?? '';
		} else {
			datetime = '';
			accessment = '';
			health_problems = '';
			actions = '';
			evolution = '';
		}
	});
	let words_accessment = $derived(get_words.filter((e) => e.type === 'accessment'));
	let words_health_problems = $derived(get_words.filter((e) => e.type === 'health_problems'));
	let words_actions = $derived(get_words.filter((e) => e.type === 'actions'));
	let words_evolution = $derived(get_words.filter((e) => e.type === 'evolution'));
</script>

<Words
	name="Accessment"
	words={words_accessment}
	modal_name_type="accessment"
	category="objective"
/>
<Words
	name="Health Problems"
	words={words_health_problems}
	modal_name_type="health_problems"
	category="objective"
/>
<Words name="Actions" words={words_actions} modal_name_type="actions" category="objective" />
<Words name="Evolution" words={words_evolution} modal_name_type="evolution" category="objective" />
<Form
	action="/ipd/{progress_note_id}/nursing-process/?/create_nursing_process"
	method="post"
	reset={false}
	bind:loading
	fnSuccess={() => invalidateAll()}
>
	<div class="card bg-light">
		<input type="hidden" value={get_nursing_process?.id || ''} name="get_nursing_process.id" />
		<div class="card-header fs-5">
			<div class="row">
				<div class="col">
					<a
						aria-label="nersing_process"
						href="/ipd/{progress_note_id}/nursing-process"
						class="btn btn-link m-0 p-0"
						><i class="fa-solid fa-rotate-left"></i> {locale.T('back')}
					</a>
					<!-- <span># {locale.T('nursing_process')}</span> -->
				</div>
				<div class="col-auto"></div>
			</div>
		</div>
		<div class="modal-body">
			<div class="card-body">
				{#if get_nursing_process?.id}
					<input value={datetime || ''} type="hidden" name="date" />
					<input value={get_nursing_process?.id} type="hidden" name="nursing_process_id" />
					<div class=" row pb-3">
						<div class="col-sm-3">
							<button type="button" class="btn btn-outline-primary btn-sm"
								>{locale.T('time')}</button
							>
						</div>

						<div class="col-sm-9">
							<div class="input-group">
								<input
									value={datetime?.slice(11, 16)}
									id="time"
									name="time"
									type="time"
									class="form-control"
								/>
							</div>
						</div>
					</div>
				{/if}
				<div class=" row pb-2">
					<div class="col-sm-3">
						<button
							data-bs-toggle="modal"
							data-bs-target="#accessment"
							type="button"
							class="btn btn-outline-primary btn-sm">ការប៉ាន់ប្រមាណ</button
						>
					</div>
					<div class="col-sm-9">
						<SelectWords
							name="accessment"
							value={accessment}
							words={words_accessment?.map((e) => e.text)}
							type="textarea"
							rows="3"
						/>
					</div>
				</div>

				<div class=" row pb-3">
					<div class="col-sm-3">
						<button
							data-bs-toggle="modal"
							data-bs-target="#health_problems"
							type="button"
							class="btn btn-outline-primary btn-sm">បញ្ហាសុខភាពដែលត្រូវថែទាំ</button
						>
					</div>
					<div class="col-sm-9">
						<div class="input-group">
							<SelectWords
								name="health_problems"
								value={health_problems}
								words={words_health_problems?.map((e) => e.text)}
								type="textarea"
								rows="3"
							/>
						</div>
					</div>
				</div>
				<div class=" row pb-3">
					<div class="col-sm-3">
						<button
							data-bs-toggle="modal"
							data-bs-target="#actions"
							type="button"
							class="btn btn-outline-primary btn-sm">សកម្មភាពដែលបានអនុវត្តន៍</button
						>
					</div>
					<div class="col-sm-9">
						<div class="input-group">
							<SelectWords
								name="actions"
								value={actions}
								words={words_actions?.map((e) => e.text)}
								type="textarea"
								rows="3"
							/>
						</div>
					</div>
				</div>

				<div class=" row pb-3">
					<div class="col-sm-3">
						<button
							data-bs-toggle="modal"
							data-bs-target="#evolution"
							type="button"
							class="btn btn-outline-primary btn-sm">ការវាយតម្លៃ</button
						>
					</div>
					<div class="col-sm-9">
						<div class="input-group">
							<SelectWords
								rows="3"
								name="evolution"
								value={evolution}
								words={words_evolution?.map((e) => e.text)}
								type="textarea"
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="card-footer float-end text-end">
			<SubmitButton {loading} />
		</div>
	</div>
</Form>
