<script lang="ts">
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import Name from '$lib/coms/Name.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let nursing_process_id: number = $state(0);
	let { progress_note_id, get_progress_note } = $derived(data);
</script>

<DeleteModal action="?/delete_nursing_process" id={nursing_process_id} />

<div class="card bg-light">
	<div class="card-header fs-5">
		<div class="row">
			<div class="col">
				<span># {locale.T('nursing_process')}</span>
			</div>
			<div class="col-auto">
				<a
					target="_blank"
					aria-label="nersing_process"
					href="/print/{progress_note_id}/nursing-process"
					class="btn btn-success btn-sm"
					><i class="fa-solid fa-print"></i>
				</a>
				<a
					aria-label="nersing_process"
					href="/ipd/{progress_note_id}/nursing-process/create"
					class="btn btn-success btn-sm"
					><i class="fa-solid fa-square-plus"></i>
				</a>
			</div>
		</div>
	</div>
	<div class="card-body table-responsive p-0">
		<table class="table mb-0 table-bordered table-hover text-wrap text-break table-light">
			<thead class="table-active">
				<tr class="text-center">
					<th style="width: 7%;">{locale.T('date')}</th>
					<th style="width: 7%;">{locale.T('time')}</th>
					<th style="width: 15.8%;">ការប៉ាន់ប្រមាណ</th>
					<th style="width: 15.8%;">បញ្ហាសុខភាពដែលត្រូវថែទាំ</th>
					<th style="width: 15.8%;">សកម្មភាពដែលបានអនុវត្តន៍</th>
					<th style="width: 15.8%;">ការវាយតម្លៃ</th>
					<th style="width: 15.8%;">{locale.T('nursing_sign')}</th>
					<th style="width: 7%;"></th>
				</tr>
			</thead>
			<tbody>
				{#each get_progress_note?.activeDepartment || [] as item}
					<tr class="text-center table-active">
						<th colspan="8">{locale.T('department')} {item.department.products ?? ''}</th>
					</tr>
					{#each get_progress_note?.nursingProcess || [] as item_1}
						{#if item_1.active_department_id === item.id}
							<tr class="text-center">
								<td>
									<DDMMYYYYFormat style="date" date={item_1.datetime} />
								</td>
								<td>
									<DDMMYYYYFormat style="time" date={item_1.datetime} />
								</td>
								<td>{item_1.accessment ?? ''}</td>
								<td>{item_1.health_problems ?? ''}</td>
								<td>{item_1.actions ?? ''} </td>
								<td>{item_1.evolution ?? ''}</td>
								<td>
									<Name name={item_1.nursingSign} />
								</td>
								<td>
									<div>
										<a
											aria-label="createnursingprocess"
											class="btn btn-primary btn-sm"
											href="/ipd/{progress_note_id}/nursing-process/create/?nursing_process_id={item_1.id}"
											><i class="fa-solid fa-file-pen"></i>
										</a>
										<button
											aria-label="deletemodal"
											class="btn btn-danger btn-sm"
											type="button"
											data-bs-toggle="modal"
											data-bs-target="#delete_modal"
											onclick={() => {
												nursing_process_id = item_1.id;
											}}
											><i class="fa-solid fa-trash-can"></i>
										</button>
									</div>
								</td>
							</tr>
						{/if}
					{/each}
				{/each}
			</tbody>
		</table>
	</div>
</div>
