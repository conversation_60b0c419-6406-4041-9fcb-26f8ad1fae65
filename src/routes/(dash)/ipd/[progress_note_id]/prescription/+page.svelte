<script lang="ts">
	import type { ActionData, PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import PastePrescription from '$lib/coms-ipd-opd/PastePrescription.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import CreateAWord from '$lib/coms-cu/CreateAWord.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import { page } from '$app/state';
	import Words from '$lib/coms-cu/Words.svelte';
	import SelectWords from '$lib/coms/SelectWords.svelte';
	interface Props {
		data: PageServerData;
		form: ActionData;
	}

	let { data, form }: Props = $props();
	let loading = $state(false);
	let {
		get_categories,
		get_uses,
		get_prescriptions,
		get_prescription,
		get_durations,
		get_advice_teaching,
		get_groups,
		get_products,
		get_words
	} = $derived(data);
	let prescription_id: number = $derived(get_prescription?.id || 0);
	let product_id: number | null = $derived(get_prescription?.product_id || null);
	let category_id: number | null = $state(null);
	let group_id: number | null = $state(null);
	let get_product = $derived(get_products.find((e) => e.id === product_id));
	$effect(() => {
		if (get_prescription) {
			group_id = get_prescription?.product?.group_id || null;
			category_id = get_prescription?.product?.category_id || null;
		}
		if (page.url.searchParams.get('product_id')) {
			product_id = Number(page.url.searchParams.get('product_id'));
		}
	});
	let description = $derived(get_advice_teaching?.description ?? '');
</script>

<DeleteModal action="?/delete_prescription" id={prescription_id!} />
<CreateAWord
	title={locale.T('use')}
	modal="create_use"
	actionCreate="?/create_use"
	actionDelete="?/delete_use"
	data={get_uses.map((e) => ({ id: e.id, word: e?.description }))}
/>
<CreateAWord
	title={locale.T('duration')}
	modal="create_duration"
	actionCreate="?/create_duration"
	actionDelete="?/delete_duration"
	data={get_durations.map((e) => ({ id: e.id, word: e?.description }))}
/>
<Words
	name="Advice teaching"
	words={get_words}
	modal_name_type="advice_teaching"
	category="prescription"
/>

<div class="table table-responsive">
	<table class="table table-bordered table-hover text-nowrap table-light">
		<thead>
			<tr class="table-secondary">
				<td colspan="7">
					<div class="row g-1">
						<div class="col fs-5">
							<span># {locale.T('prescription')}</span>
						</div>
						<div class="col-auto">
							<div class="row g-1">
								<div class="col">
									{#if get_prescriptions?.length}
										<a
											target="_blank"
											aria-label="nersing_process"
											href="/report/{get_prescriptions[0]
												?.progress_note_id}/prescription?visit_type=ipd"
											class="btn btn-success btn-sm"
											><i class="fa-solid fa-print"></i>
										</a>
									{/if}
								</div>
								<div class="col">
									<PastePrescription action="?/paste_prescription" />
								</div>
							</div>
						</div>
					</div>
				</td>
			</tr>
			<tr class:table-primary={get_prescription?.id}>
				<th colspan="7">
					<Form
						action={get_prescription?.id ? '?/update_prescription' : '?/create_prescription'}
						bind:loading
						fnSuccess={() => (product_id = null)}
						method="post"
					>
						<input value={get_prescription?.id || ''} type="hidden" name="prescription_id" />
						<div class="row g-2 pb-3">
							<div class="col-sm-2">
								<label for="">{locale.T('category')}</label>
								<SelectParam
									placeholder={locale.T('select')}
									name="category_id"
									bind:value={category_id}
									items={get_categories.map((e) => ({ id: e.id, name: e.name }))}
								/>
							</div>
							<div class="col-sm-2">
								<label for="">{locale.T('product_group')}</label>
								<SelectParam
									placeholder={locale.T('select')}
									name="group_id"
									bind:value={group_id}
									items={get_groups.map((e) => ({ id: e.id, name: e.name }))}
								/>
							</div>
							<div class="col-sm-3">
								<label for="">{locale.T('products')}</label>
								<SelectParam
									q_name="q"
									name="product_id"
									placeholder={locale.T('select')}
									bind:value={product_id}
									items={get_products?.map((e) => ({
										id: e.id,
										name: e.products.concat(`
							<span style="float: inline-end;" class="${e.qty_available ? 'text-success' : 'text-danger'}" >
										${e.qty_available} 
										${e.unit?.unit ?? ''} 
										<span/>
										`)
									}))}
								/>
								{#if form?.product_id}
									<div class="text-danger">{locale.T('input_data')}</div>
								{/if}
							</div>
							<div class="col-5">
								<div class="row g-1">
									<div class="col-lg">
										<label for="">{locale.T('morning')}</label>
										<input
											value={get_prescription?.morning === 0
												? ''
												: (get_prescription?.morning ?? '')}
											id="morning"
											name="morning"
											type="number"
											step="any"
											class="form-control"
										/>
									</div>
									<div class="col-lg">
										<label for="">{locale.T('noon')}</label>
										<input
											value={get_prescription?.noon === 0 ? '' : (get_prescription?.noon ?? '')}
											id="noon"
											name="noon"
											step="any"
											type="number"
											class="form-control"
										/>
									</div>
									<div class="col-lg">
										<label for="">{locale.T('afternoon')}</label>
										<input
											value={get_prescription?.afternoon === 0
												? ''
												: (get_prescription?.afternoon ?? '')}
											id="afternoon"
											name="afternoon"
											step="any"
											type="number"
											class="form-control"
										/>
									</div>
									<div class="col-lg">
										<label for="">{locale.T('evening')}</label>
										<input
											value={get_prescription?.evening === 0
												? ''
												: (get_prescription?.evening ?? '')}
											id="evening"
											step="any"
											name="evening"
											type="number"
											class="form-control"
										/>
									</div>
									<div class="col-lg">
										<label for="">{locale.T('night')}</label>
										<input
											value={get_prescription?.night === 0 ? '' : (get_prescription?.night ?? '')}
											id="night"
											step="any"
											name="night"
											type="number"
											class="form-control"
										/>
									</div>
								</div>
							</div>
						</div>
						<div class="row g-2">
							<div class="col-3">
								<div class="row g-1">
									<div class="col-auto">
										<button
											aria-label="createuse"
											type="button"
											data-bs-toggle="modal"
											data-bs-target="#create_use"
											onclick={() => document.getElementById('close_create_prescription')?.click()}
											class=" btn btn-outline-primary"
											>{locale.T('use')}
										</button>
									</div>
									<div class="col">
										<SelectParam
											placeholder={locale.T('select')}
											name="use"
											value={get_prescription?.use ?? ''}
											items={get_uses.map((e) => ({ id: e.description, name: e.description }))}
										/>
									</div>
								</div>
							</div>
							<div class="col-sm-3">
								<div class="row g-1">
									<div class="col-auto">
										<button
											aria-label="createduration"
											type="button"
											data-bs-toggle="modal"
											data-bs-target="#create_duration"
											class="btn btn-outline-primary"
										>
											{locale.T('duration')}
										</button>
									</div>
									<div class="col-md">
										<SelectParam
											placeholder={locale.T('select')}
											name="duration"
											value={get_prescription?.duration ?? ''}
											items={get_durations.map((e) => ({
												id: e.description,
												name: e.description
											}))}
										/>
									</div>
								</div>
							</div>
							<div class="col-sm-4">
								<div class="input-group">
									<label for="Amount" class="input-group-text">{locale.T('amount')}</label>
									<input
										value={get_prescription?.amount ?? ''}
										id="Amount"
										name="amount"
										type="number"
										class="form-control"
									/>
									<select
										required
										value={get_prescription?.unit_id?.toString() || ''}
										class="form-control"
										name="unit_id"
										id="unit_id"
									>
										<option value={String(get_product?.unit_id) || ''}
											>{get_product?.unit?.unit ?? locale.T('not_selected')}
										</option>
										{#each get_product?.subUnit || [] as item}
											<option value={String(item.unit_id)}>{item.unit.unit ?? ''}</option>
										{/each}
									</select>
								</div>
							</div>
							<div class="col-sm-2">
								<button type="submit" class="form-control text-bg-primary border-0">
									{#if get_prescription}
										{locale.T('update')}
									{:else}
										{locale.T('save')}
									{/if}
								</button>
							</div>
						</div>
					</Form>
				</th>
			</tr>
			<tr class="table-active">
				<th>{locale.T('n')}</th>
				<th>{locale.T('medicine')}</th>
				<th>{locale.T('use')}</th>
				<th>{locale.T('time_to_use')}</th>
				<th class="text-center">{locale.T('duration')}</th>
				<th class="text-center">{locale.T('amount')}</th>
				<th></th>
			</tr>
		</thead>
		<tbody class="table-sm">
			{#each get_prescriptions as item, index}
				<tr
					class:table-active={item.id === prescription_id}
					class:table-primary={item.product_id === product_id}
				>
					<td class="text-center">{index + 1}</td>
					<td
						>{item.product?.products} <br />
						<span class="badge text-bg-primary">{item.product?.generic_name ?? ''}</span>
					</td>

					<td>
						{item.use ?? ''}
					</td>
					<td>
						<div>
							<span class="badge text-bg-warning">
								{#if item.morning !== 0}
									{locale.T('morning')} {item.morning}
								{/if}
							</span>
							<span class="badge text-bg-warning">
								{#if item.noon !== 0}
									{locale.T('noon')} {item.noon}
								{/if}
							</span>
							<span class="badge text-bg-warning">
								{#if item.afternoon !== 0}
									{locale.T('afternoon')} {item.afternoon}
								{/if}
							</span>
							<span class="badge text-bg-warning">
								{#if item.evening !== 0}
									{locale.T('evening')} {item.evening}
								{/if}
							</span>
							<span class="badge text-bg-warning">
								{#if item.night !== 0}
									{locale.T('night')} {item.night}
								{/if}
							</span>
						</div>
					</td>
					<td class="text-center">{item.duration ?? ''}</td>
					<td class="text-center">{item.amount} {item.unit?.unit ?? ''} </td>
					<td>
						<div>
							{#if prescription_id === item.id}
								<a
									data-sveltekit-noscroll
									href={`?group_id=${item.product?.group_id}&category_id=${item.product?.category_id}`}
									aria-label="createprescription"
									class="btn btn-primary btn-sm"
									><i class="fa-solid fa-file-pen"></i>
								</a>
							{:else}
								<a
									data-sveltekit-noscroll
									href={`?group_id=${item.product?.group_id}&category_id=${item.product?.category_id}&prescription_id=${item.id}`}
									aria-label="createprescription"
									class="btn btn-primary btn-sm"
									><i class="fa-solid fa-file-pen"></i>
								</a>
							{/if}
							<button
								aria-label="deletemodal"
								onclick={() => {
									prescription_id = item.id;
								}}
								type="button"
								class="btn btn-danger btn-sm"
								data-bs-toggle="modal"
								data-bs-target="#delete_modal"
								><i class="fa-solid fa-trash-can"></i>
							</button>
						</div>
					</td>
				</tr>
			{/each}
			<tr>
				<td colspan="7">
					# {locale.T('advice_or_teaching')}
				</td>
			</tr>
		</tbody>
		<thead>
			<tr>
				<td colspan="7">
					<Form
						data_sveltekit_keepfocus={true}
						bind:loading
						action="?/create_advice_teaching"
						method="post"
					>
						<button
							data-bs-toggle="modal"
							data-bs-target="#advice_teaching"
							type="button"
							class="btn btn-outline-primary btn-sm mb-2">{locale.T('description')}</button
						>

						<SelectWords
							name="advice_teaching"
							rows="4"
							type="textarea"
							value={description}
							words={get_words.map((e) => e.text)}
						/>

						<div class="pt-2 text-end">
							<SubmitButton {loading} />
						</div>
					</Form>
				</td>
			</tr>
		</thead>
	</table>
</div>
