<script lang="ts">
	import ConfirmSubmit from '$lib/coms-form/ConfirmSubmit.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Name from '$lib/coms/Name.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_progress_note, get_rooms, get_progress_notes, get_progress_note_as_patient } =
		$derived(data);
	let get_active_departments = $derived(get_progress_note?.activeDepartment);
</script>

<div class="card bg-light">
	<div class="card-header fs-5">
		<span># Discharge summary</span>
	</div>

	<div class="card-body">
		<div class=" row pb-3">
			<div class="col-sm-6">
				<h5 class="card-title text-primary text-decoration-underline">
					{locale.T('present_history')}
				</h5>
				<ul class="list-group">
					{#each get_active_departments || [] as item, index}
						{@const get_active_bed = item.activeBed.find((e) => e.active === true)}
						{@const get_free_bed = get_rooms.filter((e) => e.department_id === item.department_id)}

						<li
							class:bg-primary-subtle={item.datetime_out === null}
							class="list-group-item d-flex justify-content-between align-items-start"
						>
							<div class="ms-2 me-auto">
								<div class="fw-bold">
									{Number(get_active_departments?.length) - index}.
									{locale.T('date_checkin')}
									<DDMMYYYYFormat date={item.datetime_in} />
									{#if item.datetime_out}
										-
										{locale.T('date_checkout')}
										<DDMMYYYYFormat date={item.datetime_out} />
									{/if}
								</div>
								<table>
									<thead>
										<tr>
											<td
												><button class="btn btn-sm btn-link text-decoration-none py-0">
													{locale.T('department')}
												</button></td
											>
											<td>:</td>
											<td>
												{item?.department?.products ?? ''}
											</td>
										</tr>

										<tr>
											<td>
												<button class="btn btn-sm btn-link py-0 text-decoration-none">
													{locale.T('sender')}
												</button>
											</td>
											<td>:</td>
											<td>
												<Name name={item?.sender} />
											</td>
										</tr>
										<tr>
											<td>
												<button class="btn btn-sm btn-link py-0 text-decoration-none">
													{locale.T('getter')}
												</button>
											</td>
											<td>:</td>
											<td>
												<Name name={item?.getter} />
											</td>
										</tr>
										<tr>
											<td>
												<button class="btn btn-sm btn-link py-0 text-decoration-none">
													{locale.T('remark')}
												</button>
											</td>
											<td>:</td>
											<td>
												<i>
													{item?.remark}
												</i>
											</td>
										</tr>
										<tr>
											<td>
												<ConfirmSubmit
													disabled={get_active_bed?.datetime_out !== null}
													class="btn btn-link btn-sm"
													action="?/update_bed"
													header={locale.T('chang_bed')}
													name={`${locale.T('room')} - ${locale.T('bed')}`}
												>
													<input type="hidden" name="active_bed_id" value={get_active_bed?.id} />
													{#if item.datetime_out === null}
														<input type="hidden" name="active_department_id" value={item.id} />
													{/if}
													<div class="col">
														<select class="form-control" name="bed_id" id="bed_id">
															<option value="">{locale.T('not_selected')}</option>
															{#each get_free_bed as iitem}
																<optgroup label={iitem?.room ?? ''}>
																	{#each iitem.bed || [] as iiitem}
																		<option
																			value={iiitem.id}
																			disabled={get_progress_notes.some(
																				(e) => e.activeBed[0].bed_id === iiitem.id
																			)
																				? true
																				: false}>{iiitem.bed}</option
																		>
																	{/each}
																</optgroup>
															{/each}
														</select>
													</div>
												</ConfirmSubmit>
											</td>
											<td>:</td>
											<td>
												{get_active_bed?.bed?.ward?.ward},{get_active_bed?.bed?.room?.room ??
													''},{get_active_bed?.bed?.bed ?? ''}
											</td>
										</tr>
									</thead>
								</table>
							</div>
						</li>
					{/each}
				</ul>
			</div>
			<div class="col-sm-6">
				<h5 class="card-title text-primary text-decoration-underline">
					{locale.T('past_history')}
				</h5>
				<ul class="list-group">
					{#each get_progress_note_as_patient as item, index}
						<li
							class="list-group-item d-flex justify-content-between align-items-start"
							aria-current="true"
						>
							<div class="ms-2 me-auto">
								<div class="fw-bold">
									{get_progress_note_as_patient?.length - index}.
									{locale.T('date_checkin')}
									<DDMMYYYYFormat date={item?.date_checkup} />
									{#if item?.date_checkout}
										-
										{locale.T('date_checkout')}
										<DDMMYYYYFormat date={item?.date_checkout} />
									{/if}
								</div>
								<table>
									<thead>
										<tr>
											<td
												><button class="btn btn-sm btn-link text-decoration-none py-0">
													{locale.T('department')}
												</button></td
											>
											<td>:</td>
											<td>{item?.department?.products ?? ''}</td>
										</tr>
										<tr>
											<td>
												<button class="btn btn-sm btn-link py-0 text-decoration-none py-0">
													{locale.T('symptoms')}
												</button>
											</td>
											<td>:</td>
											<td>
												<i>
													{item?.etiology}
												</i>
											</td>
										</tr>
										<tr>
											<td>
												<button class="btn btn-link btn-sm text-decoration-none py-0">
													{locale.T('diagnosis')}
												</button>
											</td>
											<td>:</td>
											<td>{item.accessment?.diagnosis_or_problem}</td>
										</tr>
									</thead>
								</table>
							</div>
							<a target="_blank" href="/ipd/{item.id}/progress-note" class="text-primary">
								{locale.T('view')}
							</a>
						</li>
					{/each}
				</ul>
			</div>
		</div>
	</div>
</div>
