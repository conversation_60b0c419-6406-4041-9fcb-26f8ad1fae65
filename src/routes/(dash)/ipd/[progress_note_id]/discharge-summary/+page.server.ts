import { db } from '$lib/server/db';
import { activeBed, activeDepartment, progressNote } from '$lib/server/schemas';
import { desc, eq, isNull } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { fail } from '@sveltejs/kit';
import { logError } from '$lib/server/utils/telegram';
import { YYYYMMDD_Format } from '$lib/server/utils';

export const load = (async ({ params }) => {
	const { progress_note_id } = params;
	const get_rooms = await db.query.room.findMany({
		with: {
			product: true,
			bed: {
				with: {
					room: {
						with: {
							product: true
						}
					},
					ward: true
				}
			}
		}
	});
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +progress_note_id),
		with: {
			activeDepartment: {
				with: {
					department: true,
					sender: true,
					getter: true,
					activeBed: {
						with: {
							bed: {
								with: {
									ward: true,
									room: {
										with: {
											product: true
										}
									}
								}
							}
						},
						orderBy: desc(activeBed.datetime_in)
					}
				},
				orderBy: desc(activeDepartment.datetime_in)
			},
			activeBed: {
				with: {
					bed: {
						with: {
							ward: true,
							room: {
								with: {
									product: true
								}
							}
						}
					}
				}
			}
		}
	});
	const get_progress_notes = await db.query.progressNote.findMany({
		where: isNull(progressNote.date_checkout),
		with: {
			patient: true,
			activeBed: {
				where: isNull(activeBed.datetime_out)
			}
		}
	});
	const get_progress_note_as_patient = await db.query.progressNote.findMany({
		where: eq(progressNote.patient_id, get_progress_note!.patient_id),
		with: {
			staff: true,
			department: true,
			accessment: true
		},
		orderBy: desc(progressNote.date_checkup)
	});
	return {
		get_progress_note,
		get_rooms,
		get_progress_notes,
		get_progress_note_as_patient
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	update_bed: async ({ request, params, url }) => {
		const body = await request.formData();
		const { progress_note_id } = params;
		const { active_bed_id, bed_id, active_department_id } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		if (!active_bed_id || !bed_id || !active_department_id || !progress_note_id)
			return fail(400, { changeBedErr: true });
		await db
			.update(activeBed)
			.set({ datetime_out: YYYYMMDD_Format.datetime(new Date()), active: false })
			.where(eq(activeBed.id, +active_bed_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await db
			.insert(activeBed)
			.values({
				datetime_in: YYYYMMDD_Format.datetime(new Date()),
				active_department_id: +active_department_id,
				bed_id: +bed_id,
				progress_note_id: +progress_note_id
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
