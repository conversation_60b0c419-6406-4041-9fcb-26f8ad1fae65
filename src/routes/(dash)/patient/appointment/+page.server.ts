import { db } from '$lib/server/db';
import { appointment, patient, progressNote, visit } from '$lib/server/schemas';
import type { Actions, PageServerLoad } from './$types';
import { and, desc, eq, inArray, like, or } from 'drizzle-orm';
import { betweenHelper, pagination } from '$lib/server/utils';
import { logError } from '$lib/server/utils/telegram';
export const load = (async ({ parent, url }) => {
	await parent();
	const q = url.searchParams.get('q') ?? '';
	const patient_id = Number(url.searchParams.get('patient_id'));
	const get_visit = await db.query.visit.findMany({
		where: and(eq(visit.patient_id, +patient_id)),
		columns: {
			id: true
		}
	});
	const get_progress_notes = await db.query.progressNote.findMany({
		where: eq(progressNote.patient_id, +patient_id),
		columns: {
			id: true
		}
	});
	const visit_ids = get_visit.map((v) => v.id);
	const progress_note_ids = get_progress_notes.map((v) => v.id);
	const get_appoinments = await db.query.appointment.findMany({
		where: and(
			// eq(visit.transfer, true),
			betweenHelper(url, appointment.datetime),
			patient_id
				? or(
						inArray(appointment.visit_id, visit_ids),
						inArray(appointment.progress_note_id, progress_note_ids)
					)
				: undefined
		),
		with: {
			visit: {
				with: {
					department: true,
					patient: true,
					staff: true
				}
			},
			progressNote: {
				with: {
					patient: true,
					staff: true,
					department: true
				}
			}
		},
		orderBy: desc(appointment.datetime),
		...pagination(url)
	});
	const count = await db.$count(
		appointment,
		and(
			// eq(visit.transfer, true),
			betweenHelper(url, appointment.datetime),
			patient_id
				? or(
						inArray(appointment.visit_id, visit_ids),
						inArray(appointment.progress_note_id, progress_note_ids)
					)
				: undefined
		)
	);
	const get_patients = await db.query.patient.findMany({
		where: or(
			like(patient.name_latin, `%${q}%`),
			like(patient.name_khmer, `%${q}%`),
			like(patient.telephone, `%${q}%`),
			like(patient.id, `%${q}%`)
		),
		limit: 200
	});
	return {
		get_appoinments,
		get_patients,
		items: count
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	reject: async ({ request, url }) => {
		const body = await request.formData();
		const appointment_id = Number(body.get('id'));
		const get_appointment = await db.query.appointment.findFirst({
			where: eq(appointment.id, appointment_id)
		});
		await db
			.update(appointment)
			.set({
				status: get_appointment?.status === false ? true : true
			})
			.where(eq(appointment.id, appointment_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
