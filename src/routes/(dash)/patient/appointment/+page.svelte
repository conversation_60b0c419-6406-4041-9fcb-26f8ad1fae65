<script lang="ts">
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import GenderAge from '$lib/coms/GenderAge.svelte';
	import ConfirmModal from '$lib/coms-form/ConfirmModal.svelte';
	import Export from '$lib/coms/Export.svelte';
	import HandleQ from '$lib/coms-form/HandleQ.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_appoinments, get_patients, items } = $derived(data);
	let n = $state(1);
	let three_day = $state(new Date());
	let total_male = $derived(
		get_appoinments.filter((e) => e.progressNote?.patient.gender.toLowerCase() === 'male').length +
			get_appoinments.filter((e) => e.visit?.patient.gender.toLowerCase() === 'male').length
	);
	let appoinment_id: number | null = $state(null);
</script>

<ConfirmModal action="?/reject" id={appoinment_id} />
<div class="modal fade" id="modal-visite">
	<div class="modal-dialog modal-sm">
		<div class="modal-content">
			<div class="modal-header">
				<button aria-label="submit" type="submit" class="btn btn-success btn-lg p-4"
					><i class=" fas fa-stethoscope fa-3x"></i></button
				>
				<button aria-label="submit" type="submit" class="btn btn-danger btn-lg p-4"
					><i class=" fas fa-procedures fa-3x"></i></button
				>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-sm-6">
		<h2>
			{locale.T('appintment')}
		</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-restroom"></i>
					{locale.T('patient')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary">
					<i class=" fas fa-stethoscope"></i>
					{locale.T('appintment')}
				</a>
			</li>
		</ol>
	</div>
</div>
<div class="card bg-light">
	<div class="card-header">
		<HeaderQuery class="row g-1">
			<div class="col-auto">
				<Export
					title="Appointments list"
					data={get_appoinments.map((e) => {
						return {
							id: e.id,
							patient: e.visit?.patient?.name_khmer ?? e.progressNote?.patient?.name_khmer,
							datetime: e.datetime,
							description: e.description,
							status: e.status
						};
					})}
				/>
			</div>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('start')}</span>
					<input type="date" name="start" class="form-control" />
				</div>
			</div>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('end')}</span>
					<input type="date" name="end" class="form-control" />
				</div>
			</div>
			<div class="col-sm-3">
				<SelectParam
					q_name="q"
					placeholder={locale.T('patient')}
					name="patient_id"
					items={get_patients.map((e) => ({
						id: e.id,
						name: e.name_khmer?.concat(` ${e.name_latin}`)
					}))}
				/>
			</div>
			<div class="col-sm-auto">
				<HandleQ q_name="q" />
			</div>
		</HeaderQuery>
	</div>
	<div style="height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-hover table-bordered table-light">
			<thead class="sticky-top bg-light table-active">
				<tr class="text-center">
					<th style="width: 3%;">{locale.T('n')}</th>
					<th style="width: 5%;">{locale.T('id')}</th>
					<th style="width: 10%;">{locale.T('patient_name')}</th>
					<th style="width: 15%;">{locale.T('appintment')} / {locale.T('date')}</th>
					<th style="width: 15%;">{locale.T('description')}</th>
					<th colspan="2" style="width: 10%;">{locale.T('doctor')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_appoinments as item, index}
					{@const apvs = item.progress_note_id ? item.progressNote : item.visit}
					{@const datetime = new Date(item.datetime || '')}
					<tr class="text-center">
						<td>{index + n}</td>

						<td>
							VS{apvs?.id} <br />
							PT{apvs?.patient_id}
						</td>
						<td>
							{#if !item.status}
								<a
									aria-label="link-to-visit"
									class="btn btn-link text-danger"
									href={`/visit?visit_type=opd&patient_id=${apvs?.patient_id}&appointment_id=${item.id}`}
								>
									{apvs?.patient?.name_khmer}
									<br />
									{apvs?.patient?.name_latin}
									<GenderAge
										dob={apvs?.patient.dob}
										date={new Date()}
										gender={apvs?.patient.gender}
									/>
								</a>
							{:else}
								<a
									aria-label="link-to-visit"
									class="btn btn-link"
									href={item.progress_note_id
										? `/ipd/${item.progress_note_id}/appointment`
										: `/opd/${item.visit_id}/appointment`}
								>
									{apvs?.patient?.name_khmer}
									<br />
									{apvs?.patient?.name_latin}

									<GenderAge
										dob={apvs?.patient.dob}
										date={new Date()}
										gender={apvs?.patient.gender}
									/>
								</a>
							{/if}
						</td>
						<td>
							<DDMMYYYYFormat date={item.datetime} /> - {#if item.datetime_come}
								<span class="badge bg-primary text-light fs-6">
									<DDMMYYYYFormat date={item.datetime_come} />
								</span>
							{:else if item.status}
								<span class="badge bg-danger text-light fs-6">
									{locale.T('reject')}
								</span>
							{:else}
								<span class="badge bg-warning text-dark fs-6"> មិនទាន់មក </span>
							{/if}
						</td>
						<td class="text-break text-start">{item.description}</td>
						<td>{apvs?.staff?.name_khmer}</td>
						<td>
							{#if item.status && item.datetime_come}
								<button class="btn btn-primary btn-sm" type="button"
									><i class="fa-solid fa-circle-check"></i> Done</button
								>
							{:else if item.status && !item.datetime_come}
								<button class="btn btn-danger btn-sm" type="button"
									><i class="fa-solid fa-circle-xmark"></i> Rejected</button
								>
							{:else}
								<div>
									{#if datetime > three_day}
										<button class="btn btn-info btn-sm" type="button"
											><i class="fa-solid fa-spinner fa-spin"></i> Padding
										</button>
									{:else}
										<button
											data-bs-toggle="modal"
											data-bs-target="#confirm_modal"
											onclick={() => (appoinment_id = item.id)}
											class="btn btn-warning btn-sm"
											type="button"
											><i class="fa-solid fa-circle-exclamation"></i> លើសថ្ងៃកំណត់
										</button>
									{/if}
								</div>
							{/if}
						</td>
					</tr>
				{/each}
				<tr class="text-center table-success">
					<td colspan="7">
						{locale.T('total')}: {get_appoinments.length}
						{locale.T('people')},
						{locale.T('male')}: {total_male}
						{locale.T('female')}: {get_appoinments.length - total_male}
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations bind:n {items} />
	</div>
</div>
