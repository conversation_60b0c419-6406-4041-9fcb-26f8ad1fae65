<script lang="ts">
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import ListIpd from '$lib/coms-ipd-opd/ListIPD.svelte';
	import ListOpd from '$lib/coms-ipd-opd/ListOPD.svelte';
	import Export from '$lib/coms/Export.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_visits, get_departments, get_staffs, get_patients, get_pregress_notes } =
		$derived(data);
	let n = $state(1);
</script>

<div class="modal fade" id="modal-visite">
	<div class="modal-dialog modal-sm">
		<div class="modal-content">
			<div class="modal-header">
				<button aria-label="submit" type="submit" class="btn btn-success btn-lg p-4"
					><i class=" fas fa-stethoscope fa-3x"></i></button
				>
				<button aria-label="submit" type="submit" class="btn btn-danger btn-lg p-4"
					><i class=" fas fa-procedures fa-3x"></i></button
				>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-sm-6">
		<h2>
			{locale.T('dialy_visi_consult')}
		</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-restroom"></i>
					{locale.T('patient')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<button class="btn btn-link p-0 text-secondary">
					<i class=" fas fa-stethoscope"></i>
					{locale.T('dialy_visi_consult')}
				</button>
			</li>
		</ol>
	</div>
</div>
<div class="card bg-light">
	<div class="card-header">
		<HeaderQuery class="row g-1">
			<div class="col-auto">
				<Export
					title="OPD list"
					data={[
						...get_visits.map((e) => {
							return {
								id: `VS${e.id}`,
								patient: e.patient?.name_khmer,
								date_checkup: e.date_checkup,
								etiology: e.etiology,
								department: e.department?.products,
								doctor: e.staff?.name_khmer,
								status: e.status,
								type: e.checkin_type
							};
						}),
						...get_pregress_notes.map((e) => {
							return {
								id: `VS${e.id}`,
								patient: e.patient?.name_khmer,
								date_checkup: e.date_checkup,
								etiology: e.etiology,
								department: e.department?.products,
								doctor: e.staff?.name_khmer,
								status: e.status,
								type: 'IPD'
							};
						})
					]}
				/>
			</div>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('start')}</span>
					<input type="date" name="start" class="form-control" />
				</div>
			</div>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('end')}</span>
					<input type="date" name="end" class="form-control" />
				</div>
			</div>
			<div class="col-sm-3">
				<SelectParam
					q_name="q"
					placeholder={locale.T('patient')}
					name="patient_id"
					items={get_patients.map((e) => ({
						id: e.id,
						name: e.name_khmer?.concat(` ${e.name_latin}`)
					}))}
				/>
			</div>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('department')}</span>
					<select name="department_id" id="department_id" class="form-control">
						<option value="">All</option>
						{#each get_departments as item}
							<option value={item.id}>{item.products}</option>
						{/each}
					</select>
				</div>
			</div>
		</HeaderQuery>
	</div>
	<div class="card-body table-responsive border m-0 p-0 border-2 border-primary">
		<div class="kh_font_muol_light my-2 text-center">ផ្នែកពិគ្រោះជំងឺក្រៅ OPD</div>
		<ListOpd
			data={{
				get_departments,
				get_staffs,
				get_visits
			}}
			{n}
		/>
	</div>
	<div class="card-body table-responsive border m-0 p-0 border-2 border-top-0 border-primary">
		<div class="kh_font_muol_light text-center my-2">ផ្នែកជំងឺសម្រាកព្យាបាល IPD</div>
		<ListIpd data={{ get_pregress_notes }} {n} />
	</div>
</div>
