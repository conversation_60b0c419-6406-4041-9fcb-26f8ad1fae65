import { db } from '$lib/server/db';
import { billing, injection, patient, product, vaccine, visit, words } from '$lib/server/schemas';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { and, desc, eq, like, or } from 'drizzle-orm';
import { logError } from '$lib/server/utils/telegram';
import { betweenHelper, pagination, YYYYMMDD_Format } from '$lib/server/utils';

export const load = (async ({ parent, url }) => {
	await parent();
	const q = url.searchParams.get('q') ?? '';
	const patient_id = Number(url.searchParams.get('patient_id'));
	const department_id = Number(url.searchParams.get('department_id'));
	const get_departments = await db.query.product.findMany({
		where: eq(product.category_id, 11)
	});
	const get_staffs = await db.query.staff.findMany();
	const get_visits = await db.query.visit.findMany({
		where: and(
			eq(visit.checkin_type, 'OPD'),
			// eq(visit.transfer, true),
			betweenHelper(url, visit.date_checkup),
			patient_id ? eq(visit.patient_id, patient_id) : undefined,
			department_id ? eq(visit.department_id, department_id) : undefined
		),
		with: {
			staff: {
				with: {
					title: true
				}
			},
			patient: true,
			department: true,
			billing: {
				with: {
					serviceType: true,
					paymentService: true
				}
			},
			document: true
		},
		orderBy: desc(visit.date_checkup),
		...pagination(url)
	});
	const count = await db.$count(
		visit,
		and(
			eq(visit.checkin_type, 'OPD'),
			// eq(visit.transfer, true),
			betweenHelper(url, visit.date_checkup),
			patient_id ? eq(visit.patient_id, patient_id) : undefined,
			department_id ? eq(visit.department_id, department_id) : undefined
		)
	);
	const get_patients = await db.query.patient.findMany({
		where: or(
			like(patient.name_latin, `%${q}%`),
			like(patient.name_khmer, `%${q}%`),
			like(patient.telephone, `%${q}%`),
			like(patient.id, `%${q}%`)
		),
		limit: 200
	});
	const get_words_payment_service = await db.query.words.findMany({
		where: eq(words.category, 'payment_service')
	});
	return {
		get_visits,
		get_departments,
		get_staffs,
		get_patients,
		get_words_payment_service,
		items: count
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	update_etiology: async ({ request, url }) => {
		const body = await request.formData();
		const id = body.get('id');
		const etiology = body.get('etiology') || '';
		if (!id || isNaN(+id)) return fail(303, { idErr: true });
		await db
			.update(visit)
			.set({
				etiology: etiology.toString()
			})
			.where(eq(visit.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_department: async ({ request, url }) => {
		const body = await request.formData();
		const id = body.get('id');
		const department_id = body.get('department_id') || '';
		if (!id || isNaN(+id)) return fail(303, { idErr: true });
		await db
			.update(visit)
			.set({
				department_id: +department_id
			})
			.where(eq(visit.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_staff: async ({ request, url }) => {
		const body = await request.formData();
		const id = body.get('id');
		const staff_id = body.get('staff_id') || '';
		if (!id || isNaN(+id)) return fail(303, { idErr: true });
		await db
			.update(visit)
			.set({
				staff_id: +staff_id
			})
			.where(eq(visit.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	process_billing: async ({ request, url }) => {
		const body = await request.formData();
		const billing_id = body.get('id');
		const visit_id = body.get('visit_id');
		if (!billing_id || isNaN(+billing_id)) return fail(303, { idErr: true });
		if (!visit_id || isNaN(+visit_id)) return fail(303, { idErr: true });
		const get_billing = await db.query.billing.findFirst({
			where: eq(billing.id, +billing_id)
		});
		const status = get_billing?.status === 'checking' ? 'paying' : 'checking';

		await db
			.update(visit)
			.set({
				status: status === 'checking' ? 'CHECKING' : 'DONE'
			})
			.where(eq(visit.id, +visit_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await db
			.update(billing)
			.set({
				status: status,
				created_at: YYYYMMDD_Format.datetime(new Date())
			})
			.where(eq(billing.id, +billing_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_status: async ({ request, url }) => {
		const body = await request.formData();
		const { id, status } = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(visit)
			.set({ status: status as 'CHECKING' | 'DONE' | 'LOADING' })
			.where(eq(visit.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	active_visit: async ({ request, locals, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(visit)
			.set({
				status: 'CHECKING',
				staff_id: locals.user?.staff_id
			})
			.where(eq(visit.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		redirect(303, `/opd/${id}/subjective`);
	},
	delete_visit: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		const get_visit = await db.query.visit.findFirst({
			where: eq(visit.id, +id),
			with: {
				vaccine: {
					with: {
						injection: true
					}
				}
			}
		});
		if (Number(get_visit?.vaccine?.length) > 0) {
			for (const e of get_visit?.vaccine || []) {
				await db
					.update(vaccine)
					.set({
						status: false,
						visit_id: null,
						product_id: null
					})
					.where(eq(vaccine.id, e.id))
					.catch((e) => {
						logError({ url, body, err: e });
					});
				const get_injection = await db.query.injection.findFirst({
					where: eq(injection.id, Number(e.injection_id)),
					with: {
						vaccine: true
					}
				});
				if (get_injection?.vaccine.every((e) => e.status === false)) {
					await db.delete(injection).where(eq(injection.id, Number(e.injection_id)));
				}
			}
		}
		try {
			await db.delete(visit).where(eq(visit.id, +id));
		} catch (e) {
			if (e) logError({ url, body, err: e });
		}
	}
};
