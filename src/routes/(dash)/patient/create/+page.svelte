<script lang="ts">
	import type { ActionData, PageServerData } from './$types';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import { dobToAge, YYYYMMDD_Format } from '$lib/helper';
	import CropImage from '$lib/coms-form/CropImage.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import Address from '$lib/coms-cu/Address.svelte';
	import WordsOne from '$lib/coms-cu/WordsOne.svelte';
	import WordsForm from '$lib/coms-form/WordsForm.svelte';
	import GetBack from '$lib/coms/GetBack.svelte';
	interface Props {
		form: ActionData;
		data: PageServerData;
	}
	let loading = $state(false);
	let { form, data }: Props = $props();
	let { get_provinces, get_patient, get_conmunies, get_districts, get_vilages, get_words } =
		$derived(data);

	let dob: string = $state(YYYYMMDD_Format.date(data.get_patient?.dob ?? ''));

	let imagepreview = $state('');
	$effect(() => {
		if (get_patient?.uploads?.filename) {
			imagepreview = get_patient?.uploads?.filename;
		} else {
			imagepreview = '';
		}
	});

	let words = $state('');
	let filltered_words = $derived(get_words.filter((e) => e.type === words));
</script>

<WordsOne category="patient" name={words} modal_name="patient_words" words={filltered_words} />
<div class="row">
	<div class="col-sm-6">
		<GetBack href="/patient/all" />
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-restroom"></i>
					{locale.T('patient')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/create" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('add')}
				</a>
			</li>
		</ol>
	</div>
</div>
<div class="card bg-light">
	<div class="card-body">
		<Form
			action={get_patient?.id ? '?/update_patient' : '?/create_patient'}
			method="post"
			enctype="multipart/form-data"
			bind:loading
		>
			<div class="row">
				<div class="col-md-3">
					<input value={get_patient?.id} type="hidden" name="patient_id" />
					<input value={get_patient?.uploads?.filename ?? ''} type="hidden" name="old_image" />
					<div class=" pb-3">
						<label for="name_khmer">{locale.T('name_khmer')}</label>
						<input
							value={get_patient?.name_khmer ?? ''}
							required
							minlength="1"
							name="name_khmer"
							type="text"
							class="form-control"
							id="name_khmer"
						/>

						{#if form?.name_khmer}
							<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
						{/if}
					</div>
				</div>
				<div class="col-md-3">
					<div class=" pb-3">
						<label for="name_latin">{locale.T('name_latin')}</label>
						<input
							required
							value={get_patient?.name_latin ?? ''}
							name="name_latin"
							type="text"
							class="form-control"
							id="name_latin"
						/>
						{#if form?.name_latin}
							<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
						{/if}
					</div>
				</div>
				<div class="col-md-3">
					<div class=" pb-3">
						<label for="gender">{locale.T('gender')}</label>
						<select
							required
							value={get_patient?.gender ?? ''}
							name="gender"
							class="form-control"
							id="gender"
						>
							<option value="Other">{locale.T('none')}</option>
							<option value="Male">{locale.T('male')}</option>
							<option value="Female">{locale.T('female')}</option>
						</select>
						{#if form?.gender}
							<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
						{/if}
					</div>
				</div>
				<div class="col-md-3">
					<div class=" ">
						<label for="blood_group">{locale.T('blood_group')}</label>
						<select
							value={get_patient?.blood_group ?? ''}
							name="blood_group"
							class="form-control"
							id="blood_group"
						>
							<option value="">{locale.T('unknown')}</option>

							<option value="A-">A-</option>
							<option value="A+">A+</option>

							<option value="AB+">AB+</option>
							<option value="AB-">AB-</option>

							<option value="B-">B-</option>
							<option value="B+">B+</option>

							<option value="O-">O-</option>
							<option value="O+">O+</option>
						</select>
					</div>
				</div>
				<div class="col-md-3">
					<div class=" pb-3">
						<label for="dob">{locale.T('dob')}</label>
						<input bind:value={dob} name="dob" type="date" class="form-control" id="dob" />
						{#if form?.dob}
							<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
						{/if}
					</div>
				</div>

				<!-- New filed  -->
				<div class="col-md-3">
					<div class="pb-3">
						<label for="nation">{locale.T('nationality')}</label>
						<WordsForm
							class="input-group"
							default_value={get_patient?.nation ?? ''}
							form_name="nation"
							get_words={get_words.filter((e) => e.type === 'nation')}
							words_form="nation"
							onclick={() => (words = 'nation')}
						/>
					</div>
				</div>
				<div class="col-md-3">
					<div class="pb-3">
						<label for="id_cart_passport">{locale.T('id_card_or_passport')}</label>
						<input
							value={get_patient?.id_cart_passport ?? ''}
							name="id_cart_passport"
							type="text"
							class="form-control"
							id="id_cart_passport"
						/>
					</div>
				</div>
				<div class="col-md-3">
					<div class="pb-3">
						<label for="education">{locale.T('education')}</label>
						<WordsForm
							class="input-group"
							default_value={get_patient?.education ?? ''}
							form_name="education"
							get_words={get_words.filter((e) => e.type === 'education')}
							words_form="education"
							onclick={() => (words = 'education')}
						/>
					</div>
				</div>
				<div class="col-md-3">
					<div class="pb-3">
						<label for="material_status">{locale.T('material')}</label>
						<WordsForm
							class="input-group"
							default_value={get_patient?.material_status ?? ''}
							form_name="material_status"
							get_words={get_words.filter((e) => e.type === 'material_status')}
							words_form="material_status"
							onclick={() => (words = 'material_status')}
						/>
					</div>
				</div>
				<div class="col-md-3">
					<div class="pb-3">
						<label for="occupation">{locale.T('occupation')}</label>
						<WordsForm
							class="input-group"
							default_value={get_patient?.occupation ?? ''}
							form_name="occupation"
							get_words={get_words.filter((e) => e.type === 'occupation')}
							words_form="occupation"
							onclick={() => (words = 'occupation')}
						/>
					</div>
				</div>
				<div class="col-md-3">
					<div class="pb-3">
						<label for="work_place">{locale.T('work_place')}</label>
						<WordsForm
							class="input-group"
							default_value={get_patient?.work_place ?? ''}
							form_name="work_place"
							get_words={get_words.filter((e) => e.type === 'work_place')}
							words_form="work_place"
							onclick={() => (words = 'work_place')}
						/>
					</div>
				</div>
				<div class="col-md-3">
					<div class=" pb-3">
						<label for="telephone">{locale.T('contact')}</label>
						<input
							value={get_patient?.telephone ?? ''}
							name="telephone"
							type="text"
							class="form-control"
							id="telephone"
						/>
						{#if form?.telephone}
							<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
						{/if}
					</div>
				</div>
			</div>
			<Address
				data={{ get_conmunies, get_districts, get_provinces, get_vilages }}
				defaultValue={{
					commune_id: get_patient?.commune_id ?? null,
					province_id: get_patient?.province_id ?? null,
					district_id: get_patient?.district_id ?? null,
					village_id: get_patient?.village_id ?? null
				}}
			/>
			<div class="row">
				<div class="col-md-12">
					<div class="pb-3">
						<label for="Other">{locale.T('other')}</label>
						<input
							value={get_patient?.other ?? ''}
							name="other"
							type="text"
							class="form-control"
							id="Other"
						/>
						{#if form?.other}
							<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
						{/if}
					</div>
				</div>
				<div class="col-md-12">
					<div class=" pb-3">
						<label for="exampleInputFile">{locale.T('picture')}</label>
						<div class="input-group">
							<CropImage
								related_id={get_patient.id}
								related_type_="patient"
								default_image={imagepreview ?? ''}
								name="file"
							/>
						</div>
					</div>
				</div>
			</div>
			<div class=" alert alert-primary py-2 my-2">
				<h3>{locale.T('mother')}</h3>
				<div class="row">
					<div class="col-md-3">
						<div class=" pb-3">
							<label for="m_name_khmer">{locale.T('name_khmer')}</label>
							<input
								value={get_patient?.m_name_khmer ?? ''}
								minlength="1"
								name="m_name_khmer"
								type="text"
								class="form-control"
								id="m_name_khmer"
							/>
						</div>
					</div>
					<div class="col-md-3">
						<div class=" pb-3">
							<label for="m_name_latin">{locale.T('name_latin')}</label>
							<input
								value={get_patient?.m_name_latin ?? ''}
								name="m_name_latin"
								type="text"
								class="form-control"
								id="m_name_latin"
							/>
						</div>
					</div>
					<div class="col-md-3">
						<div class="pb-3">
							<label for="m_telephone">{locale.T('contact')}</label>
							<input
								value={get_patient?.m_telephone ?? ''}
								name="m_telephone"
								type="number"
								class="form-control"
								id="m_telephone"
							/>
						</div>
					</div>
					<div class="col-md-3">
						<div class="pb-3">
							<label for="m_occupation">{locale.T('occupation')}</label>
							<WordsForm
								class="input-group"
								default_value={get_patient?.m_occupation ?? ''}
								form_name="m_occupation"
								get_words={get_words.filter((e) => e.type === 'occupation')}
								words_form="occupation"
								onclick={() => (words = 'occupation')}
							/>
						</div>
					</div>
				</div>
			</div>
			<div class=" alert alert-primary py-2 my-2">
				<h3>{locale.T('father')}</h3>
				<div class="row">
					<div class="col-md-3">
						<div class=" pb-3">
							<label for="f_name_khmer">{locale.T('name_khmer')}</label>
							<input
								value={get_patient?.f_name_khmer ?? ''}
								minlength="1"
								name="f_name_khmer"
								type="text"
								class="form-control"
								id="f_name_khmer"
							/>
						</div>
					</div>
					<div class="col-md-3">
						<div class=" pb-3">
							<label for="f_name_latin">{locale.T('name_latin')}</label>
							<input
								value={get_patient?.f_name_latin ?? ''}
								name="f_name_latin"
								type="text"
								class="form-control"
								id="f_name_latin"
							/>
						</div>
					</div>
					<div class="col-md-3">
						<div class="pb-3">
							<label for="f_telephone">{locale.T('contact')}</label>
							<input
								value={get_patient?.f_telephone ?? ''}
								name="f_telephone"
								type="number"
								class="form-control"
								id="f_telephone"
							/>
						</div>
					</div>
					<div class="col-md-3">
						<div class="pb-3">
							<label for="f_occupation">{locale.T('occupation')}</label>
							<WordsForm
								class="input-group"
								default_value={get_patient?.f_occupation ?? ''}
								form_name="f_occupation"
								get_words={get_words.filter((e) => e.type === 'occupation')}
								words_form="occupation"
								onclick={() => (words = 'occupation')}
							/>
						</div>
					</div>
				</div>
			</div>
			<div class=" alert alert-primary py-2 my-2">
				<h3>{locale.T('carer')}</h3>
				<div class="row">
					<div class="col-md-3">
						<div class=" pb-3">
							<label for="c_name_khmer">{locale.T('name_khmer')}</label>
							<input
								value={get_patient?.c_name_khmer ?? ''}
								minlength="1"
								name="c_name_khmer"
								type="text"
								class="form-control"
								id="c_name_khmer"
							/>
						</div>
					</div>
					<div class="col-md-3">
						<div class=" pb-3">
							<label for="c_name_latin">{locale.T('name_latin')}</label>
							<input
								value={get_patient?.c_name_latin ?? ''}
								name="c_name_latin"
								type="text"
								class="form-control"
								id="c_name_latin"
							/>
						</div>
					</div>
					<div class="col-md-3">
						<div class="pb-3">
							<label for="c_telephone">{locale.T('contact')}</label>
							<input
								value={get_patient?.f_telephone ?? ''}
								name="c_telephone"
								type="number"
								class="form-control"
								id="c_telephone"
							/>
						</div>
					</div>
					<div class="col-md-3">
						<div class="pb-3">
							<label for="c_occupation">{locale.T('occupation')}</label>
							<WordsForm
								class="input-group"
								default_value={get_patient?.c_occupation ?? ''}
								form_name="c_occupation"
								get_words={get_words.filter((e) => e.type === 'occupation')}
								words_form="occupation"
								onclick={() => (words = 'occupation')}
							/>
						</div>
					</div>
				</div>
			</div>

			<div class="float-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>
