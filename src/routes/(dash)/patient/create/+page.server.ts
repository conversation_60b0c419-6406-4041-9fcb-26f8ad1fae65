import { db } from '$lib/server/db';
import { uploads, patient, village, commune, district, words, provice } from '$lib/server/schemas';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { and, desc, eq } from 'drizzle-orm';
import { DDMMYYYY_Format, YYYYMMDD_Format } from '$lib/server/utils';
import { logError, message } from '$lib/server/utils/telegram';
import { fileHandle } from '$lib/server/upload';
export const load = (async ({ parent, url }) => {
	await parent();
	const patient_id = url.searchParams.get('patient_id') || '';
	const province_id = url.searchParams.get('province_id') ?? '';
	const district_id = url.searchParams.get('district_id') ?? '';
	const commune_id = url.searchParams.get('commune_id') ?? '';
	const get_provinces = await db.query.provice.findMany({});
	const get_districts = await db.query.district.findMany({
		where: eq(district.province_id, Number(province_id))
	});
	const get_conmunies = await db.query.commune.findMany({
		where: eq(commune.district_id, Number(district_id))
	});
	const get_vilages = await db.query.village.findMany({
		where: eq(village.commune_id, Number(commune_id))
	});
	const get_patient = await db.query.patient.findFirst({
		where: eq(patient.id, +patient_id),
		orderBy: desc(patient.id),
		with: {
			provice: true,
			district: true,
			commune: true,
			village: true
		}
	});
	const get_words = await db.query.words.findMany({
		where: eq(words.category, 'patient')
	});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'patient'), eq(uploads.related_id, +patient_id))
	});
	return {
		get_patient: {
			...get_patient,
			uploads: get_upload
		},
		get_provinces,
		get_districts,
		get_conmunies,
		get_vilages,
		get_words
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_patient: async ({ request, url }) => {
		const body = await request.formData();
		const image = body.get('image') as File;
		const {
			name_khmer,
			name_latin,
			province_id,
			district_id,
			id_cart_passport,
			blood_group,
			education,
			nation,
			material_status,
			work_place,
			occupation,
			dob,
			commune_id,
			village_id,
			other,
			telephone,
			gender,
			f_name_khmer,
			f_name_latin,
			f_telephone,
			f_occupation,
			m_name_khmer,
			m_name_latin,
			m_telephone,
			m_occupation,
			c_name_khmer,
			c_name_latin,
			c_telephone,
			c_occupation
		} = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			name_khmer: false,
			name_latin: false,
			province_id: false,
			district_id: false,
			age: false,
			dob: false,
			commune_id: false,
			village_id: false,
			other: false,
			telephone: false,
			gender: false,
			blood_group: false
		};
		if (!['A-', 'A+', 'AB+', 'AB-', 'B-', 'B+', 'O-', 'O+', ''].includes(blood_group))
			validErr.blood_group = true;
		if (!name_khmer.trim()) validErr.name_khmer = true;
		if (!name_latin.trim()) validErr.name_latin = true;
		if (!dob.trim()) validErr.dob = true;
		if (!gender.trim()) validErr.gender = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		const created_at = YYYYMMDD_Format.datetime(new Date());
		try {
			const create_patient: { id: number }[] = await db
				.insert(patient)
				.values({
					dob: dob,
					gender: gender,
					name_khmer: name_khmer,
					blood_group: blood_group,
					education: education,
					nation: nation,
					material_status: material_status,
					work_place: work_place,
					occupation: occupation,
					name_latin: name_latin,
					telephone: telephone,
					id_cart_passport: id_cart_passport,
					province_id: Number(province_id) || null,
					district_id: Number(district_id) || null,
					commune_id: Number(commune_id) || null,
					village_id: Number(village_id) || null,
					other: other,
					f_name_khmer: f_name_khmer,
					f_name_latin: f_name_latin,
					f_telephone: f_telephone,
					f_occupation: f_occupation,
					m_name_khmer: m_name_khmer,
					m_name_latin: m_name_latin,
					m_telephone: m_telephone,
					m_occupation: m_occupation,
					c_name_khmer: c_name_khmer,
					c_name_latin: c_name_latin,
					c_telephone: c_telephone,
					c_occupation: c_occupation,
					created_at: created_at
				})
				.$returningId();
			if (image.size && create_patient[0].id) {
				await fileHandle.insert(image, create_patient[0].id, 'patient');
			}
		} catch (e) {
			logError({ url, body, err: e });
		}
		const khmer_date = '*កាលបរិច្ឆេទ ៖ '
			.concat(DDMMYYYY_Format(new Date().toISOString(), 'datetime'))
			.concat(' បង្កើតថ្មី*');
		async function get_text() {
			let address = '';
			if (village_id.length) {
				const get_vilage = await db.query.village.findFirst({
					where: eq(village.id, Number(village_id))
				});
				address = address.concat(get_vilage?.type ?? '').concat(get_vilage?.name_khmer ?? '');
			}
			if (commune_id.length) {
				const get_commune = await db.query.commune.findFirst({
					where: eq(commune.id, Number(commune_id))
				});
				address = address
					.concat(',')
					.concat(get_commune?.type ?? '')
					.concat(get_commune?.name_khmer ?? '');
			}
			if (district_id.length) {
				const get_district = await db.query.district.findFirst({
					where: eq(district.id, Number(district_id))
				});
				address = address
					.concat(',')
					.concat(get_district?.type ?? '')
					.concat(get_district?.name_khmer ?? '');
			}
			if (province_id.length) {
				const get_province = await db.query.provice.findFirst({
					where: eq(provice.id, Number(province_id))
				});
				address = address
					.concat(',')
					.concat(get_province?.type ?? '')
					.concat(get_province?.name_khmer ?? '');
			}
			let text = khmer_date;
			text = text.concat('\n').concat('ឈ្មោះអ្នកជំងឺ ៖ ').concat(`${name_khmer}(${name_latin})`);

			if (gender.length) {
				text = text.concat('\n').concat('ភេទ ៖ ').concat(gender);
			}
			if (dob.length) {
				text = text.concat('\n').concat('ថ្ងៃខែឆ្នាំកំណើត ៖ ').concat(dob);
			}
			if (nation.length) {
				text = text.concat('\n').concat('សញ្ជាតិ ៖ ').concat(nation);
			}
			if (address.length) {
				text = text
					.concat('\n')
					.concat('អាសយដ្ឋាន ៖ ')
					.concat(address ?? '');
			}
			if (telephone.length) {
				text = text.concat('\n').concat('លេខទូរស័ព្ទ ៖ ').concat(telephone);
			}
			if (blood_group.length) {
				text = text.concat('\n').concat('ក្រុមឈាម ៖ ').concat(blood_group);
			}
			if (education.length) {
				text = text.concat('\n').concat('កម្រិតវប្បធម៌ ៖ ').concat(education);
			}
			if (material_status.length) {
				text = text.concat('\n').concat('ស្ថានភាពគ្រួសារ ៖ ').concat(material_status);
			}
			if (work_place.length) {
				text = text.concat('\n').concat('ទីកន្លែងការងារ ៖ ').concat(work_place);
			}
			if (occupation.length) {
				text = text.concat('\n').concat('មុខរបរ ៖ ').concat(occupation);
			}
			if (other.length) {
				text = text.concat('\n').concat('ផ្សេងៗ ៖ ').concat(other);
			}
			if (f_name_khmer.length) {
				text = text.concat('\n').concat('ឈ្មោះឪពុក ៖ ').concat(f_name_khmer);
			}
			if (f_name_latin.length) {
				text = text.concat(' ').concat(f_name_latin);
			}
			if (f_telephone.length) {
				text = text.concat('\n').concat('លេខទូរស័ព្ទឪពុក ៖ ').concat(f_telephone);
			}
			if (f_occupation.length) {
				text = text.concat('\n').concat('មុខរបរឪពុក ៖ ').concat(f_occupation);
			}
			if (m_name_khmer.length) {
				text = text.concat('\n').concat('ឈ្មោះម្ដាយ ៖ ').concat(m_name_khmer);
			}
			if (m_name_latin.length) {
				text = text.concat(' ').concat(m_name_latin);
			}
			if (m_telephone.length) {
				text = text.concat('\n').concat('លេខទូរស័ព្ទម្ដាយ ៖ ').concat(m_telephone);
			}
			if (m_occupation.length) {
				text = text.concat('\n').concat('មុខរប���ម្ដាយ ៖ ').concat(m_occupation);
			}
			if (c_name_khmer.length) {
				text = text.concat('\n').concat('ឈ្មោះអ្នកថែទាំ ៖ ').concat(c_name_khmer);
			}
			if (c_name_latin.length) {
				text = text.concat(' ').concat(c_name_latin);
			}
			if (c_telephone.length) {
				text = text.concat('\n').concat('លេខទូរស័ព្ទអ្នកថែទាំ ៖ ').concat(c_telephone);
			}
			if (c_occupation.length) {
				text = text.concat('\n').concat('មុខរបរអ្នកថែទាំ ៖ ').concat(c_occupation);
			}
			return text;
		}
		const text = await get_text();
		await message(`${text}`, 'ADMIT');
		redirect(300, '/patient/all');
	},
	update_patient: async ({ request, url }) => {
		const body = await request.formData();
		const {
			patient_id,
			name_khmer,
			name_latin,
			province_id,
			district_id,
			id_cart_passport,
			blood_group,
			education,
			nation,
			material_status,
			work_place,
			occupation,
			dob,
			commune_id,
			village_id,
			other,
			telephone,
			gender,
			f_name_khmer,
			f_name_latin,
			f_telephone,
			f_occupation,
			m_name_khmer,
			m_name_latin,
			m_telephone,
			m_occupation,
			c_name_khmer,
			c_name_latin,
			c_telephone,
			c_occupation
		} = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			name_khmer: false,
			name_latin: false,
			province_id: false,
			district_id: false,
			dob: false,
			commune_id: false,
			village_id: false,
			other: false,
			telephone: false,
			patient_id: false,
			gender: false,
			blood_group: false
		};
		if (!['A-', 'A+', 'AB+', 'AB-', 'B-', 'B+', 'O-', 'O+', ''].includes(blood_group))
			validErr.blood_group = true;
		if (!name_khmer.trim()) validErr.name_khmer = true;
		if (!name_latin.trim()) validErr.name_latin = true;
		if (!dob.trim()) validErr.dob = true;
		if (!gender.trim()) validErr.gender = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		try {
			await db
				.update(patient)
				.set({
					blood_group: blood_group,
					education: education,
					nation: nation,
					material_status: material_status,
					work_place: work_place,
					occupation: occupation,
					dob: dob,
					gender: gender,
					id_cart_passport: id_cart_passport,
					name_khmer: name_khmer,
					name_latin: name_latin,
					telephone: telephone,
					province_id: province_id ? Number(province_id) : null,
					district_id: district_id ? Number(district_id) : null,
					commune_id: commune_id ? Number(commune_id) : null,
					village_id: village_id ? Number(village_id) : null,
					other: other,
					f_name_khmer: f_name_khmer,
					f_name_latin: f_name_latin,
					f_telephone: f_telephone,
					f_occupation: f_occupation,
					m_name_khmer: m_name_khmer,
					m_name_latin: m_name_latin,
					m_telephone: m_telephone,
					m_occupation: m_occupation,
					c_name_khmer: c_name_khmer,
					c_name_latin: c_name_latin,
					c_telephone: c_telephone,
					c_occupation: c_occupation
				})
				.where(eq(patient.id, +patient_id));
			await fileHandle.auto(body);
			const khmer_date = '*កាលបរិច្ឆេទ ៖ '
				.concat(DDMMYYYY_Format(new Date().toISOString(), 'datetime'))
				.concat(' កែប្រែ*');
			async function get_text() {
				let address = '';
				if (village_id) {
					const get_vilage = await db.query.village.findFirst({
						where: eq(village.id, Number(village_id))
					});
					address = address.concat(get_vilage?.type ?? '').concat(get_vilage?.name_khmer ?? '');
				}
				if (commune_id) {
					const get_commune = await db.query.commune.findFirst({
						where: eq(commune.id, Number(commune_id))
					});
					address = address
						.concat(',')
						.concat(get_commune?.type ?? '')
						.concat(get_commune?.name_khmer ?? '');
				}
				if (district_id) {
					const get_district = await db.query.district.findFirst({
						where: eq(district.id, Number(district_id))
					});
					address = address
						.concat(',')
						.concat(get_district?.type ?? '')
						.concat(get_district?.name_khmer ?? '');
				}
				if (province_id) {
					const get_province = await db.query.provice.findFirst({
						where: eq(provice.id, Number(province_id))
					});
					address = address
						.concat(',')
						.concat(get_province?.type ?? '')
						.concat(get_province?.name_khmer ?? '');
				}
				let text = khmer_date;
				text = text.concat('\n').concat('ឈ្មោះអ្នកជំងឺ ៖ ').concat(`${name_khmer}(${name_latin})`);

				if (gender) {
					text = text.concat('\n').concat('ភេទ ៖ ').concat(gender);
				}
				if (dob) {
					text = text.concat('\n').concat('ថ្ងៃខែឆ្នាំកំណើត ៖ ').concat(dob);
				}
				if (nation) {
					text = text.concat('\n').concat('សញ្ជាតិ ៖ ').concat(nation);
				}
				if (address.length) {
					text = text
						.concat('\n')
						.concat('អាសយដ្ឋាន ៖ ')
						.concat(address ?? '');
				}
				if (telephone) {
					text = text.concat('\n').concat('លេខទូរស័ព្ទ ៖ ').concat(telephone);
				}
				if (blood_group) {
					text = text.concat('\n').concat('ក្រុមឈាម ៖ ').concat(blood_group);
				}
				if (education) {
					text = text.concat('\n').concat('កម្រិតវប្បធម៌ ៖ ').concat(education);
				}
				if (material_status) {
					text = text.concat('\n').concat('ស្ថានភាពគ្រួសារ ៖ ').concat(material_status);
				}
				if (work_place) {
					text = text.concat('\n').concat('ទីកន្លែងការងារ ៖ ').concat(work_place);
				}
				if (occupation) {
					text = text.concat('\n').concat('មុខរបរ ៖ ').concat(occupation);
				}
				if (other) {
					text = text.concat('\n').concat('ផ្សេងៗ ៖ ').concat(other);
				}
				if (f_name_khmer) {
					text = text.concat('\n').concat('ឈ្មោះឪពុក ៖ ').concat(f_name_khmer);
				}
				if (f_name_latin) {
					text = text.concat(' ').concat(f_name_latin);
				}
				if (f_telephone) {
					text = text.concat('\n').concat('លេខទូរស័ព្ទឪពុក ៖ ').concat(f_telephone);
				}
				if (f_occupation) {
					text = text.concat('\n').concat('មុខរបរឪពុក ៖ ').concat(f_occupation);
				}
				if (m_name_khmer) {
					text = text.concat('\n').concat('ឈ្មោះម្ដាយ ៖ ').concat(m_name_khmer);
				}
				if (m_name_latin) {
					text = text.concat(' ').concat(m_name_latin);
				}
				if (m_telephone) {
					text = text.concat('\n').concat('លេខទូរស័ព្ទម្ដាយ ៖ ').concat(m_telephone);
				}
				if (m_occupation) {
					text = text.concat('\n').concat('មុខរបរម្ដាយ ៖ ').concat(m_occupation);
				}
				if (c_name_khmer) {
					text = text.concat('\n').concat('ឈ្មោះអ្នកថែទាំ ៖ ').concat(c_name_khmer);
				}
				if (c_name_latin) {
					text = text.concat(' ').concat(c_name_latin);
				}
				if (c_telephone) {
					text = text.concat('\n').concat('លេខទូរស័ព្ទអ្នកថែទាំ ៖ ').concat(c_telephone);
				}
				if (c_occupation) {
					text = text.concat('\n').concat('មុខរបរអ្នកថែទាំ ៖ ').concat(c_occupation);
				}
				return text;
			}
			const text = await get_text();
			await message(`${text}`, 'ADMIT');
		} catch (e) {
			logError({ url, body, err: e });
		}
		redirect(300, '/patient/all');
	}
};
