<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import Form from '$lib/coms-form/Form.svelte';
	import PatientInfo from '$lib/coms-ipd-opd/PatientInfo.svelte';
	import CropImage from '$lib/coms-form/CropImage.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { invalidateAll } from '$app/navigation';
	import { page } from '$app/state';
	import QrBrScaner from '$lib/coms/QrBrScaner.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_payment_service, patient_info, get_service_types } = $derived(data);
	let service_type_id: number | null = $state(data.get_payment_service?.service_type_id ?? null);
	let code: string = $state(data.get_payment_service?.code ?? '');
	let loading = $state(false);
</script>

<div class="row">
	<div class="col-sm-6">
		<button onclick={() => window.history.back()} class="btn btn-link p-0"
			><i class="fa-solid fa-rotate-left"></i>
			{locale.T('back')}
		</button>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-restroom"></i>
					{locale.T('patient')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary">
					<i class=" fas fa-stethoscope"></i>
					{locale.T('opd')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<button class="btn btn-link p-0 text-secondary">
					<i class=" fas fa-money-bill"></i>
					{locale.T('payment')}
				</button>
			</li>
		</ol>
	</div>
</div>
<PatientInfo {patient_info} />
<br />

<div class="card bg-light">
	<Form
		fnSuccess={() => invalidateAll()}
		id="form_payment_service"
		reset={false}
		enctype="multipart/form-data"
		action="?/update_payment_service"
		method="POST"
		bind:loading
	>
		<div class="card-body">
			<input value={get_payment_service?.id} type="hidden" name="payment_service_id" />
			<input
				value={get_payment_service?.billing_id
					? get_payment_service?.billing_id
					: page.url.searchParams.get('billing_id')}
				type="hidden"
				name="billing_id"
			/>
			<div class="">
				<div class="input-group mb-2">
					<select
						bind:value={service_type_id}
						class="form-control"
						name="service_type_id"
						id="service_type_id"
					>
						{#each get_service_types as item}
							<option value={item?.id}>{item?.by}</option>
						{/each}
					</select>
					{#if service_type_id !== 1}
						<input
							placeholder="លេខកូដ"
							disabled={service_type_id === 1}
							bind:value={code}
							class="form-control"
							type="text"
							name="code"
							id="code"
						/>

						<QrBrScaner bind:result={code} />
					{/if}
				</div>
				<div class="input-group mb-2">
					<CropImage
						aspect_ratio
						label={locale.T('references')}
						name="file"
						default_image={get_payment_service?.uploads?.filename}
						related_id={get_payment_service?.id}
						related_type_="paymentService"
					/>
				</div>
			</div>
		</div>
		<div class="card-footer text-end">
			<SubmitButton {loading} />
		</div>
	</Form>
</div>
