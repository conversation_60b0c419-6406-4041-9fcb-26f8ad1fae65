<script lang="ts">
	import type { PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import { store } from '$lib/store/store.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import GenderAge from '$lib/coms/GenderAge.svelte';
	import SetBack from '$lib/coms/SetBack.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import Export from '$lib/coms/Export.svelte';
	import HandleQ from '$lib/coms-form/HandleQ.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let patient_id: number = $state(0);
	let { get_patients, count_patients, get_communes, get_districts, get_province, get_fields } =
		$derived(data);
	let n = $state(1);
	let total_male = $derived(get_patients.filter((e) => e.gender.toLowerCase() === 'male').length);
</script>

<DeleteModal action="?/delete_patient" id={patient_id} />
<!-- @_Visite_Modal -->
<div class="modal fade" id="modal-visite">
	<div class="modal-dialog modal-sm">
		<input
			id="close_visit_modal"
			class="hide"
			data-bs-dismiss="modal"
			aria-label="Close"
			type="hidden"
		/>
		<div class="modal-content">
			<div class="modal-header justify-content-between">
				<a
					href="/visit?visit_type=opd&patient_id={patient_id}"
					onclick={() => document.getElementById('close_visit_modal')?.click()}
					class="btn btn-primary btn-lg"
					><i class=" fas fa-stethoscope fa-4x"> </i> <br /> <span>OPD</span></a
				>
				<a
					href="/visit?visit_type=ipd&patient_id={patient_id}"
					onclick={() => document.getElementById('close_visit_modal')?.click()}
					class="btn btn-success btn-lg"
					><i class=" fas fa-procedures fa-4x"> </i> <br /> <span>IPD</span></a
				>
			</div>
		</div>
	</div>
</div>
<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('patient')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-restroom"></i>
					{locale.T('patient')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<!-- <h3 class="card-title">Fixed Header Table</h3> -->
		<HeaderQuery class="row g-1">
			<div class="col-auto">
				<Export
					title="Patients list"
					data={get_patients.map((e) => {
						return {
							id: e.id,
							name_khmer: e.name_khmer,
							name_latin: e.name_latin,
							dob: e.dob,
							gender: e.gender,
							blood_group: e.blood_group,
							education: e.education,
							nation: e.nation,
							material_status: e.material_status,
							work_place: e.work_place,
							occupation: e.occupation,
							telephone: e.telephone,
							village: e.village?.name_khmer,
							commune: e.commune?.name_khmer,
							district: e.district?.name_khmer,
							province: e.provice?.name_khmer
						};
					})}
				/>
			</div>

			<div class="col-2">
				<SelectParam
					placeholder={locale.T('province')}
					name="province_id"
					items={get_province?.map((e) => ({ id: e.id, name: e.name_khmer }))}
				/>
			</div>
			<div class="col-2">
				<SelectParam
					placeholder={locale.T('district')}
					name="district_id"
					items={get_districts?.map((e) => ({ id: e.id, name: e.name_khmer }))}
				/>
			</div>
			<div class="col-2">
				<SelectParam
					placeholder={locale.T('commune')}
					name="commune_id"
					items={get_communes?.map((e) => ({ id: e.id, name: e.name_khmer }))}
				/>
			</div>
			<div class="col-sm-1">
				<HandleQ q_name="q" />
			</div>
			<div class="col-auto ms-auto">
				<a href="/patient/create" class="btn btn-success"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('add_patient')}
				</a>
			</div>
		</HeaderQuery>
	</div>
	<div style="height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-hover table-bordered table-light">
			<thead class="sticky-top bg-light table-active">
				<tr class="text-center">
					<th class="text-center">{locale.T('n')}</th>
					<th style="width: 5%;" class="text-center">{locale.T('id')}</th>
					<th>{locale.T('picture')}</th>
					<th>{locale.T('patient_name')}</th>
					<th>{locale.T('dob')}</th>
					<th>{locale.T('gender')}</th>
					<th>{locale.T('blood_group')}</th>
					<th>{locale.T('address')}</th>
					<th></th>
				</tr>
			</thead>
			<tbody>
				{#each get_patients as item, index}
					{@const is_death = get_fields?.some(
						(e) => e.document?.progressNote?.patient_id === item.id
					)}
					<tr class={is_death ? 'table-danger text-center' : 'text-center'}>
						<td>{n + index}</td>
						<td>PT{item.id}</td>
						<td>
							<img
								src={item.uploads?.filename ? `${item.uploads?.filename}` : '/no-user.webp'}
								alt=""
								height="30"
							/>
						</td>
						<td>
							<a href="/patient/histrory?patient_id={item.id}">
								{item.name_khmer} <br />
								{item.name_latin}
							</a>
							<GenderAge dob={item.dob} date={new Date()} gender={item.gender} />
						</td>
						<td>
							<DDMMYYYYFormat style="date" date={item.dob} />
						</td>
						<td>{locale.T(item.gender.toLowerCase() as 'male' | 'female')}</td>

						<td>
							{#if item.blood_group}
								{item?.blood_group}
							{:else}
								{locale.T('unknown')}
							{/if}
						</td>
						<td class="text-start">
							{item.village?.type ?? ''}
							{item.village?.name_khmer ?? ''}
							{item.commune?.type ?? ''}
							{item.commune?.name_khmer ?? ''}
							{item.district?.type ?? ''}
							{item.district?.name_khmer ?? ''}
							{item.provice?.type ?? ''}
							{item.provice?.name_khmer ?? ''}
						</td>
						<td>
							<div class="text-start">
								<button
									disabled={is_death}
									aria-label="modalvisite"
									onclick={() => {
										patient_id = item.id;
									}}
									type="button"
									class="btn btn-success btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#modal-visite"
									><i class="fa-solid fa-circle-plus"></i>
								</button>
								<a
									href="/patient/create?patient_id={item.id}&province_id={item.province_id}&district_id={item.district_id}&commune_id={item.commune_id}&village_id={item.village_id}"
									class="btn btn-success btn-sm"
									aria-label="createpatient"
									><i class="fa-solid fa-file-pen"></i>
								</a>
								<SetBack href="/print/{item?.id}/label/barcode" class="btn btn-success btn-sm ">
									<i class="fa-solid fa-barcode"></i>
								</SetBack>
								<SetBack href="/print/{item?.id}/label/barcode" class="btn btn-success btn-sm ">
									<i class="fa-solid fa-address-card"></i>
								</SetBack>
								<button
									aria-label="deletemodal"
									onclick={() => {
										patient_id = item.id;
									}}
									type="button"
									class="btn btn-danger btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-trash-can"></i>
								</button>
							</div>
						</td>
					</tr>
				{/each}
				<tr class="table-success">
					<td colspan="9" class="text-center">
						{locale.T('total')}: {get_patients.length}
						{locale.T('people')},
						{locale.T('male')}: {total_male}
						{locale.T('female')}: {get_patients.length - total_male}
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations items={count_patients} bind:n />
	</div>
</div>
