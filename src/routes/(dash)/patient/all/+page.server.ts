import { db } from '$lib/server/db';
import { uploads, patient, commune, district, village, fields } from '$lib/server/schemas';
import type { Actions, PageServerLoad } from './$types';
import { and, count, desc, eq, inArray, like, or } from 'drizzle-orm';
import { pagination } from '$lib/server/utils';
import { logError } from '$lib/server/utils/telegram';
import { fileHandle } from '$lib/server/upload';

export const load = (async ({ parent, url }) => {
	await parent();
	const q = url.searchParams.get('q') || '';
	const province_id = url.searchParams.get('province_id') || '';
	const commune_id = url.searchParams.get('commune_id') || '';
	const district_id = url.searchParams.get('district_id') || '';
	const village_id = url.searchParams.get('village_id') || '';
	const get_provinces = await db.query.provice.findMany({
		with: {
			district: {
				with: {
					commune: true
				}
			}
		}
	});
	const get_communes = await db.query.commune.findMany({
		where: eq(commune.district_id, +district_id)
	});
	const get_districts = await db.query.district.findMany({
		where: eq(district.province_id, +province_id)
	});
	const get_villages = await db.query.village.findMany({
		where: eq(village.commune_id, +commune_id)
	});
	const get_patients = await db.query.patient.findMany({
		where: and(
			province_id ? eq(patient.province_id, +province_id) : undefined,
			commune_id ? eq(patient.commune_id, +commune_id) : undefined,
			district_id ? eq(patient.district_id, +district_id) : undefined,
			village_id ? eq(patient.village_id, +village_id) : undefined,
			or(
				like(patient.name_khmer, `%${q}%`),
				like(patient.name_latin, `%${q}%`),
				like(patient.id, `%${q}%`)
			)
		),
		orderBy: desc(patient.id),
		with: {
			provice: true,
			district: true,
			commune: true,
			village: true
		},
		...pagination(url)
	});
	const count_patients = await db
		.select({ count: count() })
		.from(patient)
		.where(
			and(
				province_id ? eq(patient.province_id, +province_id) : undefined,
				commune_id ? eq(patient.commune_id, +commune_id) : undefined,
				district_id ? eq(patient.district_id, +district_id) : undefined,
				village_id ? eq(patient.village_id, +village_id) : undefined,
				or(
					like(patient.name_khmer, `%${q}%`),
					like(patient.name_latin, `%${q}%`),
					like(patient.id, `%${q}%`)
				)
			)
		);
	const get_uploads = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_type, 'patient'),
			inArray(
				uploads.related_id,
				get_patients.map((e) => e.id)
			)
		)
	});
	const get_fields = await db.query.fields.findMany({
		where: and(like(fields.result, '%death%')),
		with: {
			document: {
				with: {
					progressNote: {
						columns: {
							patient_id: true
						}
					}
				}
			}
		}
	});
	return {
		get_patients: get_patients.map((e) => {
			return {
				...e,
				uploads: get_uploads.find((ee) => ee.related_id === e.id)
			};
		}),
		get_fields,
		get_province: get_provinces,
		count_patients: count_patients[0].count,
		get_communes,
		get_districts,
		get_villages
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	delete_patient: async ({ request, url }) => {
		const body = await request.formData();
		const id = body.get('id') ?? '';
		const get_patient = await db.query.patient.findFirst({
			where: eq(patient.id, +id)
		});
		const get_upload = await db.query.uploads.findFirst({
			where: and(eq(uploads.related_type, 'patient'), eq(uploads.related_id, +id))
		});
		if (get_patient) {
			await db
				.delete(patient)
				.where(eq(patient.id, get_patient.id))
				.catch((e) => logError({ url, body, err: e }));

			await fileHandle.drop(get_upload?.filename ?? '');
		}
	}
};
