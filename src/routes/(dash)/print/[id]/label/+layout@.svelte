<script lang="ts">
	import { browser } from '$app/environment';
	import { locale } from '$lib/translations/locales.svelte';
	interface Props {
		children?: import('svelte').Snippet;
	}
	let { children }: Props = $props();
	function handlePrint() {
		window.scrollTo({ top: 0, behavior: 'smooth' });
		setTimeout(() => {
			window.print();
		}, 500); // Adjust the timeout as needed
	}
	let from_url = $derived.by(() => {
		const url = browser ? sessionStorage?.getItem('from_url') : '';
		if (url) {
			return url;
		} else {
			return '';
		}
	});

	function onclick() {
		setTimeout(() => {
			if (browser) {
				sessionStorage?.removeItem('from_url');
			}
		}, 300);
	}
</script>

<div class="main border-2">
	<div class="row g-0 sticky-top">
		<div class="col-auto">
			{#if from_url}
				<a
					{onclick}
					href={from_url}
					class="text-center rounded-0 w-100 mt-1 mb-1 btn btn-warning btn-lg d-print-none text-decoration-none"
					><i class="fa-solid fa-arrow-left"></i> {locale.T('back')}</a
				>
			{/if}
		</div>
		<div class="col">
			<button
				onclick={handlePrint}
				class="text-center rounded-0 w-100 mt-1 mb-1 btn btn-primary btn-lg d-print-none text-decoration-none"
			>
				<i class="fa-solid fa-print"></i>
				{locale.T('print')}
			</button>
		</div>
	</div>
	<div class="shadow">
		{@render children?.()}
	</div>
</div>

<style>
	.shadow {
		box-shadow: 0 20px 38px rgba(0, 0, 0, 0.5);
		padding: 2mm;
	}
	.main {
		max-width: 1200px;
		margin-left: auto;
		margin-right: auto;
		zoom: 85%;
	}
	@media print {
		.main {
			zoom: 100% !important;
		}
		.shadow {
			box-shadow: none !important;
			padding: 0 !important;
		}
	}
</style>
