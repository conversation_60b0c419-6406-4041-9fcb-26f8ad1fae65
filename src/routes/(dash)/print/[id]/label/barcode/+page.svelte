<script lang="ts">
	import BarcodePatient from '$lib/coms-ipd-opd/BarcodePatient.svelte';
	import type { PageServerData } from './$types';
	let { data }: { data: PageServerData } = $props();
	let { get_patient } = $derived(data);
</script>

<div class="center">
	<BarcodePatient
		info={{
			dob: get_patient?.dob ?? '',
			gender: get_patient?.gender ?? '',
			id: get_patient?.id ?? 0,
			name_khmer: get_patient?.name_khmer ?? '',
			name_latin: get_patient?.name_latin ?? ''
		}}
	/>
</div>

<style>
	.center {
		margin: auto;
		width: 20vh;
	}
	@media print {
		.center {
			width: 100%;
			margin: 0 auto;
		}
		@page {
			margin: 0mm;
			padding: 0mm;
		}

		/* :global(.main) {
			display: inline-block;
			text-align: left;
			margin: 0 auto !important;
		} */
	}
</style>
