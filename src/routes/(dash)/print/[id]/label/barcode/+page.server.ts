import { db } from '$lib/server/db';
import { patient } from '$lib/server/schemas';
import { eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

export const load = (async ({ params }) => {
	const { id } = params;
	const get_patient = await db.query.patient.findFirst({
		where: eq(patient.id, +id),
		with: {
			provice: true,
			district: true,
			commune: true,
			village: true
		}
	});
	return {
		get_patient
	};
}) satisfies PageServerLoad;
