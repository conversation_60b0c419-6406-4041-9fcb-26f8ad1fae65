<script lang="ts">
	import type { PageServerData } from './$types';
	let { data }: { data: PageServerData } = $props();
	let { get_prescriptions } = $derived(data);
</script>

<div id="print_all_prescription" class="row g-1 label_prescription">
	{#each get_prescriptions as item}
		<div class="label-prescription-page">
			<div class="table-responsive">
				<table class="table table-sm table-light table-bordered py-0 my-0">
					<thead>
						<tr>
							<td class="text-center" colspan="6">
								{item.product?.products?.slice(0, 35)}
								<br />
								{#if item.product?.generic_name}
									{item.product?.generic_name?.slice(0, 35) ?? ''}
								{/if}
							</td>
						</tr>
						<tr>
							<td class="text-center" colspan="6">{item.use?.slice(0, 35) ?? ''}</td>
						</tr>
						<tr class="text-center">
							<th>ព្រឹក</th>
							<th>ថ្ងៃត្រង់</th>
							<th>រសៀល</th>
							<th>ល្ងាច</th>
							<th>យប់</th>
						</tr>
						<tr class="text-center">
							<th>
								{item.morning ?? ''}
							</th>
							<th>
								{item.noon ?? ''}
							</th>
							<th>
								{item.afternoon ?? ''}
							</th>
							<th>
								{item.evening ?? ''}
							</th>
							<th>
								{item.night ?? ''}
							</th>
						</tr>
						<tr>
							<td colspan="6" class="text-center">
								រយៈពេលប្រើ {item?.duration ?? ''} ឱសថសរុប {item?.amount ?? ''}
								{item?.unit?.unit ?? ''}</td
							>
						</tr>
					</thead>
				</table>
			</div>
		</div>
	{/each}
</div>

<style>
	@media print {
		@page {
			margin: 0mm;
		}
		.label_prescription {
			font-size: 10.5px;
			max-width: 60mm;
			min-height: 40mm;
			padding: 0mm;
			margin: 0mm;
			display: block !important;
		}
		.label-prescription-page {
			display: block;
			break-inside: avoid-page;
			page-break-inside: avoid;
			overflow: hidden;
			padding: 1px;
			margin: 0 auto;
		}
		.label-prescription-page + .label-prescription-page {
			break-before: page;
			page-break-before: always;
		}
		/* table {
			border-collapse: collapse;
		} */
		th,
		tr,
		table,
		td {
			border: 1px solid black;
		}
		/* Ensure table borders are visible across page breaks */
		.table,
		.table-bordered {
			border-collapse: separate !important;
			border-spacing: 0 !important;
		}
		.table-responsive {
			border-color: black;
		}
	}
</style>
