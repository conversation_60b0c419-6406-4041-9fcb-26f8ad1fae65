import { db } from '$lib/server/db';
import { presrciption } from '$lib/server/schemas';
import { and, eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

export const load = (async ({ params, url }) => {
	const { id } = params;
	const type = url.searchParams.get('type') ?? '';
	const get_prescriptions = await db.query.presrciption.findMany({
		where: and(
			type === 'opd'
				? eq(presrciption.visit_id, +id)
				: type === 'ipd'
					? eq(presrciption.progress_note_id, +id)
					: eq(presrciption.id, +id)
		),
		with: {
			product: {
				with: {
					category: true,
					unit: true
				}
			},
			unit: true
		}
	});
	return {
		get_prescriptions
	};
}) satisfies PageServerLoad;
