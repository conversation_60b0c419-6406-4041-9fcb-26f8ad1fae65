<script lang="ts">
	import type { PageServerData } from './$types';
	import ServiceContract from '$lib/coms-document/ServiceContract.svelte';
	import RequestDischarge from '$lib/coms-document/RequestDischarge.svelte';
	import PatientTranswer from '$lib/coms-document/PatientTranswer.svelte';
	import BirthCertificate from '$lib/coms-document/BirthCertificate.svelte';
	import BirthRecognize from '$lib/coms-document/BirthRecognize.svelte';
	import ResultChecking from '$lib/coms-document/ResultChecking.svelte';
	import { dobToAge } from '$lib/helper';
	import AcceptLeaving from '$lib/coms-document/AcceptLeaving.svelte';
	import { page } from '$app/state';
	let { data }: { data: PageServerData } = $props();
	let {
		get_documents,
		patient_info,
		get_words,
		get_clinich_info,
		get_upload,
		get_document,
		get_document_setting
	} = $derived(data);
	let title = $derived(page.url.searchParams.get('title') ?? '');
	let birth_certificate_1 = $derived(get_documents.find((e) => e.title === 'birth_certificate_1'));
</script>

<div class="zoom">
	{#if title}
		<div>
			<div id="print_document">
				{#if title === 'accept_leaving'}
					<AcceptLeaving
						p_name={patient_info?.patient?.name_khmer
							?.concat(`(${patient_info?.patient?.name_latin}) `)
							.concat(
								`ភេទ ${patient_info?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
							)
							.concat(
								`អាយុ ${dobToAge(patient_info?.patient?.dob ?? '', patient_info?.date_checkup ?? '')}`
							)
							.toLowerCase()
							.replace('month', 'ខែ')
							.toLowerCase()
							.replace('year', 'ឆ្នាំ')
							.toLowerCase()
							.replace('day', 'ថ្ងៃ') ?? ''}
						p_nation={patient_info?.patient?.nation ?? ''}
						address={{
							village: patient_info?.patient?.village,
							commune: patient_info?.patient?.commune,
							district: patient_info?.patient?.district,
							provice: patient_info?.patient?.provice
						}}
						{get_document_setting}
						fields={get_document?.fields ?? []}
						p_date_checkup={patient_info?.date_checkup ?? ''}
						p_date_checkout={patient_info?.date_checkout ?? ''}
						title_khm={get_clinich_info?.title_khm ?? ''}
						title_eng={get_clinich_info?.title_eng ?? ''}
						logo={get_upload?.filename ?? ''}
					/>
				{/if}
				{#if title === 'service_contract'}
					<ServiceContract
						{get_document_setting}
						fields={get_document?.fields ?? []}
						address={{
							village: patient_info?.patient?.village,
							commune: patient_info?.patient?.commune,
							district: patient_info?.patient?.district,
							provice: patient_info?.patient?.provice
						}}
						occupation_list={get_words.filter((e) => e.type === 'occupation').map((e) => e.text)}
						p_name={patient_info?.patient?.name_khmer
							?.concat(`(${patient_info?.patient?.name_latin}) `)
							.concat(
								`ភេទ ${patient_info?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
							)
							.concat(
								`អាយុ ${dobToAge(patient_info?.patient?.dob ?? '', patient_info?.date_checkup ?? '')}`
							)
							.toLowerCase()
							.replace('month', 'ខែ')
							.toLowerCase()
							.replace('year', 'ឆ្នាំ')
							.toLowerCase()
							.replace('day', 'ថ្ងៃ') ?? ''}
						p_nation={patient_info?.patient?.nation ?? ''}
						title_khm={get_clinich_info?.title_khm ?? ''}
						title_eng={get_clinich_info?.title_eng ?? ''}
						logo={get_upload?.filename ?? ''}
					/>
				{/if}
				{#if title === 'request_discharge'}
					<RequestDischarge
						{get_document_setting}
						nations_list={get_words.filter((e) => e.type === 'nation').map((e) => e.text)}
						fields={get_document?.fields ?? []}
						address={{
							village: patient_info?.patient?.village,
							commune: patient_info?.patient?.commune,
							district: patient_info?.patient?.district,
							provice: patient_info?.patient?.provice
						}}
						occupation_list={get_words.filter((e) => e.type === 'occupation').map((e) => e.text)}
						p_name={patient_info?.patient?.name_khmer
							?.concat(`(${patient_info?.patient?.name_latin}) `)
							.concat(
								`ភេទ ${patient_info?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
							)
							.concat(
								`អាយុ ${dobToAge(patient_info?.patient?.dob ?? '', patient_info?.date_checkup ?? '')}`
							)
							.toLowerCase()
							.replace('month', 'ខែ')
							.toLowerCase()
							.replace('year', 'ឆ្នាំ')
							.toLowerCase()
							.replace('day', 'ថ្ងៃ') ?? ''}
						p_nation={patient_info?.patient?.nation ?? ''}
						p_contact={patient_info?.patient?.telephone ?? ''}
						p_date_checkup={patient_info?.date_checkup ?? ''}
						p_department={patient_info?.department?.products ?? ''}
						title_khm={get_clinich_info?.title_khm ?? ''}
						title_eng={get_clinich_info?.title_eng ?? ''}
						logo={get_upload?.filename ?? ''}
					/>
				{/if}
				{#if title === 'patient_transfer'}
					<PatientTranswer
						{get_document_setting}
						fields={get_document?.fields ?? []}
						p_address={{
							village: patient_info?.patient?.village,
							commune: patient_info?.patient?.commune,
							district: patient_info?.patient?.district,
							provice: patient_info?.patient?.provice
						}}
						p_name={patient_info?.patient?.name_khmer
							?.concat(`(${patient_info?.patient?.name_latin}) `)
							.concat(
								`ភេទ ${patient_info?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
							)
							.concat(
								`អាយុ ${dobToAge(patient_info?.patient?.dob ?? '', patient_info?.date_checkup ?? '')}`
							)
							.toLowerCase()
							.replace('month', 'ខែ')
							.toLowerCase()
							.replace('year', 'ឆ្នាំ')
							.toLowerCase()
							.replace('day', 'ថ្ងៃ') ?? ''}
						p_nation={patient_info?.patient?.nation ?? ''}
						p_date_checkup={patient_info?.date_checkup ?? ''}
						title_khm={get_clinich_info?.title_khm ?? ''}
						title_eng={get_clinich_info?.title_eng ?? ''}
						logo={get_upload?.filename ?? ''}
					/>
				{/if}
				{#if title === 'birth_recognize'}
					<BirthRecognize
						{get_document_setting}
						p_address={{
							village: patient_info?.patient?.village,
							commune: patient_info?.patient?.commune,
							district: patient_info?.patient?.district,
							provice: patient_info?.patient?.provice
						}}
						p_name={patient_info?.patient?.name_khmer
							?.concat(`(${patient_info?.patient?.name_latin}) `)
							.concat(
								`ភេទ ${patient_info?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
							)
							.concat(
								`អាយុ ${dobToAge(patient_info?.patient?.dob ?? '', patient_info?.date_checkup ?? '')}`
							)
							.toLowerCase()
							.replace('month', 'ខែ')
							.toLowerCase()
							.replace('year', 'ឆ្នាំ')
							.toLowerCase()
							.replace('day', 'ថ្ងៃ') ?? ''}
						p_occupation={patient_info?.patient?.occupation ?? ''}
						fields={get_document?.fields ?? []}
						occupation_list={get_words.filter((e) => e.type === 'occupation').map((e) => e.text)}
						title_khm={get_clinich_info?.title_khm ?? ''}
						title_eng={get_clinich_info?.title_eng ?? ''}
						logo={get_upload?.filename ?? ''}
					/>
				{/if}
				{#if title === 'birth_certificate_1'}
					<BirthCertificate
						{get_document_setting}
						p_contact={patient_info?.patient?.telephone ?? ''}
						p_dob={patient_info?.patient?.dob ?? ''}
						p_address={{
							village: patient_info?.patient?.village,
							commune: patient_info?.patient?.commune,
							district: patient_info?.patient?.district,
							provice: patient_info?.patient?.provice
						}}
						p_name_khmer={patient_info?.patient?.name_khmer ?? ''}
						p_nation={patient_info?.patient?.nation ?? ''}
						p_date_checkup={patient_info?.date_checkup ?? ''}
						nations_list={get_words.filter((e) => e.type === 'nation').map((e) => e.text)}
						fields_={get_document?.fields ?? []}
						birth_certificate="birth_certificate_1"
						title_khm={get_clinich_info?.title_khm ?? ''}
						title_eng={get_clinich_info?.title_eng ?? ''}
						logo={get_upload?.filename ?? ''}
					/>
				{/if}
				{#if title === 'birth_certificate_2'}
					<BirthCertificate
						{get_document_setting}
						p_contact={patient_info?.patient?.telephone ?? ''}
						p_dob={patient_info?.patient?.dob ?? ''}
						p_address={{
							village: patient_info?.patient?.village,
							commune: patient_info?.patient?.commune,
							district: patient_info?.patient?.district,
							provice: patient_info?.patient?.provice
						}}
						p_name_khmer={patient_info?.patient?.name_khmer ?? ''}
						p_nation={patient_info?.patient?.nation ?? ''}
						p_date_checkup={patient_info?.date_checkup ?? ''}
						nations_list={get_words.filter((e) => e.type === 'nation').map((e) => e.text)}
						fields_={get_document?.fields ?? []}
						birth_certificate="birth_certificate_2"
						title_khm={get_clinich_info?.title_khm ?? ''}
						title_eng={get_clinich_info?.title_eng ?? ''}
						logo={get_upload?.filename ?? ''}
						fields_1={birth_certificate_1?.fields ?? []}
					/>
				{/if}
				{#if title === 'birth_certificate_3'}
					<BirthCertificate
						{get_document_setting}
						p_contact={patient_info?.patient?.telephone ?? ''}
						p_dob={patient_info?.patient?.dob ?? ''}
						p_address={{
							village: patient_info?.patient?.village,
							commune: patient_info?.patient?.commune,
							district: patient_info?.patient?.district,
							provice: patient_info?.patient?.provice
						}}
						p_name_khmer={patient_info?.patient?.name_khmer ?? ''}
						p_nation={patient_info?.patient?.nation ?? ''}
						p_date_checkup={patient_info?.date_checkup ?? ''}
						nations_list={get_words.filter((e) => e.type === 'nation').map((e) => e.text)}
						fields_={get_document?.fields ?? []}
						birth_certificate="birth_certificate_3"
						title_khm={get_clinich_info?.title_khm ?? ''}
						title_eng={get_clinich_info?.title_eng ?? ''}
						logo={get_upload?.filename ?? ''}
						fields_1={birth_certificate_1?.fields ?? []}
					/>
				{/if}
				{#if title === 'birth_certificate_4'}
					<BirthCertificate
						{get_document_setting}
						p_contact={patient_info?.patient?.telephone ?? ''}
						p_dob={patient_info?.patient?.dob ?? ''}
						p_address={{
							village: patient_info?.patient?.village,
							commune: patient_info?.patient?.commune,
							district: patient_info?.patient?.district,
							provice: patient_info?.patient?.provice
						}}
						p_name_khmer={patient_info?.patient?.name_khmer ?? ''}
						p_nation={patient_info?.patient?.nation ?? ''}
						p_date_checkup={patient_info?.date_checkup ?? ''}
						nations_list={get_words.filter((e) => e.type === 'nation').map((e) => e.text)}
						fields_={get_document?.fields ?? []}
						birth_certificate="birth_certificate_4"
						title_khm={get_clinich_info?.title_khm ?? ''}
						title_eng={get_clinich_info?.title_eng ?? ''}
						logo={get_upload?.filename ?? ''}
						fields_1={birth_certificate_1?.fields ?? []}
					/>
				{/if}
				{#if title === 'result_checking'}
					<ResultChecking
						{get_document_setting}
						fields={get_document?.fields ?? []}
						p_address={{
							village: patient_info?.patient?.village,
							commune: patient_info?.patient?.commune,
							district: patient_info?.patient?.district,
							provice: patient_info?.patient?.provice
						}}
						p_name_khmer={patient_info?.patient?.name_khmer ?? ''}
						p_name_latin={patient_info?.patient?.name_latin ?? ''}
						p_occupation={patient_info?.patient?.occupation ?? ''}
						p_id_card_passport={patient_info?.patient?.id_cart_passport ?? ''}
						p_dob={patient_info?.patient?.dob ?? ''}
						p_phone={patient_info?.patient?.telephone ?? ''}
						p_nation={patient_info?.patient?.nation ?? ''}
						p_gender={patient_info?.patient?.gender
							.toLowerCase()
							.replace('male', 'ប្រុស')
							.replace('female', 'ស្រី') ?? ''}
						title_khm={get_clinich_info?.title_khm ?? ''}
						title_eng={get_clinich_info?.title_eng ?? ''}
						logo={get_upload?.filename ?? ''}
					/>
				{/if}
			</div>
		</div>
	{/if}
</div>

<style>
	.zoom {
		zoom: 110%;
	}
	@page {
		size: A4;
		padding: 5mm 5mm 0mm 5mm;
		margin: 0mm 0mm 5mm 0mm;
		@bottom-right {
			content: counter(page) ' / ' counter(pages);
			font-size: 13px;
			padding-bottom: 5mm;
			padding-right: 5mm;
		}
	}
	@media print {
		.zoom {
			zoom: 100% !important;
		}
	}
</style>
