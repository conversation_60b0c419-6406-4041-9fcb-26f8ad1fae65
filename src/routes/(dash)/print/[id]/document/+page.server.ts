import { db } from '$lib/server/db';
import type { PageServerLoad } from './$types';
import { document, progressNote, uploads, visit, words } from '$lib/server/schemas';
import { and, eq } from 'drizzle-orm';
import { error } from '@sveltejs/kit';
export const load = (async ({ params, url }) => {
	const { id } = params;
	const title = url.searchParams.get('title') ?? '';
	const type = url.searchParams.get('type') ?? '';
	if (!['opd', 'ipd'].includes(type) || !type) {
		error(404, 'Not Found');
	}
	const get_document = await db.query.document.findFirst({
		where: and(
			type === 'opd'
				? eq(document.visit_id, +id)
				: eq(document.progress_note_id, +id),
			eq(document.title, title)),
		with: {
			fields: true
		}
	});
	const get_documents = await db.query.document.findMany({
		where: eq(document.progress_note_id, +id),
		with: {
			fields: true
		}
	});
	let patient_info
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +id),
		with: {
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			department: true
		}
	});

	const get_visit = await db.query.visit.findFirst({
		where: eq(visit.id, +id),
		with: {
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			department: true
		}
	});
	if (type === 'opd') {
		patient_info = {
			department: get_visit?.department,
			date_checkup: get_visit?.date_checkup,
			date_checkout: get_visit?.date_checkout,
			patient: get_visit?.patient

		}
	} if (type === 'ipd') {
		patient_info = {
			department: get_progress_note?.department,
			date_checkup: get_progress_note?.date_checkup,
			date_checkout: get_progress_note?.date_checkout,
			patient: get_progress_note?.patient

		}
	}
	const get_words = await db.query.words.findMany({
		where: eq(words.category, 'patient')
	});
	const get_clinich_info = await db.query.clinicinfo.findFirst({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'clinicinfo'), eq(uploads.mimeType, 'logo0'))
	});
	const get_upload_doc = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'document'), eq(uploads.related_id, get_document?.id || 0))
	});
	const get_document_setting = await db.query.documentSetting.findFirst({});

	return {
		get_document: {
			...get_document,
			uploads: get_upload_doc
		},
		get_document_setting: get_document_setting,
		patient_info,
		get_words,
		get_clinich_info,
		get_upload,
		get_documents
	};
}) satisfies PageServerLoad;
