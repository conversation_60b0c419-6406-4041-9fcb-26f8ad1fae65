<script lang="ts">
	import Sign from '$lib/coms-report/Sign.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import TimeFormat from '$lib/coms/TimeFormat.svelte';
	import { dobToAge } from '$lib/helper';
	import type { PageServerData } from './$types';
	let { data }: { data: PageServerData } = $props();
	let { get_service } = $derived(data);
	let get_visit = $derived(get_service?.visit_id ? get_service.visit : get_service?.progressNote);
</script>

<h5 class="en_font_times_new_roman text-center pt-1">Surgery Post Operative Report</h5>
<u class="fs-5">I. <span class="kh_font_muol_light"> ព័តមានអ្នកជំងឺ </span></u>
<ul>
	<p
		class="fs-5"
		style="  text-align:justify;  
	text-justify:initial;}"
	>
		(ID) PT{get_visit?.patient_id} VS{get_visit?.id} <br />
		ឈ្មោះ {get_visit?.patient?.name_khmer ?? ''}
		{`(${get_visit?.patient?.name_latin ?? ''})`}
		ភេទ {get_visit?.patient?.gender?.toLowerCase() === 'male' ? 'ប្រុស' : 'ស្រី'}
		ថ្ងៃខែឆ្នាំកំណើត
		<DDMMYYYYFormat date={get_visit?.patient?.dob} style="date" />
		អាយុ
		{dobToAge(get_visit?.patient.dob, get_visit?.date_checkup) ?? '........'}
		<br />
		មុខរបរ........................
		<br />
		អាសយដ្ឋាន
		{get_visit?.patient?.village?.type ?? ''}
		{get_visit?.patient?.village?.name_khmer.concat(',') ?? ''}
		{get_visit?.patient?.commune?.type ?? ''}
		{get_visit?.patient?.commune?.name_khmer.concat(',') ?? ''}
		{get_visit?.patient?.district?.type ?? ''}
		{get_visit?.patient?.district?.name_khmer.concat(',') ?? ''}
		{get_visit?.patient?.provice?.type ?? ''}
		{get_visit?.patient?.provice?.name_khmer ?? ''} <br />
		ថ្ងៃខែឆ្នាំចូលពិនិត្យ
		<DDMMYYYYFormat date={get_visit?.date_checkup} style="date" />
		ផ្នែក
		{get_visit?.department?.products}
	</p>
</ul>

<ul>
	{#if get_service}
		{#if get_visit?.accessment?.diagnosis_or_problem}
			<li class="fs-4 pt-2">Diagnosis</li>
			<h5>
				{get_visit?.accessment?.diagnosis_or_problem}
			</h5>
		{/if}
		{#if get_visit?.accessment?.differential_diagnosis}
			<li class="fs-4 pt-2">Differential Diagnosis</li>
			<h5>
				{get_visit?.accessment?.differential_diagnosis}
			</h5>
		{/if}
		{#if get_visit?.accessment?.assessment_process}
			<li class="fs-4 pt-2">Accessment Process</li>
			<h5 class="text-break">
				{get_visit?.accessment?.assessment_process}
			</h5>
			<li class="fs-4 pt-2">Protocol</li>
		{/if}
		<div class="card">
			<div class="card-header">
				{get_service?.product?.products ?? ''}
			</div>
			<div class="card-body p-0 m-0">
				<table class="table p-0 m-0 table-sm table-light">
					<tbody class="">
						<tr>
							<td style="width: 12.5%;">Surgeon</td>
							<td>:</td>
							<td style="width: 12.5%;"> {get_service?.operationProtocol?.surgeon ?? ''} </td>
							<td style="width: 12.5%;">Scrub Nurse</td>
							<td>:</td>
							<td style="width: 12.5%;"> {get_service?.operationProtocol?.scrub_nurse ?? ''} </td>
							<td style="width: 12.5%;">Start Time</td>
							<td>:</td>
							<td style="width: 12.5%;">
								<TimeFormat time={get_service?.operationProtocol?.start_time} />
							</td>
						</tr>
						<tr>
							<td style="width: 12.5%;">Assistant Surgeon</td>
							<td>:</td>
							<td style="width: 12.5%;">
								{get_service?.operationProtocol?.assistant_surgeon ?? ''}
							</td>

							<td style="width: 12.5%;">Circulation Nurse block</td>
							<td>:</td>
							<td style="width: 12.5%;">
								{get_service?.operationProtocol?.cirulating_nurse_block ?? ''}
							</td>
							<td style="width: 12.5%;">Finish Time</td>
							<td>:</td>
							<td style="width: 12.5%;">
								<TimeFormat time={get_service?.operationProtocol?.finish_time} />
							</td>
						</tr>
						<tr>
							<td style="width: 12.5%;">Anesthetist</td>
							<td>:</td>
							<td style="width: 12.5%;"> {get_service?.operationProtocol?.anesthetist ?? ''} </td>

							<td style="width: 12.5%;">Midwife</td>
							<td>:</td>
							<td style="width: 12.5%;"> {get_service?.operationProtocol?.midwife ?? ''} </td>

							<td style="width: 12.5%;">Pre Diagnosis</td>
							<td>:</td>
							<td style="width: 12.5%;">
								{get_service?.operationProtocol?.pre_diagnosis ?? ''}
							</td>
						</tr>
						<tr>
							<td style="width: 12.5%;">Assistant Anesthetist</td>
							<td>:</td>
							<td style="width: 12.5%;">
								{get_service?.operationProtocol?.assistant_anesthetist ?? ''}
							</td>

							<td style="width: 12.5%;">Dates</td>
							<td>:</td>
							<td style="width: 12.5%;">
								<DDMMYYYYFormat date={get_service?.operationProtocol?.date} style="date" />
							</td>
							<td style="width: 12.5%;">Post Diagnosis</td>
							<td>:</td>
							<td style="width: 12.5%;">
								{get_service?.operationProtocol?.post_diagnosis ?? ''}
							</td>
						</tr>
						<tr>
							<td style="width: 12.5%;">Type Anesthesia</td>
							<td>:</td>
							<td style="width: 12.5%;">
								{get_service?.operationProtocol?.type_anesthesia ?? ''}
							</td>
							<td style="width: 12.5%;">Opertive Technique</td>
							<td>:</td>
							<td style="width: 12.5%;">
								{get_service?.operationProtocol?.opertive_technique ?? ''}
							</td>
							<td style="width: 12.5%;">Blood Less</td>
							<td>:</td>
							<td style="width: 12.5%;"> {get_service?.operationProtocol?.blood_less ?? ''} </td>
						</tr>
						<tr>
							<td style="width: 12.5%;">Notes</td>
							<td>:</td>
							<td colspan="7" style="width: 12.5%;">
								{get_service?.operationProtocol?.notes ?? ''}
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	{/if}
</ul>

<br />
<Sign
	left={{
		title: `<div class="kh_font_muol_light">បានឃើញ និងឯកភាព </div>`,
		role: 'ប្រធានផ្នែក'
	}}
	right={{
		role: `គ្រូពេទ្យពិនិត្យ`,
		date: get_visit?.date_checkup,
		name: get_visit?.staff?.name_khmer,
		img: '/sign.png'
	}}
/>

<style>
	@page {
		size: A4;
		padding: 5mm 5mm 0mm 5mm;
		margin: 0mm 0mm 5mm 0mm;
		@bottom-right {
			content: counter(page) ' / ' counter(pages);
			font-size: 13px;
			padding-bottom: 5mm;
			padding-right: 5mm;
		}
	}
</style>
