import { db } from '$lib/server/db';
import { group, template } from '$lib/server/schemas';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { asc, eq } from 'drizzle-orm';
import { logError } from '$lib/server/utils/telegram';

export const load = (async () => {
	const get_groups = await db.query.group.findMany({
		where: eq(group.category_id, 2)
	});
	const get_templates = await db.query.template.findMany({
		with: {
			group: true
		},
		orderBy: asc(template.diagnosis)
	});

	return {
		get_templates,
		get_groups
	};
}) satisfies PageServerLoad;
export const actions: Actions = {
	create_template: async ({ request, url }) => {
		const body = await request.formData();
		const { diagnosis, template_, group_id } = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			diagnosis: false,
			template_: false,
			group_id: false
		};
		if (!diagnosis.trim()) validErr.diagnosis = true;
		if (!template_) validErr.template_ = true;
		if (!group_id) validErr.group_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		await db
			.insert(template)
			.values({
				diagnosis: diagnosis,
				template: template_,
				group_id: +group_id
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_template: async ({ request, url }) => {
		const body = await request.formData();
		const { diagnosis, template_, template_id, group_id } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		const validErr = {
			diagnosis: false,
			template_: false,
			group_id: false
		};
		if (!diagnosis.trim()) validErr.diagnosis = true;
		if (!template_) validErr.template_ = true;
		if (!group_id) validErr.group_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		await db
			.update(template)
			.set({
				diagnosis: diagnosis,
				template: template_,
				group_id: +group_id
			})
			.where(eq(template.id, +template_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_template: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(template)
			.where(eq(template.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
