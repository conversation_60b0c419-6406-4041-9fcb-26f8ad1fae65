import { db } from '$lib/server/db';
import { bed, product, category, room, ward } from '$lib/server/schemas';
import { logError } from '$lib/server/utils/telegram';
import type { Actions, PageServerLoad } from './$types';
import { eq, like } from 'drizzle-orm';

export const load = (async ({ params }) => {
	const get_category = await db.query.category.findFirst({
		where: like(category.name, 'room')
	});
	const get_products = await db.query.product.findMany({
		where: eq(product.category_id, get_category?.id || 0)
	});
	const get_departments = await db.query.product.findMany({
		where: eq(product.category_id, 11)
	});

	const get_ward = await db.query.ward.findFirst({
		with: {
			room: {
				with: {
					bed: true,
					department: true,
					product: true
				}
			},
			bed: true
		},
		where: eq(ward.id, +params.ward_id)
	});
	const get_wards = await db.query.ward.findMany({
		with: {
			room: {
				with: {
					bed: true,
					department: true,
					product: true
				}
			},
			bed: true
		}
	});
	return {
		get_products,
		get_wards,
		get_ward,
		get_departments
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_ward: async ({ request, url }) => {
		const body = await request.formData();
		const { ward: ward_ } = Object.fromEntries(body) as Record<string, string>;
		await db
			.insert(ward)
			.values({
				ward: ward_
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_ward: async ({ request, url }) => {
		const body = await request.formData();
		const { ward: ward_, id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(ward)
			.set({
				ward: ward_
			})
			.where(eq(ward.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_ward: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(ward)
			.where(eq(ward.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	create_room: async ({ request, url }) => {
		const body = await request.formData();
		const {
			room: room_,
			ward_id,
			department_id,
			product_id
		} = Object.fromEntries(body) as Record<string, string>;
		await db
			.insert(room)
			.values({
				room: room_,
				ward_id: +ward_id,
				product_id: +product_id,
				department_id: +department_id
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_room: async ({ request, url }) => {
		const body = await request.formData();
		const {
			room: room_,
			product_id,
			room_id,
			department_id
		} = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(room)
			.set({
				room: room_,
				product_id: +product_id,
				department_id: +department_id
			})
			.where(eq(room.id, +room_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_room: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(room)
			.where(eq(room.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},

	create_bed: async ({ request, url }) => {
		const body = await request.formData();
		const { room_id, ward_id, bed: bed_ } = Object.fromEntries(body) as Record<string, string>;
		await db
			.insert(bed)
			.values({
				room_id: +room_id,
				ward_id: +ward_id,
				bed: bed_
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_bed: async ({ request, url }) => {
		const body = await request.formData();
		const {
			room_id,
			ward_id,
			bed: bed_,
			bed_id
		} = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(bed)
			.set({
				room_id: +room_id,
				ward_id: +ward_id,
				bed: bed_
			})
			.where(eq(bed.id, +bed_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_bed: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(bed)
			.where(eq(bed.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
