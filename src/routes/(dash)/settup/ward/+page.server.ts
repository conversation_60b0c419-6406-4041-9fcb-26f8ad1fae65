import { db } from '$lib/server/db';
import { bed, product, category, progressNote, room, ward } from '$lib/server/schemas';
import { logError } from '$lib/server/utils/telegram';
import type { Actions, PageServerLoad } from './$types';
import { eq, isNull } from 'drizzle-orm';

export const load = (async () => {
	const get_category = await db.query.category.findFirst({
		where: eq(category.name, 'Room')
	});
	const get_products = await db.query.product.findMany({
		where: eq(product.category_id, get_category?.id || 0)
	});
	const wards = await db.query.ward.findMany({
		with: {
			room: {
				with: {
					department: true,
					product: true,
					bed: true
				}
			},
			bed: true
		}
	});
	const get_progress_note = await db.query.progressNote.findMany({
		where: isNull(progressNote.date_checkout),
		with: {
			patient: true,
			activeBed: {
				with: {
					bed: {
						with: {
							room: {
								with: {
									product: true
								}
							}
						}
					}
				}
			}
		}
	});
	return {
		wards,
		get_products,
		get_progress_note
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_ward: async ({ request, url }) => {
		const body = await request.formData();
		const { ward: ward_ } = Object.fromEntries(body) as Record<string, string>;
		await db
			.insert(ward)
			.values({
				ward: ward_
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_ward: async ({ request, url }) => {
		const body = await request.formData();
		const { ward: ward_, id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(ward)
			.set({
				ward: ward_
			})
			.where(eq(ward.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_ward: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(ward)
			.where(eq(ward.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	create_room: async ({ request, url }) => {
		const body = await request.formData();
		const { room: room_, ward_id, product_id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.insert(room)
			.values({
				room: room_,
				ward_id: +ward_id,
				product_id: +product_id
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},

	create_bed: async ({ request, url }) => {
		const body = await request.formData();
		const { room_id, ward_id, bed: bed_ } = Object.fromEntries(body) as Record<string, string>;
		await db
			.insert(bed)
			.values({
				room_id: +room_id,
				ward_id: +ward_id,
				bed: bed_
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
