import { db } from '$lib/server/db';
import { fileHandle } from '$lib/server/upload';
import { clinicinfo, uploads } from '$lib/server/schemas';
import type { Actions, PageServerLoad } from './$types';
import { eq } from 'drizzle-orm';
import { logError } from '$lib/server/utils/telegram';
export const load: PageServerLoad = async () => {
	const get_uploads = await db.query.uploads.findMany({
		where: eq(uploads.related_type, 'clinicinfo')
	});
	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	return {
		get_clinic_info,
		get_uploads
	};
};

export const actions: Actions = {
	create_clinic_info: async ({ request, url }) => {
		const body = await request.formData();
		const { title_khm, title_eng, address, contact, id, detail, logo0, logo1 } = Object.fromEntries(
			body
		) as Record<string, string>;
		const img0 = body.get('img0') as File;
		const img1 = body.get('img1') as File;
		const fineclinichinfo = await db.query.clinicinfo.findFirst();
		if (id) {
			await db
				.update(clinicinfo)
				.set({
					address: address,
					detail: detail,
					contact: contact,
					title_eng: title_eng,
					title_khm: title_khm
				})
				.where(eq(clinicinfo.id, fineclinichinfo!.id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
			if (img0.size)
				await fileHandle.update(img0, logo0, fineclinichinfo!.id, 'clinicinfo', 'logo0');
			if (img1.size)
				await fileHandle.update(img1, logo1, fineclinichinfo!.id, 'clinicinfo', 'logo1');
		}
		if (!id) {
			const create_clinic_info: { id: number }[] = await db
				.insert(clinicinfo)
				.values({
					address: address,
					detail: detail,
					contact: contact,
					title_eng: title_eng,
					title_khm: title_khm
				})
				.$returningId()
				.catch((e) => {
					logError({ url, body, err: e });
					return [];
				});

			if (img0.size) await fileHandle.insert(img0, create_clinic_info[0].id, 'clinicinfo', 'logo0');

			if (img1.size) await fileHandle.insert(img1, create_clinic_info[0].id, 'clinicinfo', 'logo1');
		}
	}
};
