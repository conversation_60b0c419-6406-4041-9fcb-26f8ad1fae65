<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_clinic_info, get_uploads } = $derived(data);
	let edit = $state(false);
	let loading = $state(false);
</script>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('clinic_info')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="#/" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tools"></i>
					{locale.T('settup')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/settup/clinic-info" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-circle-h"></i>
					{locale.T('clinic_info')}
				</a>
			</li>
		</ol>
	</div>
</div>

<Form
	method="post"
	action="?/create_clinic_info"
	bind:loading
	fnSuccess={() => (edit = false)}
	enctype="multipart/form-data"
	class="card"
>
	<input type="hidden" name="id" value={get_clinic_info?.id || ''} />
	<input
		type="hidden"
		name="logo0"
		value={get_uploads?.find((e) => e.mimeType === 'logo0')?.filename || ''}
	/>
	<input
		type="hidden"
		name="logo1"
		value={get_uploads?.find((e) => e.mimeType === 'logo1')?.filename || ''}
	/>

	<div class="card-header">
		{#if !edit}
			<button
				aria-label="submit"
				type="button"
				onclick={() => (edit = !edit)}
				class="float-right btn btn-outline-primary btn-lg"><i class="fa-solid fa-pen"></i></button
			>
		{/if}
		{#if edit}
			<button aria-label="submit" type="submit" class="float-right btn btn-primary btn-lg"
				><i class="fa-solid fa-floppy-disk"></i></button
			>
		{/if}
	</div>
	<div style="max-height: {store.inerHight};" class="card-body">
		<section class="invoice border-0">
			<div class="page-header row">
				<div class="col-auto text-center">
					<!-- svelte-ignore a11y_click_events_have_key_events -->
					<!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
					<img
						class:border={edit}
						id="imgp1"
						onclick={() => document.getElementById('img0')?.click()}
						height="170px"
						class="float-right"
						src={get_uploads?.find((e) => e.mimeType === 'logo0')?.filename}
						alt="no logo"
					/>
					{#if edit}
						<input accept="image/*" type="file" name="img0" id="img0" style="display:none;" />
					{/if}
				</div>
				<div class="col text-center">
					{#if edit}
						<input
							name="title_khm"
							style="font-size: 30px;"
							value={get_clinic_info?.title_khm ?? ''}
							class="form-control form-control-lg text-center kh_font_muol"
							type="text"
							required
							autocomplete="off"
						/>
					{/if}
					{#if !edit}
						<input
							disabled
							style="font-size: 30px;background-color: #FFFFFF;"
							value={get_clinic_info?.title_khm ?? ''}
							class="form-control form-control-lg text-center kh_font_muol form-control-plaintext shadow-none border-0"
							type="text"
							required
							autocomplete="off"
						/>
					{/if}

					{#if edit}
						<input
							name="title_eng"
							style="font-size: 30px;"
							value={get_clinic_info?.title_eng ?? ''}
							class="form-control form-control-lg text-center en_font_times_new_roman"
							type="text"
							required
							autocomplete="off"
						/>
					{/if}
					{#if !edit}
						<input
							disabled
							style="font-size: 30px;background-color: #FFFFFF;"
							value={get_clinic_info?.title_eng ?? ''}
							class="form-control form-control-lg text-center en_font_times_new_roman form-control-plaintext shadow-none border-0"
							type="text"
							required
							autocomplete="off"
						/>
					{/if}
					{#if edit}
						<input
							value={get_clinic_info?.detail ?? ''}
							class="form-control form-control-lg text-center kh_font_battambang"
							type="text"
							required
							autocomplete="off"
							name="detail"
						/>
					{/if}
					{#if !edit}
						<input
							disabled
							style="background-color: #FFFFFF;"
							value={get_clinic_info?.detail ?? ''}
							class="form-control form-control-lg text-center kh_font_battambang form-control-plaintext shadow-none border-0"
							type="text"
							required
							autocomplete="off"
						/>
					{/if}
					{#if edit}
						<input
							name="contact"
							value={get_clinic_info?.contact ?? ''}
							class="form-control form-control-lg text-center kh_font_battambang"
							type="text"
							required
							autocomplete="off"
						/>
					{/if}
					{#if !edit}
						<input
							disabled
							style="background-color: #FFFFFF;"
							value={get_clinic_info?.contact ?? ''}
							class="form-control form-control-lg text-center kh_font_battambang form-control-plaintext shadow-none border-0"
							type="text"
							required
							autocomplete="off"
						/>
					{/if}
				</div>
				<div class="col-auto text-center">
					<!-- svelte-ignore a11y_click_events_have_key_events -->
					<!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
					<img
						onclick={() => document.getElementById('img1')?.click()}
						height="170px"
						class:border={edit}
						class="float-right"
						src={get_uploads?.find((e) => e.mimeType === 'logo1')?.filename}
						alt="no logo"
					/>
					{#if edit}
						<input accept="image/*" type="file" name="img1" id="img1" style="display:none;" />
					{/if}
					<!-- <label class="btn btn-default" for="img">upload image</label> -->
				</div>
			</div>
		</section>
	</div>
	<div class="card-body">
		<div class="row">
			<div class="col">
				{#if edit}
					<input
						value={get_clinic_info?.address ?? ''}
						class="form-control text-center form-control-lg kh_font_battambang"
						type="text"
						required
						autocomplete="off"
						name="address"
					/>
				{/if}
				{#if !edit}
					<input
						disabled
						style="background-color: #FFFFFF;"
						value={get_clinic_info?.address ?? ''}
						class="form-control text-center form-control-lg kh_font_battambang form-control-plaintext shadow-none border-0"
						type="text"
						required
						autocomplete="off"
					/>
				{/if}
			</div>
		</div>
	</div>
</Form>
