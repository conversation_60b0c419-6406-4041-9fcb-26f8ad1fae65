<script lang="ts">
	import type { ActionData, PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import { store } from '$lib/store/store.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import Export from '$lib/coms/Export.svelte';
	interface Props {
		form: ActionData;
		data: PageServerData;
	}

	let { form, data }: Props = $props();
	let lab_group_id: number = $state(0);
	let loading = $state(false);
	let { get_lab_groups } = $derived(data);
	let find_lab_group = $derived(get_lab_groups.find((e) => e.id === lab_group_id));
</script>

<DeleteModal action="?/delete_lab_group" id={find_lab_group?.id} />
<!-- @_Add_Patient -->
<div class="modal fade" id="modal-add-medicine" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<Form
			action={find_lab_group?.id ? '?/update_lab_group' : '?/create_lab_group'}
			method="post"
			class="modal-content"
			bind:loading
			fnSuccess={() => {
				lab_group_id = 0;
				document.getElementById('close')?.click();
			}}
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('lab_group')}</h4>
				<button
					id="close"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card-body pt-0">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<input value={find_lab_group?.id} type="hidden" name="lab_group_id" />
								<label for="lab_group">{locale.T('name')}</label>
								<input
									value={find_lab_group?.laboratory_group ?? ''}
									name="lab_group"
									type="text"
									class="form-control"
									id="lab_group"
								/>
								{#if form?.lab_group}
									<p class="text-danger">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('lab_group')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="#/" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tools"></i>
					{locale.T('settup')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/settup/lab-group" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-vials nav-icon"></i>
					{locale.T('lab_group')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<HeaderQuery class="row g-1">
			<div class="col-auto">
				<Export
					title="Lab group list"
					data={get_lab_groups.map((e) => {
						return {
							id: e.id,
							name: e.laboratory_group
						};
					})}
				/>
			</div>

			<div class="col-auto ms-auto">
				<button
					onclick={() => (lab_group_id = 0)}
					type="button"
					class="btn btn-success"
					data-bs-toggle="modal"
					data-bs-target="#modal-add-medicine"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('add_lab_group')}
				</button>
			</div>
		</HeaderQuery>
	</div>
	<div style="max-height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-bordered table-light text-nowrap table-hover">
			<thead class="table-light table-active sticky-top">
				<tr>
					<th style="width: 5%;" class="text-center">{locale.T('n')}</th>
					<th>{locale.T('name')}</th>
					<th></th>
				</tr>
			</thead>
			<tbody>
				{#each get_lab_groups as item, index}
					<tr>
						<td class="text-center">{index + 1}</td>
						<td>{item.laboratory_group}</td>

						<td>
							<div>
								<button
									aria-label="modaladdmedicine"
									onclick={() => {
										lab_group_id = 0;
										lab_group_id = item.id;
									}}
									type="button"
									class="btn btn-primary btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#modal-add-medicine"
									><i class="fa-solid fa-file-pen"></i>
								</button>
								<button
									aria-label="deletemodal"
									onclick={() => {
										lab_group_id = 0;
										lab_group_id = item.id;
									}}
									type="button"
									class="btn btn-danger btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-trash-can"></i>
								</button>
							</div>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
</div>
