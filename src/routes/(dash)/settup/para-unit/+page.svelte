<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import Athtml from '$lib/coms/Athtml.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import Export from '$lib/coms/Export.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { ActionData, PageServerData } from './$types';
	interface Props {
		form: ActionData;
		data: PageServerData;
	}

	let { form, data }: Props = $props();
	let para_unit_id: number | undefined = $state();
	let { get_para_units } = $derived(data);
	let find_para_unit = $derived(get_para_units.find((e) => e.id === para_unit_id));
	let loading = $state(false);
</script>

<DeleteModal id={find_para_unit?.id} action="?/delete_para_unit" />

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('para_unit')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="#/" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tools"></i>
					{locale.T('settup')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/settup/para-unit" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-layer-group nav-icon"></i>
					{locale.T('unit')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<!-- <h3 class="card-title">Fixed Header Table</h3> -->
		<div class="row g-1">
			<div class="col-auto">
				<Export
					title="Unit list"
					data={get_para_units.map((e) => {
						return {
							id: e.id,
							unit: e.unit
						};
					})}
				/>
			</div>
			<div class="col-auto ms-auto">
				<button
					onclick={() => {
						para_unit_id = 0;
					}}
					type="button"
					class="btn btn-success"
					data-bs-toggle="modal"
					data-bs-target="#create_unit"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('add')}
				</button>
			</div>
		</div>
	</div>
	<div style="max-height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-bordered table-light">
			<thead class="table-active table-light sticky-top">
				<tr>
					<th class="text-center" style="width: 5%;"> {locale.T('n')}</th>
					<th> {locale.T('unit')}</th>
					<th> {locale.T('action')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_para_units as item, index}
					<tr>
						<td class="text-center">{index + 1}</td>
						<td> <Athtml html={item.unit ?? ''} /> </td>
						<td>
							<div>
								<button
									aria-label="createunit"
									onclick={() => {
										para_unit_id = item.id;
									}}
									data-bs-toggle="modal"
									data-bs-target="#create_unit"
									type="button"
									class="btn btn-primary btn-sm"
									><i class="fa-solid fa-file-pen"></i>
								</button>
								<button
									aria-label="deletemodal"
									onclick={() => {
										para_unit_id = item.id;
									}}
									type="button"
									class="btn btn-danger btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-trash-can"></i>
								</button>
							</div>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
</div>
<!-- @_List_Parameter -->
<div class="modal fade" id="create_unit" data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-scrollabl modal-xl">
		<Form
			action={find_para_unit?.id ? '?/update_para_unit' : '?/create_para_unit'}
			method="post"
			bind:loading
			fnSuccess={() => {
				para_unit_id = undefined;
				document.getElementById('close_create_unit')?.click();
			}}
			class="modal-content"
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('unit')}</h4>
				<button
					id="close_create_unit"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="card-body pt-0">
				<div class="modal-body">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<input value={find_para_unit?.id} type="hidden" name="para_unit_id" />
								<label for="unit_">{locale.T('unit')}</label>
								<input
									value={find_para_unit?.unit}
									name="unit_"
									type="text"
									class="form-control"
									id="unit_"
								/>
								{#if form?.unit_}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>
