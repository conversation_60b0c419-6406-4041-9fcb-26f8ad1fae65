<script lang="ts">
	import { page } from '$app/state';
</script>

<div class="error-container">
	<div class="lottie-animation"></div>
	<div class="error-content">
		<h1>Error!</h1>
		<h1>{page.status} {page?.error?.message}</h1>
		<a href="/" class="btn btn-primary">Go Back!</a>
	</div>
</div>

<style>
	.error-container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		min-height: 100vh;
		background-color: #f8f9fa;
	}

	.error-content {
		text-align: center;
	}

	.error-content h1 {
		font-size: 6rem;
		font-weight: bold;
		margin-bottom: 1rem;
	}

	.lottie-animation {
		max-width: 400px;
		margin-bottom: 2rem;
	}
</style>
