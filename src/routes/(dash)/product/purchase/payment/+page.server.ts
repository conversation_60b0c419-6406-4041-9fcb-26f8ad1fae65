import { db } from '$lib/server/db';
import { fail, redirect } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { inventory, exspend, payment, uploads } from '$lib/server/schemas';
import { YYYYMMDD_Format, pagination } from '$lib/server/utils';
import { eq, inArray, and } from 'drizzle-orm';
import { logError } from '$lib/server/utils/telegram';
import { fileHandle } from '$lib/server/upload';
// import { now_datetime } from '$lib/server/utils';
async function totalExspend(id: number) {
	const get_amount = await db.query.exspend.findFirst({
		where: eq(exspend.id, id),
		with: { inventory: true, payment: true }
	});
	const total_payment = get_amount?.payment.reduce((s, e) => s + Number(e.value), 0);
	const amount = get_amount?.inventory?.reduce((s, e) => s + Number(e.total_expense), 0);
	await db
		.update(exspend)
		.set({ amount: amount, paid: total_payment, credit: Number(amount) - Number(total_payment) })
		.where(eq(exspend.id, id));
	redirect(303, `?exspend_id=${id}`);
}
export const load = (async ({ url }) => {
	const exspend_id = url.searchParams.get('exspend_id') || '';
	const get_exspend = await db.query.exspend.findFirst({
		where: eq(exspend.id, +exspend_id),
		with: {
			payment: {
				with: {
					paymentType: true,
					staff: true
				}
			}
		}
	});
	const get_payments = await db.query.payment.findMany({
		where: eq(payment.exspend_id, +exspend_id)
	});

	const get_currency = await db.query.currency.findFirst({});
	const get_inventories = await db.query.inventory.findMany({
		where: eq(inventory.exspend_id, +exspend_id),
		with: {
			costUnit: true,
			product: {
				with: {
					subUnit: {
						with: {
							unit: true
						}
					},
					unit: true
				}
			}
		},
		...pagination(url)
	});
	const items = await db.$count(inventory, eq(inventory.exspend_id, +exspend_id));
	const get_payment_types = await db.query.paymentType.findMany({});
	const get_upload_payment = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_type, 'payment'),
			inArray(uploads.related_id, get_exspend?.payment.map((item) => item.id) || [])
		)
	});
	return {
		get_currency,
		get_exspend: {
			...get_exspend,
			payment: get_exspend?.payment.map((item) => {
				return {
					...item,
					uploads: get_upload_payment.find((ee) => ee.related_id === item.id)
				};
			})
		},
		get_payments,
		get_inventories,
		items,
		get_payment_types
	};
}) satisfies PageServerLoad;
export const actions: Actions = {
	create_payment: async ({ request, locals, url }) => {
		const body = await request.formData();
		const { user } = locals;
		const { total_pay, payment_id, exspend_id, note, payment_type_id, total_paid, amount } =
			Object.fromEntries(body) as Record<string, string>;
		await fileHandle.auto(body);
		if (!exspend_id) return fail(400, { paymentErr: true });
		if (+total_pay <= 0) return fail(400, { paymentErr: true });
		if (payment_id) {
			await db
				.update(payment)
				.set({
					value: +total_pay,
					exspend_id: +exspend_id,
					payment_type_id: +payment_type_id,
					note: note
				})
				.where(eq(payment.id, +payment_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		if (!payment_id) {
			if (+total_pay + +total_paid > +amount) return fail(400, { paymentErr: true });
			await db
				.insert(payment)
				.values({
					value: +total_pay,
					datetime: YYYYMMDD_Format.datetime(new Date()),
					exspend_id: +exspend_id,
					payment_type_id: +payment_type_id,
					staff_id: user?.staff_id,
					note: note
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		await totalExspend(+exspend_id);
	},
	delete_payment: async ({ request, url }) => {
		const body = await request.formData();
		const { id, exspend_id } = Object.fromEntries(body) as Record<string, string>;
		if (id) {
			await db
				.delete(payment)
				.where(eq(payment.id, +id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
			await totalExspend(+exspend_id);
		}
	}
};
