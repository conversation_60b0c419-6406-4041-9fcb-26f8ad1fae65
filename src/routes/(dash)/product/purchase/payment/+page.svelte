<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import CurrencyInput from '$lib/coms-form/CurrencyInput.svelte';
	import { page } from '$app/state';
	import { DDMMYYYY_Format } from '$lib/helper';
	import Form from '$lib/coms-form/Form.svelte';
	import { store } from '$lib/store/store.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import CropImage from '$lib/coms-form/CropImage.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import Confirm from '$lib/coms/Confirm.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_currency, get_exspend, get_inventories, items, get_payment_types } = $derived(data);
	let exspend_id: string = $derived(page.url.searchParams.get('exspend_id') || '');
	let inventory_id: string = $derived(page.url.searchParams.get('inventory_id') || '');
	let inventory = $derived(get_inventories?.find((e) => e.id === Number(inventory_id)));
	let loading = $state(false);
	let n = $state(1);
	// let qty_bought = $state(0);
	// let cost = $state(0);
	let inventory_id_ = $state('');
	$effect(() => {
		if (inventory) {
			// qty_bought = inventory?.qty_bought ?? 0;
			// cost = inventory?.cost ?? 0;
		}
		if (!inventory) {
			// qty_bought = 0;
			// cost = 0;
		}
	});
	let percent_inerhight = $derived(Number(store.inerHight.replace('px', '')) / 1.8);
	let payment_id = $state<number | undefined>(undefined);
	let payment_id_delete = $state<number | undefined>(undefined);
	let balance = $derived(get_exspend?.credit);
	let pay_1 = $state(0);
	let get_payment = $derived(get_exspend?.payment?.find((e) => e.id === payment_id));
	let total_paid = $derived(
		get_exspend?.payment?.reduce((e, s) => Number(e || 0) + Number(s.value), 0)
	);
	let pay: number = $derived.by(() => {
		if (get_payment) {
			return Number(get_payment?.value);
		} else if (balance) {
			return Number(balance);
		} else {
			return 0;
		}
	});
	let total_pay = $derived(
		pay + (pay_1 * Number(get_currency?.currency_rate)) / Number(get_currency?.exchang_rate)
	);
</script>

<DeleteModal action="?/remove_product" id={Number(inventory_id_)} />
<div class="row">
	<div class="col-sm-6">
		<a href="/product/purchase" class="btn btn-link p-0"
			><i class="fa-solid fa-rotate-left"></i>
			{locale.T('back')}
		</a>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('products')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product/purchase" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('purchase')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product/purchase/create" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('add')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light mt-2 border-2 border-primary">
	<div class="row card-body">
		<div class="col-5">
			<fieldset disabled={payment_id ? false : Number(balance) <= 0 ? true : false}>
				<Form
					fnSuccess={() => {
						payment_id = undefined;
					}}
					enctype="multipart/form-data"
					action="?/create_payment"
					method="post"
					bind:loading
					reset={true}
				>
					<div class={payment_id ? 'alert alert-warning' : 'alert alert-primary'}>
						<div class="row">
							<div class="col">
								<input type="hidden" name="exspend_id" value={exspend_id} />
								<input type="hidden" name="payment_id" value={payment_id} />
								<input type="hidden" name="total_pay" value={total_pay} />
								<input type="hidden" name="total_paid" value={total_paid} />
								<input type="hidden" name="amount" value={get_exspend.amount} />
								<label for="amount">{locale.T('amount')}</label>
								<CurrencyInput
									class="input-group pb-2"
									bind:amount={pay}
									symbol={get_currency?.currency}
								/>
								<CurrencyInput bind:amount={pay_1} symbol={get_currency?.exchang_to} />
							</div>
							<div class="col">
								<label for="payment_type_id">{locale.T('type')} </label>
								<select
									value={get_payment?.payment_type_id || get_payment_types[0]?.id}
									class="form-control"
									name="payment_type_id"
									id="payment_type_id"
								>
									{#each get_payment_types as item}
										<option value={item.id}>{item.by}</option>
									{/each}
								</select>
							</div>
						</div>
					</div>
					<label for="file">{locale.T('documents')}</label>
					<!-- <CropImage
						name="file"
						related_id={get_payment?.id}
						related_type_="payment"
						default_image={get_payment?.uploads?.filename}
						aspect_ratio
					/> -->
					<CropImage
						related_id={get_payment?.id}
						related_type_="payment"
						default_image={get_payment?.uploads?.filename ?? ''}
						name="file"
					/>
					{get_payment?.uploads?.mimeType ?? ''}
					<label class="pt-2" for="note">{locale.T('note')}</label>
					<textarea value={get_payment?.note} class="form-control" rows="4" name="note" id="note"
					></textarea>
					<div class=" text-end pt-2">
						<SubmitButton {loading} />
					</div>
				</Form>
			</fieldset>
		</div>
		<div class="col-7">
			<span class="btn btn-sm btn-info rounded-0">{locale.T('payment_history')}</span>
			<Currency
				class="text-bg-primary mx-2 px-2"
				amount={get_exspend?.payment?.reduce((s, e) => s + Number(e.value), 0) || 0}
				symbol={get_currency?.currency}
			/>
			{#if Number(balance) > 0}
				{locale.T('balance')}
				<Currency
					class="text-bg-danger mx-2 px-2"
					amount={balance}
					symbol={get_currency?.currency}
				/>
			{/if}
			{#if get_exspend?.payment?.length}
				{#each get_exspend?.payment || [] as item, index}
					<div class="row g-0">
						<input type="hidden" name="id" value={item.id} />

						<div class="col">
							<div class="alert alert-warning rounded-0 py-1 my-1">
								<div class="row g-1">
									<div class="col-auto">{locale.T('n')} {index + 1}</div>
									<div class="col-auto">
										{locale.T('amount')}
										<Currency amount={item.value} symbol={get_currency?.currency} />
									</div>
									<div class="col-auto">
										{locale.T('date')}
										<DDMMYYYYFormat date={item.datetime} />
									</div>
									<div class="col-auto">
										{item.paymentType?.by}
									</div>
								</div>
							</div>
						</div>
						<div class="col-auto">
							<button type="button" class="alert alert-primary rounded-0 py-1 my-1"
								>{item.staff?.name_khmer}</button
							>
						</div>
						<div class="col-auto">
							<button
								type="button"
								onclick={() => (payment_id = item.id)}
								class="alert alert-success rounded-0 py-1 my-1">{locale.T('edit')}</button
							>
						</div>
						<div class="col-auto">
							<button
								data-bs-toggle="modal"
								data-bs-target="#delete_payment"
								type="button"
								onclick={() => (payment_id_delete = item.id)}
								class="alert alert-danger rounded-0 py-1 my-1">{locale.T('delete_')}</button
							>
						</div>
					</div>
				{/each}
			{/if}
		</div>
	</div>
</div>

<Confirm
	body={[
		{ name: 'id', value: payment_id_delete?.toString() ?? '' },
		{ name: 'exspend_id', value: exspend_id?.toString() ?? '' }
	]}
	method="POST"
	modal_id="delete_payment"
	action="?/delete_payment"
/>

<div class="card bg-light mt-2">
	<div style="height: {percent_inerhight}px;" class="card-body table-responsive p-0 m-0">
		<table class="table table-bordered table-hover text-nowrap table-sm">
			<thead class="sticky-top top-0 bg-light table-active">
				<tr class="text-center">
					<th style="width: 3%;" class="text-center">{locale.T('n')}</th>
					<th style="width: 20%;">{locale.T('products')}</th>
					<th style="width: 10%;">{locale.T('price')}</th>
					<th style="width: 10%;">{locale.T('purchase_qty')}</th>
					<th style="width: 10%;">{locale.T('total')}</th>
					<th style="width: 10%;">{locale.T('expires_date')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_inventories || [] as item, index}
					<tr class={item.id === Number(inventory_id) ? 'table-success' : ''}>
						<td class="text-center">{n + index}</td>
						<td class="text-start">
							<div class="row g-2">
								<div class="col-auto">
									{item?.product?.products}
								</div>
							</div>
						</td>
						<td>
							<Currency amount={item?.cost} symbol={get_currency?.currency} />
						</td>
						<td>
							{item.qty_bought}
							{item?.costUnit?.unit}
						</td>
						<td>
							<Currency amount={item?.total_expense} symbol={get_currency?.currency} />
						</td>
						<td>
							{#if item?.datetime_expire}
								{DDMMYYYY_Format(item?.datetime_expire, 'date')}
							{:else}
								<span class="text-danger">{locale.T('none')}</span>
							{/if}
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations bind:n {items} />
	</div>
</div>
