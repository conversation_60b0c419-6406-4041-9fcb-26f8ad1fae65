<script lang="ts">
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import { goto } from '$app/navigation';
	import Paginations from '$lib/coms/Paginations.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import HandleQ from '$lib/coms-form/HandleQ.svelte';
	import Export from '$lib/coms/Export.svelte';

	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_exspends, get_currency, items } = $derived(data);

	let n = $state(1);
	let exspend_id: number | null = $state(null);
	let total_credit = $derived(get_exspends.reduce((s, e) => s + Number(e.credit), 0));
	let total_amount = $derived(get_exspends.reduce((s, e) => s + Number(e.amount), 0));
	let total_paid = $derived(get_exspends.reduce((s, e) => s + Number(e.paid), 0));
</script>

<DeleteModal action="?/delete_exspend" id={Number(exspend_id)} />

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('purchase_list')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('products')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product/purchase" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-basket-shopping"></i>
					{locale.T('purchase')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<HeaderQuery class="row g-1">
			<div class="col-auto">
				<Export
					title="Purchase list"
					data={get_exspends.map((e) => {
						return {
							id: e.id,
							date: e.datetime_invoice,
							supplier: e.supplier?.name,
							amount: e.amount,
							paid: e.paid,
							credit: e.credit,
							invoice_no: e.invoice_no
						};
					})}
				/>
			</div>
			<div class="col-sm-3">
				<div class="input-group">
					<span class="input-group-text">{locale.T('start')}</span>
					<input type="date" name="start" class="form-control" />
				</div>
			</div>
			<div class="col-sm-3">
				<div class="input-group">
					<span class="input-group-text">{locale.T('end')}</span>
					<input type="date" name="end" class="form-control" />
				</div>
			</div>
			<div class="col-sm-3">
				<HandleQ />
			</div>

			<div class="col-auto ms-auto">
				<a href="/product/purchase/create" type="button" class="btn btn-success"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('purchase')}
				</a>
			</div>
		</HeaderQuery>
	</div>
	<div style="height: {store.inerHight};" class="card-body table-responsive p-0 m-0">
		<table class="table table-hover table-bordered table-light">
			<thead class="sticky-top top-0 bg-light table-active">
				<tr class="text-center">
					<th class="text-center">{locale.T('n')}</th>
					<th>{locale.T('date')}</th>
					<th>{locale.T('supplier')}</th>
					<th>{locale.T('amount')}</th>
					<th>{locale.T('paid')}</th>
					<th>{locale.T('credit')}</th>
					<th>{locale.T('invoice_no')}</th>
					<th>{locale.T('action')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_exspends as item, index}
					<tr class="text-center">
						<td>
							{index + 1}
						</td>
						<td>
							<DDMMYYYYFormat date={item?.datetime_invoice} />
						</td>
						<td>
							{item.supplier?.name ?? ''}
							{item.supplier?.address ?? ''}
						</td>

						<td>
							<Currency amount={item?.amount} symbol={get_currency?.currency} />
						</td>
						<td>
							<Currency amount={item?.paid} symbol={get_currency?.currency} />
						</td>

						<td>
							{#if Number(item?.credit) >= 0}
								{locale.T('none')}
							{:else}
								<Currency amount={item?.credit} symbol={get_currency?.currency} />
							{/if}
						</td>
						<td>
							{item?.invoice_no}
						</td>

						<td>
							<div class=" m-0 p-0 text-start">
								<button
									aria-label="sowingimage"
									data-bs-target="#view_refrence"
									class="btn btn-primary btn-sm"
								>
									<i class="fa-solid fa-receipt"></i>
								</button>

								<a
									href="/product/purchase/create?exspend_id={item.id}"
									aria-label="createproduct"
									class="btn btn-primary btn-sm"
									><i class="fa-solid fa-file-pen"></i>
								</a>

								<a
									href="/product/purchase/payment?exspend_id={item.id}"
									class="btn btn-success btn-sm"
									>ទូទាត់
								</a>

								<button
									onclick={() => {
										exspend_id = item.id;
										goto(`?exspend_id=${item.id}`);
									}}
									aria-label="deletemodal"
									type="button"
									class="btn btn-danger btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-trash-can"></i>
								</button>
							</div>
						</td>
					</tr>
				{/each}
				<tr class="text-center table-success">
					<td class="text-end" colspan="3">{locale.T('total')}</td>
					<td>
						<Currency amount={total_amount} symbol={get_currency?.currency} />
					</td>
					<td>
						<Currency amount={total_paid} symbol={get_currency?.currency} />
					</td>
					<td>
						{#if Number(total_credit) >= 0}
							{locale.T('none')}
						{:else}
							<Currency amount={total_credit} symbol={get_currency?.currency} />
						{/if}
					</td>
					<td colspan="3"></td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations {items} bind:n />
	</div>
</div>
