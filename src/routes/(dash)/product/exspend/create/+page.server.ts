import { db } from '$lib/server/db';
import type { PageServerLoad, Actions } from './$types';
import { exspend, exspendType, uploads } from '$lib/server/schemas';
import { and, eq } from 'drizzle-orm';
import { logError } from '$lib/server/utils/telegram';
import { fail, redirect } from '@sveltejs/kit';
import { fileHandle } from '$lib/server/upload';
export const load = (async ({ url }) => {
	const exspend_id = url.searchParams.get('exspend_id') ?? '';
	const get_exspend = await db.query.exspend.findFirst({
		where: eq(exspend.id, +exspend_id)
	});
	const get_currency = await db.query.currency.findFirst({});
	const get_exspend_types = await db.query.exspendType.findMany();
	const get_staffs = await db.query.staff.findMany();
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_id, +exspend_id), eq(uploads.related_type, 'exspend'))
	});
	return {
		get_currency,
		get_exspend: { ...get_exspend, uploads: get_upload },
		get_staffs,
		get_exspend_types
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_exspend: async ({ request, url }) => {
		const body = await request.formData();
		const {
			amount,
			recorder_id,
			invoice_no,
			datetime_invoice,
			exspend_type_id,
			description,
			exspend_id
		} = Object.fromEntries(body) as Record<string, string>;
		const image = body.get('image') as File;
		const validErr = {
			amount: false,
			datetime_invoice: false,
			exspend_type_id: false
		};

		if (!amount) validErr.amount = true;
		if (!exspend_type_id) validErr.exspend_type_id = true;
		if (!datetime_invoice) validErr.datetime_invoice = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		if (exspend_id) {
			await db
				.update(exspend)
				.set({
					amount: +amount,
					paid: +amount,
					invoice_no: invoice_no,
					recorder_id: recorder_id ? +recorder_id : null,
					datetime_invoice: datetime_invoice,
					description: description,
					exspend_type_id: +exspend_type_id ? +exspend_type_id : null
				})
				.where(eq(exspend.id, +exspend_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});

			await fileHandle.auto(body);
		}
		if (!exspend_id) {
			const id: { id: number }[] = await db
				.insert(exspend)
				.values({
					amount: +amount,
					paid: +amount,
					invoice_no: invoice_no,
					recorder_id: recorder_id ? +recorder_id : null,
					datetime_invoice: datetime_invoice,
					description: description,
					exspend_type_id: +exspend_type_id ? +exspend_type_id : null
				})
				.$returningId()
				.catch((e) => {
					logError({ url, body, err: e });
					return [];
				});
			await fileHandle.insert(image, id[0].id, 'exspend');
		}
		redirect(303, '/product/exspend');
	},
	delete_exspend: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (isNaN(+id)) return fail(400, { id: true });
		await db
			.delete(exspend)
			.where(eq(exspend.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	create_expsend_type: async ({ request }) => {
		const body = await request.formData();
		const { id, word } = Object.fromEntries(body) as Record<string, string>;
		if (id) {
			await db
				.update(exspendType)
				.set({
					type: word
				})
				.where(eq(exspendType.id, +id));
		}
		if (!id) {
			await db.insert(exspendType).values({
				type: word
			});
		}
	},
	delete_expsend_type: async ({ request }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (id) {
			await db.delete(exspendType).where(eq(exspendType.id, +id));
		}
	}
};
