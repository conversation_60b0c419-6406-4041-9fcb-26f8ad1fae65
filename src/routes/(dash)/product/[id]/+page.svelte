<script lang="ts">
	import type { PageServerData } from './$types';
	import { locale } from '$lib/translations/locales.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import { store } from '$lib/store/store.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import { DDMMYYYY_Format } from '$lib/helper';
	import Paginations from '$lib/coms/Paginations.svelte';
	import GetBack from '$lib/coms/GetBack.svelte';
	import SetBack from '$lib/coms/SetBack.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_product, get_currency, items, get_inventories } = $derived(data);
	let inventory_id: number | null = $state(null);
	let n = $state(1);
	let percent_inerhight = $derived(Number(store.inerHight.replace('px', '')) / 1.8);
</script>

<DeleteModal action="?/delete_inventory" id={Number(inventory_id)} />

<div class="row">
	<div class="col-sm-6">
		<GetBack href="/product" />
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('products')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-gear"></i>
					{get_product?.products}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="row">
	<div class="col-12">
		<div class="card">
			<div class="card-body bg-light">
				<div class="alert alert-primary">
					<div class="row g-2">
						<div class="col-auto">
							<img
								src={get_product?.uploads?.filename
									? `${get_product?.uploads?.filename}`
									: '/no-image.webp'}
								alt=""
								style="min-height:400px;max-width:400px"
								class="img-thumbnail"
							/>
						</div>
						<div class="col">
							<div style="height: 400px;" class="card">
								<div class="card-header">
									<span>{locale.T('description')}</span>
								</div>
								<div class="card-body">
									<div class="row g-1 pb-2">
										<div class="col-4">
											<span>{locale.T('product_name')}</span>
										</div>
										<div class="col-8">
											<span class="form-control text-start">{get_product?.products}</span>
										</div>
									</div>
									<div class="row g-1 pb-2">
										<div class="col-4">
											<span>{locale.T('generic_name')}</span>
										</div>
										<div class="col-8">
											<span class="form-control text-start"
												>{get_product?.generic_name || locale.T('none')}</span
											>
										</div>
									</div>
									<div class="row g-1 pb-2">
										<div class="col-4">
											<span>{locale.T('barcode')}</span>
										</div>
										<div class="col-8">
											<span class="form-control text-start"
												>{get_product?.barcode || locale.T('none')}</span
											>
										</div>
									</div>
									<div class="row g-1 pb-2">
										<div class="col-4">
											<span>{locale.T('category')}</span>
										</div>
										<div class="col-8">
											<span class="form-control text-start"
												>{get_product?.category?.name ?? ''}</span
											>
										</div>
									</div>
									<div class="row g-1 pb-2">
										<div class="col-4">
											<span>{locale.T('product_group')}</span>
										</div>
										<div class="col-8">
											<span class="form-control text-start">{get_product?.group?.name ?? ''}</span>
										</div>
									</div>
									<div class="row g-1 pb-2">
										<div class="col-4">
											<span>{locale.T('unit')}</span>
										</div>
										<div class="col-8">
											<span class="form-control text-start">
												{get_product?.unit?.unit || locale.T('none')}
												{#each get_product?.subUnit || [] as item}
													/ {item?.unit?.unit}
												{/each}
											</span>
										</div>
									</div>
									<div class="row g-1">
										<div class="col-4">
											<span>{locale.T('price')}</span>
										</div>
										<div class="col-8">
											<span class="form-control text-start">
												<Currency amount={get_product?.price} symbol={get_currency?.currency} /> / {get_product
													?.unit?.unit ?? ''}
											</span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- <div class="vr mx-2"></div> -->
				</div>
				<div class="card bg-light mt-2">
					<div style="height: {percent_inerhight}px;" class="card-body table-responsive p-0 m-0">
						<table class="table table-bordered table-hover text-nowrap table-sm">
							<thead class="sticky-top top-0 bg-light table-active">
								<tr class="text-center">
									<th style="width: 3%;" class="text-center">{locale.T('n')}</th>
									<th style="width: 20%;">{locale.T('products')}</th>
									<th style="width: 10%;">{locale.T('price')}</th>
									<th style="width: 10%;">{locale.T('purchase_qty')}</th>
									<th style="width: 10%;">{locale.T('total')}</th>
									<th style="width: 10%;">{locale.T('date_bought')}</th>
									<th style="width: 10%;">{locale.T('expires_date')}</th>
									<th style="width: 10%;">{locale.T('action')}</th>
								</tr>
							</thead>
							<tbody>
								{#each get_inventories || [] as item, index}
									<tr
										class={item.id === Number(inventory_id)
											? 'table-success text-center'
											: 'text-center'}
									>
										<td class="text-center">{n + index}</td>
										<td class="text-start">
											<div class="row g-2">
												<div class="col-auto">
													{item?.product?.products}
												</div>
											</div>
										</td>
										<td>
											<Currency amount={item?.cost} symbol={get_currency?.currency} />
										</td>
										<td>
											{item.qty_bought}
											{item?.costUnit?.unit}
										</td>
										<td>
											<Currency amount={item?.total_expense} symbol={get_currency?.currency} />
										</td>
										<td>
											{#if item?.datetime_expire}
												{DDMMYYYY_Format(item?.datetime_buy, 'date')}
											{:else}
												<span class="text-danger">{locale.T('none')}</span>
											{/if}
										</td>
										<td>
											{#if item?.datetime_expire}
												{DDMMYYYY_Format(item?.datetime_expire, 'date')}
											{:else}
												<span class="text-danger">{locale.T('none')}</span>
											{/if}
										</td>
										<td class="text-center">
											<div class=" m-0 p-0">
												<SetBack href="/product/purchase/create?exspend_id={item?.exspend_id}">
													{locale.T('invoice')}
												</SetBack>
											</div>
										</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</div>
					<div class="card-footer">
						<Paginations bind:n {items} />
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
