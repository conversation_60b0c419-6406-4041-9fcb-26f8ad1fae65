import { db } from '$lib/server/db';
import { fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { supplier } from '$lib/server/schemas';
import { eq, like, desc, or } from 'drizzle-orm';
import { logError } from '$lib/server/utils/telegram';
import { pagination } from '$lib/server/utils';

export const load = (async ({ url }) => {
	const get_suppliers = await db.query.supplier.findMany({
		where: or(
			like(supplier.name, `%${url.searchParams.get('q') || ''}%`),
			like(supplier.company_name, `%${url.searchParams.get('q') || ''}%`),
			like(supplier.contact, `%${url.searchParams.get('q') || ''}%`)
		),
		orderBy: desc(supplier.id),
		...pagination(url)
	});
	const count = await db.$count(
		supplier,
		or(
			like(supplier.name, `%${url.searchParams.get('q') || ''}%`),
			like(supplier.company_name, `%${url.searchParams.get('q') || ''}%`),
			like(supplier.contact, `%${url.searchParams.get('q') || ''}%`)
		)
	);
	return { get_suppliers, items: count };
}) satisfies PageServerLoad;
export const actions: Actions = {
	create_supplier: async ({ request, url }) => {
		const body = await request.formData();
		const { name, company, contact, address, supplier_id } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		const validErr = {
			name: false,
			company: false,
			contact: false,
			address: false
		};
		if (!name.trim()) validErr.name = true;
		if (!company.trim()) validErr.company = true;
		if (!contact.trim()) validErr.contact = true;
		if (!address.trim()) validErr.address = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		if (supplier_id) {
			await db
				.update(supplier)
				.set({
					address: address,
					company_name: company,
					contact: contact,
					name: name
				})
				.where(eq(supplier.id, +supplier_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		if (!supplier_id) {
			await db
				.insert(supplier)
				.values({
					address: address,
					company_name: company,
					contact: contact,
					name: name
				})

				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	delete_supplier: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (isNaN(+id)) return fail(400, { id: true });
		await db
			.delete(supplier)
			.where(eq(supplier.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
