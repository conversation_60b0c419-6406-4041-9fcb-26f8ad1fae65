<script lang="ts">
	import type { PageServerData } from './$types';
	import { store } from '$lib/store/store.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import GenderAge from '$lib/coms/GenderAge.svelte';
	import Export from '$lib/coms/Export.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_billings, get_currency, items, get_patients, get_service_types } = $derived(data);
	let total_paid = $derived(get_billings?.reduce((acc, item) => acc + Number(item.paid), 0));
	let total_amount = $derived(get_billings?.reduce((acc, item) => acc + Number(item.amount), 0));
	let total = $derived(get_billings?.reduce((acc, item) => acc + Number(item.total), 0));
	let total_discount = $derived(total_amount - total);
	let total_after_tax = $derived(
		get_billings?.reduce((acc, item) => acc + Number(item.total_after_tax), 0)
	);
	let total_tax = $derived(total - total_after_tax);
	let total_credit = $derived(total - total_paid);
	let n = $state(1);
</script>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('sale_report')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="#/" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-file"></i>
					{locale.T('report')}
				</a>
			</li>
		</ol>
	</div>
</div>
<div class="card bg-light">
	<div class="card-header">
		<HeaderQuery class="row g-1">
			<div class="col-auto">
				<Export
					title="Billing report"
					data={get_billings.map((e) => {
						return {
							id: e.id,
							date: e.created_at,
							billing_type: e.billing_type,
							patient: e.patient?.name_khmer,
							contact: e.patient?.telephone,
							seller: e.staff?.name_khmer,
							amount: e.amount,
							discount: e.discount,
							total: e.total,
							tax: e.tax,
							total_after_tax: e.total_after_tax,
							paid: e.paid,
							balance: e.balance,
							service_type: e.serviceType?.by,
							status: e.status
						};
					})}
				/>
			</div>
			<div class="col-sm-auto">
				<div class="input-group">
					<span class="input-group-text">{locale.T('start')}</span>
					<input type="date" name="start" class="form-control" />
				</div>
			</div>
			<div class="col-sm-auto">
				<div class="input-group">
					<span class="input-group-text">{locale.T('end')}</span>
					<input type="date" name="end" class="form-control" />
				</div>
			</div>
			<div class="col-sm-2">
				<SelectParam
					q_name="q"
					placeholder={locale.T('patient')}
					name="patient_id"
					items={get_patients.map((e) => ({
						id: e.id,
						name: e.name_khmer?.concat(` ${e.name_latin}`)
					}))}
				/>
			</div>
			<div class="col-sm-auto">
				<div class="input-group">
					<span class="input-group-text">{locale.T('type_visit')}</span>
					<select name="billing_type" id="billing_type" class="form-control">
						<option value="">All</option>
						<option value="OPD">OPD</option>
						<option value="IPD">IPD</option>
						<option value="POS">POS</option>
					</select>
				</div>
			</div>
			<div class="col-sm-auto">
				<div class="input-group">
					<span class="input-group-text">{locale.T('status')}</span>
					<select name="status" id="status" class="form-control">
						<option value="">{locale.T('all')}</option>
						<option value="paid">{locale.T('paid')}</option>
						<option value="debt">{locale.T('debt')}</option>
						<option value="partial">{locale.T('partial')}</option>
					</select>
				</div>
			</div>
			<div class="col-sm-auto">
				<div class="input-group">
					<span class="input-group-text">{locale.T('type_payment')}</span>
					<select name="service_type_id" id="stservice_type_idatus" class="form-control">
						<option value="">{locale.T('all')}</option>
						{#each get_service_types as item}
							<option value={item.id}>{item.by}</option>
						{/each}
					</select>
				</div>
			</div>
		</HeaderQuery>
	</div>
	<div style="height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-bordered table-hover text-nowrap table-light">
			<thead class="sticky-top top-0 bg-light table-active">
				<tr class="text-center">
					<th>{locale.T('id')}</th>
					<th>{locale.T('date')}</th>
					<th>{locale.T('type')}</th>
					<th>{locale.T('name')}</th>
					<th>{locale.T('contact')}</th>
					<th>{locale.T('seller')}</th>
					<th>{locale.T('total')}</th>
					<th>{locale.T('discount')}</th>
					<th>{locale.T('total_after_disc')}</th>
					<th>{locale.T('tax')}</th>
					<th>{locale.T('grand_total')}</th>
					<th>{locale.T('paid')}</th>
					<th>{locale.T('balance')}</th>
					<th>{locale.T('status')}</th>
					<th></th>
				</tr>
			</thead>
			<tbody>
				{#each get_billings as item (item.id)}
					<tr class="text-center">
						<td>
							INV{item.id} <br />
							{#if item.billing_type !== 'POS'}
								VS{item.billing_type === 'OPD'
									? item.visit_id
									: item?.visit?.progress_note_id || item.progress_note_id}
							{/if}
						</td>
						<td>
							<DDMMYYYYFormat style="date" date={item.created_at} /> <br />
							<DDMMYYYYFormat style="time" date={item.created_at} />
						</td>
						<td>
							{item?.billing_type || ''}
						</td>
						<td>
							{#if !item.patient}
								<span>{locale.T('general')}</span>
							{:else}
								{item.patient?.name_khmer ?? ''}
								<br />
								{item.patient?.name_latin ?? ''}
								<GenderAge dob={item.patient.dob} date={new Date()} gender={item.patient.gender} />
							{/if}
						</td>
						<td>{item.patient?.telephone ?? ''}</td>
						<td>
							{item.staff?.name_khmer ?? ''} <br />
							{item.staff?.name_latin ?? ''}
						</td>
						<td>
							<Currency amount={item?.amount} symbol={get_currency?.currency} />
						</td>
						<td>
							{#if item.discount.includes('%')}
								{item.discount}
							{:else if Number(item.discount) > 0}
								<Currency amount={+item.discount} symbol={get_currency?.currency} />
							{/if}
						</td>
						<td>
							<Currency amount={item?.total} symbol={get_currency?.currency} />
						</td>
						<td>{item?.tax ?? ''} </td>
						<td>
							<Currency amount={item?.total_after_tax} symbol={get_currency?.currency} />
						</td>
						<td>
							<Currency amount={item?.paid} symbol={get_currency?.currency} />
						</td>
						<td>
							<Currency amount={item?.balance} symbol={get_currency?.currency} />
						</td>
						<td class="">
							<div>
								{item?.serviceType?.by ?? ''}
							</div>
							<div>
								{#if item.status === 'paid'}
									<span class="btn btn-sm btn-success py-0">{item.status ?? ''}</span>
								{:else if item.status === 'partial'}
									<span class="btn btn-sm btn-warning py-0">{item.status ?? ''}</span>
								{:else if item.status === 'debt'}
									<span class="btn btn-sm btn-danger py-0">{item.status ?? ''}</span>
								{/if}
							</div>
						</td>
						<td>
							<div class="btn-group">
								{#if item.billing_type === 'IPD'}
									<a
										aria-label="linkbilling1"
										target="_blank"
										href="/report/{item.progress_note_id}/billing/ipd"
										class="btn btn-success"><i class="fa-solid fa-receipt"></i></a
									>
								{:else if item.billing_type === 'OPD'}
									<a
										aria-label="linkbilling2"
										target="_blank"
										href="/report/{item.visit_id}/billing/opd"
										class="btn btn-success"><i class="fa-solid fa-receipt"></i></a
									>
								{:else if item.billing_type === 'CHECKING'}
									<a
										aria-label="linkbilling3"
										target="_blank"
										href="/report/{item.visit_id}/billing/opd"
										class="btn btn-success"><i class="fa-solid fa-receipt"></i></a
									>
								{:else}
									<a
										aria-label="linkbilling4"
										target="_blank"
										href="/report/{item.id}/billing/pos"
										class="btn btn-success"><i class="fa-solid fa-receipt"></i></a
									>
								{/if}
								<a
									aria-label="viewpaybilling"
									href="/billing/repay?billing_id={item.id}"
									class="btn btn-success"><i class="fa-solid fa-comments-dollar"></i></a
								>

								{#if item.billing_type === 'POS'}
									<a
										aria-label="billinglink"
										target="_blank"
										href="/billing/pos/{item.id}"
										class="btn btn-primary"><i class="fa-solid fa-file-pen"></i></a
									>
								{:else if item.billing_type === 'CHECKING'}
									<a
										aria-label="link-billing1"
										target="_blank"
										href="/billing/opd/{item.visit_id}"
										class="btn btn-primary"><i class="fa-solid fa-file-pen"></i></a
									>
								{:else if item.billing_type === 'IPD'}
									<a
										aria-label="link-billing2"
										target="_blank"
										href="/billing/ipd/{item.progress_note_id}"
										class="btn btn-primary"><i class="fa-solid fa-file-pen"></i></a
									>
								{:else}
									<a
										aria-label="link-billing3"
										target="_blank"
										href="/billing/opd/{item.visit_id}"
										class="btn btn-primary"><i class="fa-solid fa-file-pen"></i></a
									>
								{/if}
								<!-- <button aria-label="deletemodal" class="btn btn-danger"
									><i class="fa-solid fa-trash-alt"></i></button
								> -->
							</div>
						</td>
					</tr>
				{/each}
				<tr class="text-center table-success">
					<td>
						{get_billings.length}
						{locale.T('invoice')}
					</td>
					<td class="text-end" colspan="5">{locale.T('total')}</td>
					<td>
						<Currency class="" amount={total_amount} symbol={get_currency?.currency} />
					</td>
					<td>
						<Currency class="" amount={total_discount} symbol={get_currency?.currency} />
					</td>
					<td>
						<Currency class="" amount={total} symbol={get_currency?.currency} />
					</td>
					<td>
						<Currency class="" amount={total_tax} symbol={get_currency?.currency} />
					</td>
					<td>
						<Currency class="" amount={total_after_tax} symbol={get_currency?.currency} />
					</td>
					<td>
						<Currency class="" amount={total_paid} symbol={get_currency?.currency} />
					</td>
					<td>
						<Currency class="" amount={total_credit} symbol={get_currency?.currency} />
					</td>

					<td colspan="2"></td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations {items} bind:n />
	</div>
</div>
