import { db } from '$lib/server/db';
import { and, asc, eq, gt, like, ne, notLike, or } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { billing, paymentType, patient, group } from '$lib/server/schemas';
import { checkout, deleteProductOrder } from '$lib/server/models';
import { fail, redirect } from '@sveltejs/kit';
import { logError } from '$lib/server/utils/telegram';
import { YYYYMMDD_Format } from '$lib/server/utils';
import { productQuery } from '$lib/server/models/productQuery';

export const load: PageServerLoad = async ({ url, params }) => {
	const { id } = params;
	const get_currency = await db.query.currency.findFirst({});
	const category_id = url.searchParams.get('category_id') || '';
	const patient_q = url.searchParams.get('patient_q') || '';
	const get_billing = await db.query.billing.findFirst({
		where: eq(billing.id, +id || 0),
		with: {
			payment: {
				with: {
					paymentType: true
				}
			},
			patient: true,
			charge: {
				with: {
					productOrder: {
						with: {
							product: {
								with: {
									subUnit: {
										with: {
											unit: true
										}
									},
									unit: true
								}
							},
							unit: true
						}
					}
				}
			}
		}
	});
	const get_pre_billing = await db.query.billing.findMany({
		with: {
			patient: true,
			charge: {
				with: {
					productOrder: {
						with: {
							product: {
								with: {
									subUnit: {
										with: {
											unit: true
										}
									},
									unit: true
								}
							},
							unit: true
						}
					}
				}
			}
		},
		where: and(
			eq(billing.status, 'paying'),
			eq(billing.billing_type, 'POS'),
			gt(billing.amount, 0),
			ne(billing.id, +id)
		)
	});
	const get_patients = await db.query.patient.findMany({
		where: or(
			like(patient.name_latin, `%${patient_q}%`),
			like(patient.name_khmer, `%${patient_q}%`),
			like(patient.telephone, `%${patient_q}%`)
		),
		limit: 200
	});
	// if (get_billing?.status !== 'process') redirect(303, '/billing/opd');
	const get_categories = await db.query.category.findMany({});
	const get_products = await productQuery({ url, page: false });
	const get_groups = await db.query.group.findMany({
		where: category_id ? eq(group.category_id, +category_id) : undefined
	});
	const get_payment_types = await db.query.paymentType.findMany({
		orderBy: asc(paymentType.by),
		where: notLike(paymentType.by, '%CASH%')
	});
	const get_billings_due = await db.query.billing.findMany({
		where: and(
			gt(billing.balance, 0),
			eq(billing.patient_id, get_billing?.patient_id || 0),
			ne(billing.id, get_billing?.id || 0),
			eq(billing.hold, true)
		)
	});
	const charge_on_general = get_billing?.charge.find((e) => e.charge_on === 'general');
	return {
		...get_products,
		get_categories,
		charge_on_general,
		get_billing,
		get_payment_types,
		get_currency,
		get_pre_billing,
		get_billings_due,
		get_patients,
		get_groups
	};
};
export const actions: Actions = {
	create_product_order: async (e) => {
		await checkout.create_product_order(e);
	},
	remove_product_order: async (e) => {
		await checkout.remove_product_order(e);
	},
	update_product_order: async (e) => {
		await checkout.update_product_order(e);
	},
	discount_product_order: async (e) => {
		await checkout.discount_product_order(e);
	},
	process_billing: async (e) => {
		await checkout.process_billing(e);
	},
	hold: async (e) => {
		await checkout.hold(e);
	},
	delete_payment: async (e) => {
		await checkout.delete_payment(e);
	},
	create_patient: async (e) => {
		const body = await e.request.formData();
		const url = e.url;
		const { name_khmer, name_latin, telephone, gender } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		const validErr = {
			name_khmer: false,
			name_latin: false,
			telephone: false,
			gender: false
		};
		if (!name_khmer.trim()) validErr.name_khmer = true;
		if (!name_latin.trim()) validErr.name_latin = true;
		if (!telephone.trim()) validErr.telephone = true;
		if (!gender.trim()) validErr.gender = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		const create_patient: { id: number }[] = await db
			.insert(patient)
			.values({
				name_khmer: name_khmer,
				name_latin: name_latin,
				telephone: telephone,
				gender: gender,
				created_at: YYYYMMDD_Format.datetime(new Date())
			})
			.$returningId()
			.catch((e) => {
				logError({ url, body, err: e });
				return [];
			});
		redirect(303, `/billing/pos/${e.params.id}?patient_id=${create_patient[0].id}`);
	},
	update_billing_customer: async (e) => {
		const url = e.url;
		const body = await e.request.formData();
		const { patient_id, billing_id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(billing)
			.set({
				patient_id: patient_id ? +patient_id : null
			})
			.where(eq(billing.id, +billing_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		// redirect(303, `/billing/pos/${e.params.id}?patient_id=${patient_id}`);
	},
	delete_billing: async (e) => {
		const body = await e.request.formData();
		const url = e.url;
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(billing)
			.where(eq(billing.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	refresh_billing: async (e) => {
		const body = await e.request.formData();
		const url = e.url;
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(billing)
			.set({
				created_at: YYYYMMDD_Format.datetime(new Date())
			})
			.where(eq(billing.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	clear_billing: async (e) => {
		const body = await e.request.formData();
		const product_order_id = body.getAll('product_order_id') ?? '';
		if (!product_order_id.length) return fail(400, { err: 'product_order_id' });
		for (const e of product_order_id) {
			await deleteProductOrder(Number(e));
		}
	}
};
