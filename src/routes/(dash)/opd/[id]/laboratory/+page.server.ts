import { db } from '$lib/server/db';
import { laboratory, laboratoryRequest, product, visit } from '$lib/server/schemas';
import type { Actions, PageServerLoad } from './$types';
import { asc, eq } from 'drizzle-orm';
import { createProductOrder, deleteProductOrder, setChargePrice } from '$lib/server/models';
import { logError, message } from '$lib/server/utils/telegram';
import { DDMMYYYY_Format, YYYYMMDD_Format } from '$lib/server/utils';
import { fail } from '@sveltejs/kit';
export const load = (async ({ params }) => {
	const id = parseInt(params.id);
	const get_currency = await db.query.currency.findFirst({});
	const get_visit = await db.query.visit.findFirst({
		with: {
			laboratoryRequest: {
				with: {
					product: true
				}
			},
			patient: true,
			staff: true,
			billing: {
				with: {
					charge: true
				}
			}
		},
		where: eq(visit.id, +id)
	});
	const get_laboratory_group = await db.query.laboratoryGroup.findMany({
		with: {
			product: {
				orderBy: asc(product.products)
			}
		}
	});
	return {
		get_laboratory_group,
		get_visit,
		get_currency
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_laboratory_request: async ({ request, params, url }) => {
		const id = parseInt(params.id);
		const body = await request.formData();
		const { product_id } = Object.fromEntries(body) as Record<string, string>;
		if (!product_id) return fail(400, { errId: true });
		const get_visit = await db.query.visit.findFirst({
			where: eq(visit.id, +id),
			with: {
				laboratoryRequest: true,
				laboratory: true,
				patient: true,
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				},
				staff: {
					with: {
						title: true
					}
				}
			}
		});
		const charge_on_laboratory = get_visit?.billing?.charge.find(
			(e) => e.charge_on === 'laboratory'
		);

		const is_created = get_visit?.laboratoryRequest.some((ee) => ee.product_id === +product_id);
		if (is_created) return fail(400, { errId: true });
		if (!is_created) {
			const get_product = await db.query.product.findFirst({
				where: eq(product.id, +product_id)
			});
			await db.insert(laboratoryRequest).values({
				product_id: +product_id,
				visit_id: get_visit?.id
			});
			if (get_visit?.laboratory?.id) {
				await db
					.update(laboratory)
					.set({
						status: false,
						visit_id: get_visit?.id
					})
					.where(eq(laboratory.id, get_visit.laboratory.id))
					.catch((e) => {
						logError({ url, body, err: e });
					});
			}
			if (!get_visit?.laboratory?.id) {
				await db
					.insert(laboratory)
					.values({
						status: false,
						visit_id: get_visit?.id,
						patient_id: get_visit?.patient_id,
						request_datetime: YYYYMMDD_Format.datetime(new Date())
					})
					.catch((e) => {
						logError({ url, body, err: e });
					});
				const khemr_date = 'កាលបរិច្ឆេទ៖ '.concat(
					DDMMYYYY_Format(new Date().toISOString(), 'datetime')
				);
				const text = khemr_date
					.concat('\n')
					.concat('ឈ្មោះអ្នកជំងឺ៖ ')
					.concat(`${get_visit?.patient?.name_khmer}(${get_visit?.patient?.name_latin})`)
					.concat('\n')
					.concat('គ្រូពេទ្យស្នើរសុំ៖ ')
					.concat(get_visit?.staff?.title?.kh ?? '')
					.concat(' ')
					.concat(get_visit?.staff?.name_khmer ?? '')
					.concat('\n')
					.concat('សេវាកម្ម៖ ')
					.concat('មន្ទីពិសោធន៍')
					.concat('\n')
					.concat('ID៖ ')
					.concat(`VS${get_visit?.id}`);
				await message(text, 'IMAGERIE');
			}
			await createProductOrder({
				charge_id: Number(charge_on_laboratory?.id),
				price: Number(get_product?.price),
				product_id: Number(+product_id),
				qty: 1,
				body: body,
				url: url
			});
		}
	},

	update_total_laboratory: async ({ request, params }) => {
		const { id } = params;
		const body = await request.formData();
		const { total_laboratory } = Object.fromEntries(body) as Record<string, string>;
		const get_visit = await db.query.visit.findFirst({
			where: eq(visit.id, +id),
			with: {
				laboratoryRequest: true,
				laboratory: true,
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				}
			}
		});

		const charge_on_laboratory = get_visit?.billing?.charge.find(
			(e) => e.charge_on === 'laboratory'
		);
		if (charge_on_laboratory) {
			await setChargePrice(charge_on_laboratory.id, +total_laboratory);
		}
	},
	delete_laboratory_request: async ({ request, url, params }) => {
		const body = await request.formData();
		const visit_id = params.id;
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (!id || !visit_id) return fail(400, { errId: true });
		const get_visit = await db.query.visit.findFirst({
			where: eq(visit.id, +visit_id),
			with: {
				laboratoryRequest: true,
				laboratory: true,
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				}
			}
		});
		const get_laboratory_request = await db.query.laboratoryRequest.findFirst({
			where: eq(laboratoryRequest.id, +id)
		});
		const charge_on_laboratory = get_visit?.billing?.charge.find(
			(e) => e.charge_on === 'laboratory'
		);
		const product_order_ = charge_on_laboratory?.productOrder.find(
			(ee) => ee.product_id === Number(get_laboratory_request?.product_id)
		);
		if (!get_visit || !product_order_?.id) return fail(400, { errId: true });

		await db
			.delete(laboratoryRequest)
			.where(eq(laboratoryRequest.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await deleteProductOrder(Number(product_order_?.id)).catch((e) => {
			logError({ url, body, err: e });
		});
		if (get_visit?.laboratoryRequest.length === 1) {
			await db
				.delete(laboratory)
				.where(eq(laboratory.visit_id, +visit_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	}
};
