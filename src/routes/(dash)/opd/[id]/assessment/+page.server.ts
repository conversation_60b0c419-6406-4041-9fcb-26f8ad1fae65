import { db } from '$lib/server/db';
import { accessment, diagnosis, diagnosisICD } from '$lib/server/schemas';
import { logError } from '$lib/server/utils/telegram';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { and, eq, like } from 'drizzle-orm';

export const load = (async ({ params, url }) => {
	const visit_id = params.id;
	const diagnosis_type_id = url.searchParams.get('diagnosis_type_id') ?? '';
	const icd_number = url.searchParams.get('icd_number') ?? '';
	const icd_en = url.searchParams.get('icd_en') ?? '';
	const icd_kh = url.searchParams.get('icd_kh') ?? '';
	const icd_fr = url.searchParams.get('icd_fr') ?? '';
	const get_diagnosis_icd = await db.query.diagnosisICD.findMany({
		where: and(
			like(diagnosisICD.diagnosis_en, `%${icd_en}%`),
			like(diagnosisICD.diagnosis_kh, `%${icd_kh}%`),
			like(diagnosisICD.diagnosis_fr, `%${icd_fr}%`),
			like(diagnosisICD.ICD_10, `%${icd_number}%`)
		),
		limit: 500
	});
	const get_diagnosis = await db.query.diagnosis.findMany({
		where: eq(diagnosis.diagnosis_type_id, +diagnosis_type_id)
	});
	const get_diagnosisTypes = await db.query.diagnosisType.findMany({});

	const get_accessment = await db.query.accessment.findFirst({
		where: eq(accessment.visit_id, +visit_id)
	});

	return {
		get_diagnosis,
		get_diagnosisTypes,
		get_accessment,
		get_diagnosis_icd
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_accessment: async ({ request, params, url }) => {
		const visit_id = params.id;
		const body = await request.formData();
		const { diagnosis_differential, diagnosis_or_problem, assessment_process } = Object.fromEntries(
			body
		) as Record<string, string>;
		const check_accessment = await db.query.accessment.findFirst({
			where: eq(accessment.visit_id, +visit_id)
		});
		if (check_accessment) {
			await db
				.update(accessment)
				.set({
					diagnosis_or_problem: diagnosis_or_problem,
					differential_diagnosis: diagnosis_differential,
					assessment_process: assessment_process
				})
				.where(eq(accessment.id, check_accessment.id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		if (!check_accessment) {
			await db
				.insert(accessment)
				.values({
					diagnosis_or_problem: diagnosis_or_problem,
					differential_diagnosis: diagnosis_differential,
					assessment_process: assessment_process,
					visit_id: +visit_id
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	create_diagnosis: async ({ request, url }) => {
		const body = await request.formData();
		const { diagnosis_type_id, diagnosis_english, diagnosis_khmer, diagnosis_id } =
			Object.fromEntries(body) as Record<string, string>;
		if (!diagnosis_type_id || !diagnosis_english)
			return fail(400, { message: 'Diagnosis Type ID is required' });
		if (!diagnosis_id) {
			await db
				.insert(diagnosis)
				.values({
					diagnosis_type_id: +diagnosis_type_id,
					diagnosis: diagnosis_english,
					diagnosis_khmer: diagnosis_khmer
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		} else {
			await db
				.update(diagnosis)
				.set({
					diagnosis_type_id: +diagnosis_type_id,
					diagnosis: diagnosis_english,
					diagnosis_khmer: diagnosis_khmer
				})
				.where(eq(diagnosis.id, +diagnosis_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	delete_diagnosis: async ({ request, url }) => {
		const body = await request.formData();
		const { diagnosis_id } = Object.fromEntries(body) as Record<string, string>;
		if (!diagnosis_id) return fail(400, { message: 'Diagnosis ID is required' });
		await db
			.delete(diagnosis)
			.where(eq(diagnosis.id, +diagnosis_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	create_diagnosis_icd: async ({ request, url }) => {
		const body = await request.formData();
		const { diagnosis_en, diagnosis_kh, diagnosis_fr, diagnosis_icd_id, icd_10 } =
			Object.fromEntries(body) as Record<string, string>;

		if (diagnosis_icd_id) {
			await db
				.update(diagnosisICD)
				.set({
					diagnosis_en: diagnosis_en,
					diagnosis_kh: diagnosis_kh,
					diagnosis_fr: diagnosis_fr,
					ICD_10: icd_10
				})
				.where(eq(diagnosisICD.id, +diagnosis_icd_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		} else {
			if (!diagnosis_en && !diagnosis_kh && !diagnosis_fr && !icd_10)
				return fail(400, { message: 'Diagnosis is required' });
			await db
				.insert(diagnosisICD)
				.values({
					diagnosis_en: diagnosis_en,
					diagnosis_kh: diagnosis_kh,
					diagnosis_fr: diagnosis_fr,
					ICD_10: icd_10
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	delete_diagnosis_icd: async ({ request, url }) => {
		const body = await request.formData();
		const { diagnosis_icd_id } = Object.fromEntries(body) as Record<string, string>;
		if (!diagnosis_icd_id) return fail(400, { message: 'Diagnosis ID is required' });
		await db
			.delete(diagnosisICD)
			.where(eq(diagnosisICD.id, +diagnosis_icd_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
