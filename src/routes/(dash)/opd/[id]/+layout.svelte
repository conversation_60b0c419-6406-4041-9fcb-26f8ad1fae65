<script lang="ts">
	import { page } from '$app/state';
	import CopyPrescription from '$lib/coms-ipd-opd/CopyPrescription.svelte';
	import Athtml from '$lib/coms/Athtml.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { LayoutServerData } from './$types';
	import TimeFormat from '$lib/coms/TimeFormat.svelte';
	import PatientInfo from '$lib/coms-ipd-opd/PatientInfo.svelte';
	import SendBillingToPayment from '$lib/coms-billing/SendBillingToPayment.svelte';
	import ConfirmModal from '$lib/coms-form/ConfirmModal.svelte';
	import Zoom from '$lib/coms/Zoom.svelte';
	import SetBack from '$lib/coms/SetBack.svelte';
	let visit_id = $derived(page.params.id);
	interface Props {
		data: LayoutServerData;
		children?: import('svelte').Snippet;
	}

	let { data, children }: Props = $props();
	let { get_visit, get_visits, get_exams, find_old_visit, patient_info } = $derived(data);
	let sort_laboraytor = $derived(
		find_old_visit?.laboratoryRequest.sort((a) => {
			if (a.product?.products.includes('CBC')) return -1;
			else return 1;
		})
	);
	let mean_arterial_pressure = $derived(
		(1 / 3) * Number(find_old_visit?.vitalSign?.sbp) +
			(2 / 3) * Number(find_old_visit?.vitalSign?.dbp)
	);
	let is_scan = $state(false);
	let is_input = $state(false);
	let imagerie_id: number | null = $state(null);
	let zoom = $state(100);
</script>

<div class="row g-0">
	<div class="col-sm-6">
		{#if get_visit?.progress_note_id}
			<a href="/ipd/{get_visit?.progress_note_id}/progress-note" class="btn btn-link m-0 p-0"
				><i class="fa-solid fa-rotate-left"></i>
				{locale.T('back')}
			</a>
		{:else}
			<a href="/patient/opd" class="btn btn-link m-0 p-0"
				><i class="fa-solid fa-rotate-left"></i>
				{locale.T('back')}
			</a>
		{/if}
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-restroom"></i>
					{locale.T('patient')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="#/" class="btn btn-link p-0 text-secondary">
					{#if get_visit?.progress_note_id}
						<i class="nav-icon fas fa-procedures"></i>
						{locale.T('ipd')}
					{:else}
						<i class=" fas fa-stethoscope"></i>
						{locale.T('opd')}
					{/if}
				</a>
			</li>
		</ol>
	</div>
</div>

<PatientInfo {patient_info} />

<div class="row pt-2">
	<div data-sveltekit-noscroll class="col-sm-12">
		<a
			href="/opd/{visit_id}/subjective "
			class={page.url.pathname.includes('subjective')
				? 'btn btn-primary  mb-2'
				: 'btn btn-outline-primary mb-2'}>Subjective</a
		>
		<a
			href="/opd/{visit_id}/objective "
			class={page.url.pathname.includes('objective')
				? 'btn btn-primary mb-2'
				: 'btn btn-outline-primary mb-2'}>Objective</a
		>
		<a
			href="/opd/{visit_id}/laboratory "
			class={page.url.pathname.includes('laboratory')
				? 'btn btn-primary mb-2'
				: 'btn btn-outline-primary mb-2'}>Laboratory</a
		>
		<a
			href="/opd/{visit_id}/imagerie "
			class={page.url.pathname.includes('imagerie')
				? 'btn btn-primary mb-2'
				: 'btn btn-outline-primary mb-2'}>Imagerie</a
		>
		<a
			href="/opd/{visit_id}/assessment "
			class={page.url.pathname.includes('assessment')
				? 'btn btn-primary mb-2'
				: 'btn btn-outline-primary mb-2'}>Assessment</a
		>
		<a
			href="/opd/{visit_id}/prescription "
			class={page.url.pathname.includes('prescription')
				? 'btn btn-primary mb-2'
				: 'btn btn-outline-primary mb-2'}>Prescription</a
		>
		{#if !get_visit.progress_note_id}
			<a
				href="/opd/{visit_id}/appointment "
				class={page.url.pathname.includes('appointment')
					? 'btn btn-primary mb-2'
					: 'btn btn-outline-primary mb-2'}>Appointment</a
			>

			<a
				href="/opd/{visit_id}/service "
				class={page.url.pathname.includes('service')
					? 'btn btn-primary mb-2'
					: 'btn btn-outline-primary mb-2'}>Services</a
			>
		{/if}
		<a
			href="/opd/{visit_id}/vaccine "
			class={page.url.pathname.includes('vaccine')
				? 'btn btn-primary mb-2'
				: 'btn btn-outline-primary mb-2'}>Vaccine</a
		>
		{#if !get_visit.progress_note_id}
			<div class="float-end">
				<SendBillingToPayment btn_size="btn-md" status={get_visit.billing?.status}>
					<input type="hidden" name="visit_id" value={visit_id} />
					<input type="hidden" name="id" value={get_visit.billing?.id ?? ''} />
					<ul class="list-group pb-2">
						<li class="list-group-item text-danger">
							<i class="fa-solid fa-triangle-exclamation"></i>
							{locale.T('please_verify_before_sending')}
						</li>
					</ul>
				</SendBillingToPayment>
			</div>
		{/if}
	</div>
</div>

{@render children?.()}

{#if !get_visit.progress_note_id}
	<hr />
	<div class="row">
		{#each get_visits as item}
			<div class="col-auto">
				<a
					data-sveltekit-noscroll
					href="?old_visit_id={item.id}"
					class={page.url.searchParams.get('old_visit_id') === item.id.toString()
						? 'btn btn-primary mb-3'
						: 'btn btn-outline-primary mb-3'}
					>{item?.checkin_type}
					<DDMMYYYYFormat date={item.date_checkup} />
					ID{item?.id ?? ''}</a
				>
			</div>
		{/each}
	</div>
{/if}

{#if find_old_visit}
	<div class="card">
		<div class="card-header">
			<div class="row justify-content-between">
				<div class="col-6">
					<span class="fs-5">SOAP NOTE</span>
				</div>
				<div class="col-6">
					<a
						target="_blank"
						aria-label="nersing_process"
						href="/print/{find_old_visit.id}/soab-note"
						class="btn btn-success float-end btn-sm"
						><i class="fa-solid fa-print"></i>
					</a>
					<div class="btn-group float-end me-2">
						<Zoom class="btn btn-success btn-sm " bind:zoom />
					</div>
				</div>
			</div>
		</div>
		<div style="font-size: 90%; zoom: {zoom}%;" class="row card-body">
			<div class="col-lg-4">
				<h4 class="text-center">Observation note</h4>
				{#if find_old_visit.vitalSign}
					<div class="border rounded border-1 p-2 mb-2">
						<span class="btn btn-success btn-sm mb-2 py-0">VitalSign</span>
						<table class="table table-sm">
							<thead>
								{#if find_old_visit.vitalSign?.sbp}
									<tr>
										<td style="width: 40%;">BP(mmHg)</td>
										<td style="width: 5%;">:</td>
										<td style="width: 55%;">
											{find_old_visit?.vitalSign?.sbp?.toFixed(0).concat(' /') ?? ''}
											{find_old_visit?.vitalSign?.dbp?.toFixed(0).concat(' mmHg') ?? ''}
										</td>
									</tr>
								{/if}
								{#if mean_arterial_pressure}
									<tr>
										<td style="width: 40%;">MAP</td>
										<td style="width: 5%;">:</td>
										<td style="width: 55%;">
											{#if mean_arterial_pressure}
												{mean_arterial_pressure
													? mean_arterial_pressure?.toFixed(0).concat(' mmHg')
													: ''}
											{/if}
										</td>
									</tr>
								{/if}
								{#if find_old_visit.vitalSign?.pulse}
									<tr>
										<td style="width: 40%;">Pulse (min)</td>
										<td style="width: 5%;">:</td>
										<td style="width: 20%;"
											>{find_old_visit?.vitalSign?.pulse?.toFixed(0).concat(' /min') ?? ''}</td
										>
									</tr>
								{/if}
								{#if find_old_visit.vitalSign?.t}
									<tr>
										<td style="width: 40%;">Temperature °C </td>
										<td style="width: 5%;">:</td>
										<td style="width: 55%;"
											><Athtml
												html={find_old_visit?.vitalSign?.t?.toFixed(1).concat(' &deg;C') ?? ''}
											/></td
										>
									</tr>
								{/if}
								{#if find_old_visit.vitalSign?.rr}
									<tr>
										<td style="width: 40%;">RR (min)</td>
										<td style="width: 5%;">:</td>
										<td style="width: 55%;"
											>{find_old_visit?.vitalSign?.rr?.toFixed(0).concat(' /min') ?? ''}</td
										>
									</tr>
								{/if}
								{#if find_old_visit.vitalSign?.sp02}
									<tr>
										<td style="width: 40%;">SpO2 (%)</td>
										<td style="width: 5%;">:</td>
										<td style="width: 55%;"
											>{find_old_visit?.vitalSign?.sp02?.toFixed(0).concat(' %') ?? ''}</td
										>
									</tr>
								{/if}
								{#if find_old_visit.vitalSign?.height}
									<tr>
										<td style="width: 40%;">Height (cm)</td>
										<td style="width: 5%;">:</td>
										<td style="width: 55%;"
											>{find_old_visit?.vitalSign?.height?.toFixed(0).concat(' cm') ?? ''}</td
										>
									</tr>
								{/if}
								{#if find_old_visit.vitalSign?.weight}
									<tr>
										<td style="width: 40%;">Weight (kg)</td>
										<td style="width: 5%;">:</td>
										<td style="width: 55%;"
											>{find_old_visit?.vitalSign?.weight?.toFixed(0).concat(' kg') ?? ''}</td
										>
									</tr>
								{/if}
								{#if find_old_visit.vitalSign?.bmi}
									<tr>
										<td style="width: 40%;">BMI</td>
										<td style="width: 5%;">:</td>
										<td style="width: 55%;"
											>{find_old_visit?.vitalSign?.bmi?.toFixed(1).concat(' kg/m2') ?? ''}</td
										>
									</tr>
								{/if}
							</thead>
						</table>
					</div>
				{/if}
				{#if find_old_visit.subjective?.cheif_complaint}
					<div class="border rounded border-1 p-2 mb-2">
						<span class="btn btn-success btn-sm mb-2 py-0">Cheif complaint</span>
						<p class="text-break">
							{find_old_visit.subjective?.cheif_complaint ?? ''}
						</p>
					</div>
				{/if}
				{#if find_old_visit.subjective?.history_of_present_illness}
					<div class="border rounded border-1 p-2 mb-2">
						<span class="btn btn-success btn-sm mb-2 py-0">History of Present illness</span>
						<p class="text-break">
							{find_old_visit.subjective?.history_of_present_illness ?? ''}
						</p>
					</div>
				{/if}
				{#if find_old_visit.subjective}
					<div class="border rounded border-1 p-2 mb-2">
						<span class="btn btn-success btn-sm mb-2 py-0">Past medicine history</span>
						<table class="table-sm table">
							<thead>
								{#if find_old_visit.subjective?.current_medication}
									<tr>
										<td style="width: 40%;"> Current Medication</td>
										<td style="width: 5%;">:</td>
										<td style="width: 55%;">
											{find_old_visit.subjective?.current_medication ?? ''}
										</td>
									</tr>
								{/if}
								{#if find_old_visit.subjective?.past_medical_history}
									<tr>
										<td style="width: 40%;">Past medical history</td>
										<td style="width: 5%;">:</td>
										<td style="width: 55%;">
											{find_old_visit.subjective?.past_medical_history ?? ''}
										</td>
									</tr>
								{/if}
								{#if find_old_visit.subjective?.allesgy_medicine}
									<tr>
										<td style="width: 40%;">Allergy medicine</td>
										<td style="width: 5%;">:</td>
										<td style="width: 55%;">
											{find_old_visit.subjective?.allesgy_medicine ?? ''}
										</td>
									</tr>
								{/if}
								{#if find_old_visit.subjective?.surgical_history}
									<tr>
										<td style="width: 40%;">Surgical history</td>
										<td style="width: 5%;">:</td>
										<td style="width: 55%;">
											{find_old_visit.subjective?.surgical_history ?? ''}
										</td>
									</tr>
								{/if}
								{#if find_old_visit.subjective?.family_and_social_history}
									<tr>
										<td style="width: 40%;">Family and social history</td>
										<td style="width: 5%;">:</td>
										<td style="width: 55%;">
											{find_old_visit.subjective?.family_and_social_history ?? ''}
										</td>
									</tr>
								{/if}
							</thead>
						</table>
					</div>
				{/if}
				{#if find_old_visit.subjective?.pre_diagnosis}
					<div class="border rounded border-1 p-2 mb-2">
						<span class="btn btn-success btn-sm mb-2 py-0">Pre Diagnosis </span>
						<p class="text-break">
							{find_old_visit.subjective?.pre_diagnosis ?? ''}
						</p>
					</div>
				{/if}
				{#if find_old_visit.physicalExam.length}
					<button class="btn btn-success btn-sm mb-2 py-0">Physical Exam</button>
				{/if}
				{#each get_exams as exam}
					{@const physicals = exam.physical}
					{#if find_old_visit.physicalExam.some((e) => e.physical?.exam_id === exam.id)}
						<div class="border rounded border-1 p-2 mb-2">
							<span class="fs-6 text-decoration-underline text-primary"
								>{exam.examination ?? ''}</span
							>
							<table class="table table-sm">
								<thead>
									{#each physicals as physical}
										{#each find_old_visit.physicalExam as physical_exam}
											{#if physical_exam.physical_id === physical.id}
												{#if physical_exam.result}
													<tr>
														<td style="width: 40%;"> {physical.physical}</td>
														<td style="width: 5%;">:</td>
														<td style="width: 55%;">
															{physical_exam.result ?? ''}
														</td>
													</tr>
												{/if}
											{/if}
										{/each}
									{/each}
								</thead>
							</table>
						</div>
					{/if}
				{/each}
				{#if find_old_visit?.accessment}
					<div class="border rounded border-1 p-2 mb-2">
						<span class="btn btn-success btn-sm mb-2 py-0">Diagnosis</span>
						<p class="text-break">
							{find_old_visit.accessment.diagnosis_or_problem ?? ''}
						</p>
					</div>
					<div class="border rounded border-1 p-2 mb-2">
						<span class="btn btn-success btn-sm mb-2 py-0">Differential Diagnosis </span>
						<p class="text-break">
							{find_old_visit.accessment.differential_diagnosis ?? ''}
						</p>
					</div>
				{/if}
				{#if find_old_visit.accessment?.assessment_process}
					<div class="border rounded border-1 p-2 mb-2">
						<span class="btn btn-success btn-sm mb-2 py-0">Assessment Process</span>
						<p class="text-break">
							{find_old_visit.accessment?.assessment_process}
						</p>
					</div>
				{/if}
				{#if find_old_visit.service.length}
					<button class="btn btn-success btn-sm mb-2 py-0">Service</button>
				{/if}
				{#if find_old_visit?.service.length}
					{#each find_old_visit.service || [] as item (item.id)}
						<div class="border rounded border-1 p-2 mb-2">
							<span class="fs-6 text-decoration-underline text-primary"
								>{item.product?.products ?? ''}</span
							>
							<table class="table">
								<thead>
									<tr>
										<td style="width: 40%;">
											<span>Surgeon</span>
										</td>
										<td style="width: 5%;">:</td>
										<td style="width:50%;">
											<span>{item?.operationProtocol?.surgeon ?? ''}</span>
										</td>
									</tr>
									<tr>
										<td style="width: 40%;">
											<span>Assistant Surgeon</span>
										</td>
										<td style="width: 5%;">:</td>
										<td style="width:50%;">
											<span>{item?.operationProtocol?.assistant_surgeon ?? ''}</span>
										</td>
									</tr>
									<tr>
										<td style="width: 40%;">
											<span>Anesthetist</span>
										</td>
										<td style="width: 5%;">:</td>
										<td style="width:50%;">
											<span>{item?.operationProtocol?.anesthetist ?? ''}</span>
										</td>
									</tr>
									<tr>
										<td style="width: 40%;">
											<span>Assistant Anesthetist</span>
										</td>
										<td style="width: 5%;">:</td>
										<td style="width:50%;">
											<span>{item?.operationProtocol?.assistant_anesthetist ?? ''}</span>
										</td>
									</tr>
									<tr>
										<td style="width: 40%;">
											<span>Scrub Nurse</span>
										</td>
										<td style="width: 5%;">:</td>
										<td style="width:50%;">
											<span>{item?.operationProtocol?.scrub_nurse ?? ''}</span>
										</td>
									</tr>
									<tr>
										<td style="width: 40%;">
											<span>Circulation / Nurse block</span>
										</td>
										<td style="width: 5%;">:</td>
										<td style="width:50%;">
											<span>{item?.operationProtocol?.cirulating_nurse_block ?? ''}</span>
										</td>
									</tr>
									<tr>
										<td style="width: 40%;">
											<span>Midwife</span>
										</td>
										<td style="width: 5%;">:</td>
										<td style="width:50%;">
											<span>{item?.operationProtocol?.midwife ?? ''}</span>
										</td>
									</tr>
									{#if item?.operationProtocol?.date}
										<tr>
											<td style="width: 40%;">
												<span>Dates</span>
											</td>
											<td style="width: 5%;">:</td>
											<td style="width:50%;">
												<span>
													<DDMMYYYYFormat style="date" date={item?.operationProtocol.date} />
												</span>
											</td>
										</tr>
									{/if}
									{#if item?.operationProtocol?.start_time}
										<tr>
											<td style="width: 40%;">
												<span>StartTime</span>
											</td>
											<td style="width: 5%;">:</td>
											<td style="width:50%;">
												<TimeFormat time={item?.operationProtocol?.start_time} />
											</td>
										</tr>
									{/if}
									{#if item?.operationProtocol?.finish_time}
										<tr>
											<td style="width: 40%;">
												<span>FinishTime</span>
											</td>
											<td style="width: 5%;">:</td>
											<td style="width:50%;">
												<TimeFormat time={item?.operationProtocol?.finish_time} />
											</td>
										</tr>
									{/if}
									<tr>
										<td style="width: 40%;">
											<span>Pre-Diagnosis</span>
										</td>
										<td style="width: 5%;">:</td>
										<td style="width:50%;">
											<span>{item?.operationProtocol?.pre_diagnosis ?? ''}</span>
										</td>
									</tr>
									<tr>
										<td style="width: 40%;">
											<span>Post Diagnosis</span>
										</td>
										<td style="width: 5%;">:</td>
										<td style="width:50%;">
											<span>{item?.operationProtocol?.post_diagnosis ?? ''}</span>
										</td>
									</tr>
									<tr>
										<td style="width: 40%;">
											<span>Type Anesthesia</span>
										</td>
										<td style="width: 5%;">:</td>
										<td style="width:50%;">
											<span>{item?.operationProtocol?.type_anesthesia ?? ''}</span>
										</td>
									</tr>
									<tr>
										<td colspan="3" class="text-wrap" style="width: 100%;">
											<div>
												<span>Opertive Technique</span>
												{item?.operationProtocol?.opertive_technique ?? ''}
											</div>
										</td>
									</tr>
									<tr>
										<td style="width: 40%;">
											<span>Blood Less </span>
										</td>
										<td style="width: 5%;">:</td>
										<td style="width:50%;">
											<span>{item?.operationProtocol?.blood_less ?? ''}</span>
										</td>
									</tr>
									<tr>
										<td colspan="3" class="text-wrap" style="min-width: 100%;">
											<div>
												<span>Notes</span>
												<div class="text-break">
													{item?.operationProtocol?.notes ?? ''}
												</div>
											</div></td
										>
									</tr>
								</thead>
							</table>
						</div>
					{/each}
				{/if}
			</div>
			<div class="col-lg-4">
				<h4 class="text-center">Para-Clinic</h4>
				{#if sort_laboraytor?.length}
					<button class="btn btn-success btn-sm mb-2 py-0">Laboratory</button>
					{#if find_old_visit?.laboratory?.status === true}
						<a
							target="_blank"
							aria-label="print_laboratory_1"
							href="/report/{find_old_visit?.id}/laboratory"
							class="btn btn-success btn-sm mb-2 py-0"
							><i class="fa-solid fa-print"></i>
						</a>
					{:else}
						<button
							disabled
							aria-label="print_laboratory_2"
							class="btn btn-success btn-sm mb-2 py-0"
							><i class="fa-solid fa-print"></i>
						</button>
					{/if}
					<SetBack
						class="btn btn-success btn-sm mb-2 py-0 float-end"
						href="/print/{find_old_visit?.id}/label/barcode"
					>
						{locale.T('print')} <i class="fa-solid fa-barcode"></i>
					</SetBack>
				{/if}
				{#each sort_laboraytor || [] as laboratory_request}
					{@const laboratory_results = laboratory_request.laboratoryResult}
					{@const parameters = laboratory_request.product?.parameter}
					<div class="border rounded border-1 p-2 mb-2">
						<span class="fs-6 text-decoration-underline text-primary"
							>{laboratory_request.product?.products ?? ''}</span
						>
						<table class="table-sm table">
							<thead>
								{#each parameters || [] as parameter}
									{#if parameter.gender === get_visit.patient.gender || parameter.gender === 'Other'}
										<tr>
											<td style="width: 40%;"> {parameter.parameter ?? ''}</td>
											<td style="width: 5%;">:</td>
											{#each laboratory_results as laboratory_result}
												{@const result = laboratory_result?.result}
												{#if laboratory_result.parameter_id === parameter.id}
													<td style="width: 20%;">
														{#if result?.toLowerCase() === 'positive' || result === '1/160' || result === '1/320' || result === '+' || result === '++' || result === '+++' || result === '++++'}
															<span style="color: #FF0000;">
																{result}
															</span>
														{:else if Number(result) >= Number(parameter?.mini) && Number(result) <= Number(parameter?.maxi)}
															<span>{result}</span>
														{:else if Number(result) < Number(parameter?.mini)}
															<span style="color: #0000FF;">{result} L</span>
														{:else if Number(result) > Number(parameter?.maxi)}
															<span style="color: #FF0000;">{result} H</span>
														{:else}
															<span style="color: #0000FF;">
																{result ?? ''}
															</span>
														{/if}
													</td>
												{/if}
											{/each}
											<td style="width: 15%;">
												<Athtml html={parameter.paraUnit?.unit ?? ''} />
											</td>
											<td style="width: 20%;">
												{parameter.mini === 0
													? ''
													: `( ${Number(parameter?.mini) % 1 === 0 ? parameter.mini : parameter.mini?.toFixed(2)}`}
												{parameter.sign ?? ''}
												{parameter.maxi === 0
													? ''
													: `${Number(parameter?.maxi) % 1 === 0 ? parameter.maxi : parameter.maxi?.toFixed(2)} )`}
											</td>
										</tr>
									{/if}
								{/each}
							</thead>
						</table>
					</div>
				{/each}

				{#if find_old_visit.imagerieRequest.length}
					<div class="border rounded border-1 p-2 mb-2">
						<span class="btn btn-success btn-sm mb-2 py-0">Imagerie</span>

						<table class="table table-sm">
							<thead>
								{#each find_old_visit.imagerieRequest as imagerie_request}
									<tr>
										<td style="width: 50%;">
											<div class="row">
												<div class="col-6">
													{#if imagerie_request.scan_by_id && imagerie_request.input_by_id}
														<button class="btn btn-link text-decoration-none btn-sm text-primary">
															{imagerie_request.product?.products ?? ''}
														</button>
													{:else}
														<button
															onclick={() => {
																if (imagerie_request.scan_by_id) {
																	is_scan = true;
																} else {
																	is_scan = false;
																}
																if (imagerie_request.input_by_id) {
																	is_input = true;
																} else {
																	is_input = false;
																}
																imagerie_id = imagerie_request.id;
															}}
															data-bs-toggle="modal"
															data-bs-target="#confirm_modal"
															class="btn btn-link text-danger btn-sm"
														>
															{imagerie_request.product?.products ?? ''}
														</button>
													{/if}
												</div>
												<div class="col">
													<fieldset
														disabled={!imagerie_request.input_by_id && !imagerie_request.scan_by_id}
													>
														{#if imagerie_request.status}
															{#if !imagerie_request.is_ob_form}
																<a
																	href="/imagerie/result/general?imagerie_request_id={imagerie_request.id}&group_id={imagerie_request
																		.product?.group_id}"
																	class="btn text-warning btn-link btn-sm"
																	>{locale.T('edit')}
																</a>
															{:else}
																<a
																	href="/imagerie/result/ob?imagerie_request_id={imagerie_request.id}"
																	class="btn text-warning btn-link btn-sm"
																	>{locale.T('edit')}
																</a>
															{/if}
														{:else if !imagerie_request.is_ob_form}
															<a
																href="/imagerie/result/general?imagerie_request_id={imagerie_request.id}&group_id={imagerie_request
																	.product?.group_id}"
																class="btn text-primary btn-link"
																>{locale.T('result')}
															</a>
														{:else}
															<a
																href="/imagerie/result/ob?imagerie_request_id={imagerie_request.id}"
																class="btn text-primary btn-link"
																>{locale.T('result')}
															</a>
														{/if}
													</fieldset>
												</div>
											</div>
										</td>
										<td style="width: 5%;">:</td>
										<td style="width: 45%;">
											<fieldset
												disabled={!imagerie_request.input_by_id && !imagerie_request.scan_by_id}
											>
												<div class="dropdown">
													<a
														class="btn btn-link btn-sm"
														href="#/"
														role="button"
														data-bs-toggle="dropdown"
														aria-expanded="false"
													>
														{locale.T('view')}
														{locale.T('result')}
														{#if imagerie_request.scan_by_id && imagerie_request.input_by_id}
															<span><i class="fa-solid fa-check-double"></i></span>
														{/if}
														{#if imagerie_request.scan_by_id && !imagerie_request.input_by_id}
															<span><i class="fa-solid fa-check"></i></span>
														{/if}
													</a>

													<ul class="dropdown-menu">
														<li>
															<a
																class="dropdown-item"
																target="_blank"
																href="/report/{imagerie_request.id}/imagerie?row=true"
																><i class="fa-regular fa-image"></i> {locale.T('up_down')}
															</a>
														</li>
														<li>
															<a
																class="dropdown-item"
																target="_blank"
																href="/report/{imagerie_request.id}/imagerie?row=false"
																><i class="fa-regular fa-file-image"></i> {locale.T('left_right')}
															</a>
														</li>
														<li>
															<a
																class="dropdown-item"
																target="_blank"
																href="/report/{imagerie_request.id}/imagerie"
																><i class="fa-solid fa-images"></i> {locale.T('ecg')}
															</a>
														</li>
													</ul>
												</div>
											</fieldset>
										</td>
									</tr>
								{/each}
							</thead>
						</table>
					</div>
				{/if}
			</div>
			<div class="col-lg-4">
				<h4 class="text-center">Treatment</h4>
				<CopyPrescription
					class="btn btn-warning btn-sm mb-2 py-0"
					data={find_old_visit?.presrciption}
				/>
				{#if find_old_visit.presrciption.length}
					<button class="btn btn-success btn-sm mb-2 py-0">Presrciption</button>
				{/if}

				{#each find_old_visit.presrciption as item, index}
					<div class="border rounded border-1 p-2 mb-2">
						<span class="fs-6 text-decoration-underline text-primary"
							>{index + 1}
							{item.product?.products ?? ''}
							{item.product?.generic_name ? `, (  ${item.product?.generic_name ?? ''} )` : ''}
						</span>,
						<span>
							ចំនួន {item.amount ?? ''}
							{item.product?.unit?.unit}, រយៈពេល {item.duration ?? ''}
						</span>
						<table class="table">
							<thead>
								<tr>
									<td style="width: 20%;">
										<span>{item.use ?? ''}</span>
									</td>
									<td style="width: 5%;">:</td>
									<td style="width: 55%;">
										<span class="badge text-bg-warning">
											{#if item.morning !== 0}
												Morning {item.morning}
											{/if}
										</span>
										<span class="badge text-bg-warning">
											{#if item.noon !== 0}
												Noon {item.noon}
											{/if}
										</span>
										<span class="badge text-bg-warning">
											{#if item.afternoon !== 0}
												Afternoon {item.afternoon}
											{/if}
										</span>
										<span class="badge text-bg-warning">
											{#if item.evening !== 0}
												Evening {item.evening}
											{/if}
										</span>
										<span class="badge text-bg-warning">
											{#if item.night !== 0}
												Night {item.night}
											{/if}
										</span>
									</td>
								</tr>
							</thead>
						</table>
					</div>
				{/each}
				{#if find_old_visit?.adviceTeaching?.description}
					<div
						class="alert alert-primary position-relative align-items-center d-flex py-2"
						role="alert"
					>
						<i class="fa-solid fa-circle-info me-2"></i>
						<div class="text-break">{find_old_visit?.adviceTeaching?.description ?? ''}</div>
						<span
							class="position-absolute top-0 start-50 translate-middle badge rounded-pill bg-primary text-break"
						>
							Advice Teaching:
							<span class="visually-hidden">unread messages</span>
						</span>
					</div>
				{/if}
				{#if find_old_visit?.vaccine.length}
					<div
						class="alert alert-primary d-flex align-items-center py-2 position-relative"
						role="alert"
					>
						<i class="fa-solid fa-eye-dropper me-2"></i>
						{#each find_old_visit?.vaccine || [] as item}
							<p>{item.product?.products}</p>
							<br />
						{/each}
						<span
							class="position-absolute top-0 start-50 translate-middle badge rounded-pill bg-primary"
						>
							Vannice:
							<span class="visually-hidden">unread messages</span>
						</span>
					</div>
				{/if}
				{#if find_old_visit?.appointment}
					<div class="alert alert-primary d-flex align-items-center py-2" role="alert">
						<div>
							<DDMMYYYYFormat date={find_old_visit?.appointment?.datetime} /> <br />
							<p class="text-break">{find_old_visit.appointment?.description ?? ''}</p>
						</div>
						<span
							class="position-absolute top-0 start-50 translate-middle badge rounded-pill bg-primary"
						>
							Appointment / follow up
							<span class="visually-hidden">unread messages</span>
						</span>
					</div>
				{/if}
			</div>
		</div>
	</div>
{/if}

<ConfirmModal action="/imagerie?/assign_inputer" id={imagerie_id}>
	<ul class="list-group">
		<li class="list-group-item text-primary-emphasis">
			<input
				disabled={is_scan}
				checked={is_scan}
				class="form-check-input"
				type="checkbox"
				name="is_scan"
				id="is_scan"
			/>
			&nbsp;
			<i class="fa-solid fa-camera-retro"></i>
			<label class="form-check-label" for="is_scan"> បញ្ចូលរូបភាពស្កេន</label>
		</li>
		<li class="list-group-item">
			<input
				checked={is_input}
				disabled={is_input}
				class="form-check-input"
				type="checkbox"
				name="is_input"
				id="is_input"
			/>
			&nbsp;
			<i class="fa-solid fa-file-pen"></i>
			<label class="form-check-label" for="is_input"> គ្រូពេទ្យពិនិត្យ</label>
		</li>
	</ul>
</ConfirmModal>
