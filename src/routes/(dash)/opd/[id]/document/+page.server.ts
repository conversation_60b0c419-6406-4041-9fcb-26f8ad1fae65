import { db } from '$lib/server/db';
import type { Actions, PageServerLoad } from './$types';
import { document, fields, uploads, visit, words } from '$lib/server/schemas';
import { YYYYMMDD_Format } from '$lib/server/utils';
import { and, eq } from 'drizzle-orm';
import { fail } from '@sveltejs/kit';
import { fileHandle } from '$lib/server/upload';
import { logError } from '$lib/server/utils/telegram';
export const load = (async ({ params, url }) => {
	const { id } = params;
	const title = url.searchParams.get('title') ?? '';
	const get_document = await db.query.document.findFirst({
		where: and(eq(document.visit_id, +id), eq(document.title, title)),
		with: {
			fields: true
		}
	});
	const get_documents = await db.query.document.findMany({
		where: eq(document.visit_id, +id),
		with: {
			fields: true
		}
	});

	const get_visit = await db.query.visit.findFirst({
		where: eq(visit.id, +id),
		with: {
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			department: true
		}
	});
	const get_words = await db.query.words.findMany({
		where: eq(words.category, 'patient')
	});
	const get_clinich_info = await db.query.clinicinfo.findFirst({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'clinicinfo'), eq(uploads.mimeType, 'logo0'))
	});
	const get_upload_doc = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'document'), eq(uploads.related_id, get_document?.id || 0))
	});
	const get_document_setting = await db.query.documentSetting.findFirst({});

	return {
		get_document: {
			...get_document,
			uploads: get_upload_doc
		},
		get_document_setting: get_document_setting,
		get_visit,
		get_words,
		get_clinich_info,
		get_upload,
		get_documents
	};
}) satisfies PageServerLoad;
export const actions: Actions = {
	create_document: async ({ request, params }) => {
		const body = await request.formData();
		const { id } = params;
		const { title } = Object.fromEntries(body) as Record<string, string>;
		const get_document = await db.query.document.findMany({
			where: eq(document.visit_id, +id),
			with: {
				fields: true
			}
		});
		const current_document = get_document.find((e) => e.title === title);
		if (!title) return fail(400, { err_title: true });
		if (get_document.some((e) => e.title === title)) {
			// remove box
			for (const field of current_document?.fields || []) {
				if (field.name === 'box') {
					await db.delete(fields).where(eq(fields.id, field.id));
				}
			}
			for (const [key, value] of Array.from(body)) {
				// recreate box
				if (key === 'box') {
					await db.insert(fields).values({
						name: key,
						result: value.toString(),
						document_id: current_document?.id
					});
				} else {
					const found_field = current_document?.fields.find((e) => e.name === key);
					if (found_field) {
						await db
							.update(fields)
							.set({
								result: value.toString()
							})
							.where(eq(fields.id, found_field.id));
					}
				}
			}
		} else {
			const create_document: { id: number }[] = await db
				.insert(document)
				.values({
					title: title,
					visit_id: +id,
					datetime: YYYYMMDD_Format.datetime(new Date())
				})
				.$returningId();
			for (const [key, value] of Array.from(body)) {
				await db.insert(fields).values({
					name: key,
					result: value.toString(),
					document_id: create_document[0]?.id
				});
			}
		}
	},
	delete_document: async ({ request, url }) => {
		const body = await request.formData();
		const { id, file_name } = Object.fromEntries(body) as Record<string, string>;
		if (!id) return fail(400, { errId: true });
		await db
			.delete(document)
			.where(eq(document.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		if (file_name) await fileHandle.drop(file_name);
	},
	upload_doc: async ({ request }) => {
		const body = await request.formData();
		await fileHandle.auto(body);
	}
};
