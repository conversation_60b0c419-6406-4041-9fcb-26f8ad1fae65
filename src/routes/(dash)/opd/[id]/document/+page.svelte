<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import ServiceContract from '$lib/coms-document/ServiceContract.svelte';
	import PatientTranswer from '$lib/coms-document/PatientTranswer.svelte';
	import BirthRecognize from '$lib/coms-document/BirthRecognize.svelte';
	import ResultChecking from '$lib/coms-document/ResultChecking.svelte';
	import { dobToAge } from '$lib/helper';
	import AcceptLeaving from '$lib/coms-document/AcceptLeaving.svelte';
	import { page } from '$app/state';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import CropImage from '$lib/coms-form/CropImage.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import SetBack from '$lib/coms/SetBack.svelte';
	let { data }: { data: PageServerData } = $props();
	let {
		get_documents,
		get_visit,
		get_words,
		get_clinich_info,
		get_upload,
		get_document,
		get_document_setting
	} = $derived(data);
	let loading = $state(false);
	let title = $derived(page.url.searchParams.get('title') ?? '');
</script>

<DeleteModal action="?/delete_document" id={get_document?.id}>
	<input type="hidden" name="file_name" value={get_document?.uploads?.filename} />
</DeleteModal>
<div class="card bg-light">
	<div class="card-header fs-5">
		{locale.T('documents')}
	</div>
	<div class="card-body">
		<div class="row g-0">
			<div class="col-md-3">
				<div class="btn-group w-100 mb-2">
					<a
						class:active={title === 'accept_leaving'}
						href="/opd/{get_visit?.id}/document?title=accept_leaving"
						type="button"
						data-sveltekit-noscroll
						class="btn btn-outline-primary text-start"
						><i class="fa-regular fa-file-word"></i> លិខិតអនុញ្ញាតចេញពិពេទ្យ</a
					>
					{#if get_documents.find((e) => e.title === 'accept_leaving')}
						<button style="max-width: 50px;" aria-label="upload-btn-doc-1" class="btn btn-primary"
							><i class="fa-regular fa-circle-check"></i></button
						>
					{/if}
				</div>
				<div class="btn-group w-100 mb-2">
					<a
						class:active={title === 'service_contract'}
						href="/opd/{get_visit?.id}/document?title=service_contract"
						type="button"
						data-sveltekit-noscroll
						class="btn btn-outline-primary text-start"
						><i class="fa-regular fa-file-word"></i> កិច្ចសន្យាទទួលសេវា</a
					>
					{#if get_documents.find((e) => e.title === 'service_contract')}
						<button style="max-width: 50px;" aria-label="upload-btn-doc-1" class="btn btn-primary"
							><i class="fa-regular fa-circle-check"></i></button
						>
					{/if}
				</div>

				<div class="btn-group w-100 mb-2">
					<a
						class:active={title === 'patient_transfer'}
						href="/opd/{get_visit?.id}/document?title=patient_transfer"
						type="button"
						data-sveltekit-noscroll
						class="btn btn-outline-primary text-start"
						><i class="fa-regular fa-file-word"></i> លិខិតបញ្ជូនអ្នកជំងឺ</a
					>
					{#if get_documents.find((e) => e.title === 'patient_transfer')}
						<button style="max-width: 50px;" aria-label="upload-btn-doc-1" class="btn btn-primary"
							><i class="fa-regular fa-circle-check"></i></button
						>
					{/if}
				</div>

				<div class="btn-group w-100 mb-2">
					<a
						class:active={title === 'result_checking'}
						href="/opd/{get_visit?.id}/document?title=result_checking"
						type="button"
						data-sveltekit-noscroll
						class="btn btn-outline-primary text-start"
						><i class="fa-regular fa-file-word"></i> លទ្ធផលពីនិត្យសុខភាព</a
					>
					{#if get_documents.find((e) => e.title === 'result_checking')}
						<button style="max-width: 50px;" aria-label="upload-btn-doc-1" class="btn btn-primary"
							><i class="fa-regular fa-circle-check"></i></button
						>
					{/if}
				</div>
			</div>
			<div class="col-md-9">
				{#if title}
					<div style="max-width: 870px;margin:auto;max-height: 3508px;">
						<Form reset={false} bind:loading action="?/create_document" method="post">
							<div style="zoom: 80%;" class="shadow p-3 mb-5 bg-body">
								<div id="print_document">
									{#if title === 'accept_leaving'}
										<AcceptLeaving
											p_name={get_visit?.patient?.name_khmer
												?.concat(`(${get_visit?.patient?.name_latin}) `)
												.concat(
													`ភេទ ${get_visit?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
												)
												.concat(
													`អាយុ ${dobToAge(get_visit?.patient?.dob ?? '', get_visit?.date_checkup ?? '')}`
												)
												.toLowerCase()
												.replace('month', 'ខែ')
												.toLowerCase()
												.replace('year', 'ឆ្នាំ')
												.toLowerCase()
												.replace('day', 'ថ្ងៃ') ?? ''}
											p_nation={get_visit?.patient?.nation ?? ''}
											address={{
												village: get_visit?.patient?.village,
												commune: get_visit?.patient?.commune,
												district: get_visit?.patient?.district,
												provice: get_visit?.patient?.provice
											}}
											{get_document_setting}
											fields={get_document?.fields ?? []}
											p_date_checkup={get_visit?.date_checkup ?? ''}
											p_date_checkout={get_visit?.date_checkout ?? ''}
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
										/>
									{/if}
									{#if title === 'service_contract'}
										<ServiceContract
											{get_document_setting}
											fields={get_document?.fields ?? []}
											address={{
												village: get_visit?.patient?.village,
												commune: get_visit?.patient?.commune,
												district: get_visit?.patient?.district,
												provice: get_visit?.patient?.provice
											}}
											occupation_list={get_words
												.filter((e) => e.type === 'occupation')
												.map((e) => e.text)}
											p_name={get_visit?.patient?.name_khmer
												?.concat(`(${get_visit?.patient?.name_latin}) `)
												.concat(
													`ភេទ ${get_visit?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
												)
												.concat(
													`អាយុ ${dobToAge(get_visit?.patient?.dob ?? '', get_visit?.date_checkup ?? '')}`
												)
												.toLowerCase()
												.replace('month', 'ខែ')
												.toLowerCase()
												.replace('year', 'ឆ្នាំ')
												.toLowerCase()
												.replace('day', 'ថ្ងៃ') ?? ''}
											p_nation={get_visit?.patient?.nation ?? ''}
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
										/>
									{/if}

									{#if title === 'patient_transfer'}
										<PatientTranswer
											{get_document_setting}
											fields={get_document?.fields ?? []}
											p_address={{
												village: get_visit?.patient?.village,
												commune: get_visit?.patient?.commune,
												district: get_visit?.patient?.district,
												provice: get_visit?.patient?.provice
											}}
											p_name={get_visit?.patient?.name_khmer
												?.concat(`(${get_visit?.patient?.name_latin}) `)
												.concat(
													`ភេទ ${get_visit?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
												)
												.concat(
													`អាយុ ${dobToAge(get_visit?.patient?.dob ?? '', get_visit?.date_checkup ?? '')}`
												)
												.toLowerCase()
												.replace('month', 'ខែ')
												.toLowerCase()
												.replace('year', 'ឆ្នាំ')
												.toLowerCase()
												.replace('day', 'ថ្ងៃ') ?? ''}
											p_nation={get_visit?.patient?.nation ?? ''}
											p_date_checkup={get_visit?.date_checkup ?? ''}
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
										/>
									{/if}
									{#if title === 'birth_recognize'}
										<BirthRecognize
											{get_document_setting}
											p_address={{
												village: get_visit?.patient?.village,
												commune: get_visit?.patient?.commune,
												district: get_visit?.patient?.district,
												provice: get_visit?.patient?.provice
											}}
											p_name={get_visit?.patient?.name_khmer
												?.concat(`(${get_visit?.patient?.name_latin}) `)
												.concat(
													`ភេទ ${get_visit?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
												)
												.concat(
													`អាយុ ${dobToAge(get_visit?.patient?.dob ?? '', get_visit?.date_checkup ?? '')}`
												)
												.toLowerCase()
												.replace('month', 'ខែ')
												.toLowerCase()
												.replace('year', 'ឆ្នាំ')
												.toLowerCase()
												.replace('day', 'ថ្ងៃ') ?? ''}
											p_occupation={get_visit?.patient?.occupation ?? ''}
											fields={get_document?.fields ?? []}
											occupation_list={get_words
												.filter((e) => e.type === 'occupation')
												.map((e) => e.text)}
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
										/>
									{/if}
									{#if title === 'result_checking'}
										<ResultChecking
											{get_document_setting}
											fields={get_document?.fields ?? []}
											p_address={{
												village: get_visit?.patient?.village,
												commune: get_visit?.patient?.commune,
												district: get_visit?.patient?.district,
												provice: get_visit?.patient?.provice
											}}
											p_name_khmer={get_visit?.patient?.name_khmer ?? ''}
											p_name_latin={get_visit?.patient?.name_latin ?? ''}
											p_occupation={get_visit?.patient?.occupation ?? ''}
											p_id_card_passport={get_visit?.patient?.id_cart_passport ?? ''}
											p_dob={get_visit?.patient?.dob ?? ''}
											p_phone={get_visit?.patient?.telephone ?? ''}
											p_nation={get_visit?.patient?.nation ?? ''}
											p_gender={get_visit?.patient?.gender
												.toLowerCase()
												.replace('male', 'ប្រុស')
												.replace('female', 'ស្រី') ?? ''}
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
										/>
									{/if}
								</div>
							</div>
							<div class="btn-group w-100">
								<button
									disabled={!get_document?.id}
									type="button"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									class=" btn btn-lg btn-danger col-4"
								>
									<i class="fa-solid fa-trash-can"></i> {locale.T('delete_')}</button
								>
								<button type="submit" class="btn btn-lg btn-primary col-4">
									<i class="fa-regular fa-floppy-disk"></i>
									{locale.T('save')}
								</button>
								{#if get_document?.fields}
									<SetBack
										href="/print/{get_visit?.id}/document?title={title}&type=opd"
										class="btn btn-lg btn-info col-4"
									>
										<i class="fa-solid fa-print"></i>
										{locale.T('print')}
									</SetBack>
								{/if}
							</div>
						</Form>
						<br />
						{#if get_document.title === title}
							<div class="alert alert-warning py-2">
								សូមបញ្ចូលឯកសារដែលបានបេាះពុម្ពហើយ!
								{#if get_document?.uploads?.filename}
									<a
										href={get_document?.uploads?.filename}
										class="btn btn-sm btn-primary"
										target="_blank"
										rel="noopener noreferrer"
										download
									>
										<i class="fa-solid fa-download"></i>
										{get_document?.uploads?.filename}
									</a>
								{/if}
							</div>

							<Form
								class="card bg-light "
								action="?/upload_doc"
								method="post"
								enctype="multipart/form-data"
							>
								<div class="card-body">
									<CropImage
										default_image={get_document?.uploads?.filename ?? ''}
										name="file"
										related_id={get_document.id}
										related_type_="document"
										aspect_ratio
									/>
									<br />
									<SubmitButton />
								</div>
							</Form>
						{/if}
					</div>
				{/if}
			</div>
		</div>
	</div>
</div>
