<script lang="ts">
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import type { PageServerData } from './$types';
	import CurrencyInput from '$lib/coms-form/CurrencyInput.svelte';
	import TimeFormat from '$lib/coms/TimeFormat.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import FieldsetBilling from '$lib/coms/FieldsetBilling.svelte';
	import ConfirmSubmit from '$lib/coms-form/ConfirmSubmit.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let service_id = $state(0);
	let loading = $state(false);
	let { get_product_as_service, get_visit, get_currency, charge_on_service } = $derived(data);
	let find_service = $derived(get_visit?.service.find((e) => e.id === service_id));
	let billing = $derived(get_visit?.billing);
</script>

<DeleteModal action="?/delete_service" id={find_service?.id || find_service?.id} />
<div class="card mb-2 bg-body-tertiary">
	<div class="card-header fs-5">
		# {locale.T('service')}
	</div>

	<table class="table table-light table-bordered mb-0">
		<thead>
			<tr>
				<th class="text-center">{locale.T('n')}</th>
				<th class="text-center">{locale.T('department')}</th>
				<th class="text-center">{locale.T('price')}</th>
			</tr>
		</thead>
		<tbody>
			{#each charge_on_service?.productOrder || [] as item, index}
				<tr>
					<td class="text-center">{index + 1}</td>
					<td>{item?.product?.products}</td>
					<td>
						<FieldsetBilling status={billing?.status}>
							<div role="group" class="btn-group btn-group-sm">
								<ConfirmSubmit
									header={locale.T('input_price')}
									class="btn btn-success"
									name="<i class=&quot;fa-solid fa-comments-dollar&quot;></i>"
									action="?/discount_product_order"
								>
									<ul class="list-group pb-2">
										<li class="list-group-item text-danger">
											<i class="fa-solid fa-triangle-exclamation"></i>
											{locale.T('please_verify_before_sending')}
										</li>
									</ul>
									<ul class="list-group pb-2">
										<li class="list-group-item">
											<i class="fa-solid fa-comments-dollar"></i>
											{item?.product?.products}
										</li>
									</ul>
									<CurrencyInput
										symbol={get_currency?.currency}
										amount={item?.total}
										name="price"
									/>
									<input type="hidden" name="product_order_id" value={item?.id} />
									<input type="hidden" name="charge_id" value={item?.id} />
									<input type="hidden" name="qty" value="1" />
									<input type="hidden" name="disc" value="" />
								</ConfirmSubmit>
								<Currency
									class="btn btn-warning"
									amount={item?.total}
									symbol={get_currency?.currency}
								/>
							</div>
						</FieldsetBilling>
					</td>
				</tr>
			{/each}
		</tbody>
	</table>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row">
			<div class="col fs-5">Operation Protocol</div>
			<div class="col-auto">
				<button
					aria-label="new_service"
					type="button"
					class="btn btn-success btn-sm"
					data-bs-toggle="modal"
					data-bs-target="#create_service_operation"
					><i class="fa-solid fa-square-plus"></i>
				</button>
			</div>
		</div>
	</div>
	<div class="card-body table-responsive p-0">
		{#each get_visit?.service || [] as item, index (item.id)}
			<table class="table table-bordered text-nowrap pb-0 mb-0 table-light">
				<thead class="table-active">
					<tr>
						<th class="text-center" style="width: 5%;">N</th>
						<th style="width: 50%;">Service Item</th>
						<th colspan="3" style="width: 15%;">Operative Protocol</th>
					</tr>
				</thead>
				<tbody class="table-sm table-light">
					<tr>
						<td class="text-center">{index + 1}</td>
						<td> {item.product?.products ?? ''}</td>
						<td
							><button
								onclick={() => {
									service_id = 0;
									service_id = item.id;
								}}
								type="button"
								class="btn btn-primary btn-sm"
								data-bs-toggle="modal"
								data-bs-target="#create_protocol"
								><i class="fa-solid fa-square-plus"></i>
								Protocol
							</button></td
						>
						<td>
							<div>
								<a
									target="_blank"
									href="/print/{item?.id}/service"
									aria-label="print_service"
									type="button"
									class="btn btn-success px-2 btn-sm"
									><i class="fa-solid fa-print"></i>
								</a>
								<button
									aria-label="deletemodal"
									onclick={() => {
										service_id = 0;
										service_id = item.id;
									}}
									type="button"
									class="btn btn-danger btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-trash-can"></i>
								</button>
							</div>
						</td>
					</tr>
					<tr>
						<td colspan="5" class="p-0 m-0">
							<table class="table p-0 m-0 table-light">
								<tbody class="">
									<tr>
										<td style="width: 12.5%;">Surgeon</td>
										<td>:</td>
										<td style="width: 12.5%;"> {item.operationProtocol?.surgeon ?? ''} </td>

										<td style="width: 12.5%;">Scrub Nurse</td>
										<td>:</td>
										<td style="width: 12.5%;"> {item.operationProtocol?.scrub_nurse ?? ''} </td>

										<td style="width: 12.5%;">Start Time</td>
										<td>:</td>
										<td style="width: 12.5%;">
											<TimeFormat time={item.operationProtocol?.start_time ?? ''} />
										</td>

										<td style="width: 12.5%;">Type Anesthesia</td>
										<td>:</td>
										<td style="width: 12.5%;">
											{item.operationProtocol?.type_anesthesia ?? ''}
										</td>
									</tr>
									<tr>
										<td style="width: 12.5%;">Assistant Surgeon</td>
										<td>:</td>
										<td style="width: 12.5%;">
											{item.operationProtocol?.assistant_surgeon ?? ''}
										</td>

										<td style="width: 12.5%;">Circulation Nurse block</td>
										<td>:</td>
										<td style="width: 12.5%;">
											{item.operationProtocol?.cirulating_nurse_block ?? ''}
										</td>
										<td style="width: 12.5%;">Finish Time</td>
										<td>:</td>
										<td style="width: 12.5%;">
											<TimeFormat time={item.operationProtocol?.finish_time ?? ''} />
										</td>

										<td style="width: 12.5%;">Opertive Technique</td>
										<td>:</td>
										<td style="width: 12.5%;">
											<div class="text-break">
												{item.operationProtocol?.opertive_technique ?? ''}
											</div>
										</td>
									</tr>
									<tr>
										<td style="width: 12.5%;">Anesthetist</td>
										<td>:</td>
										<td style="width: 12.5%;"> {item.operationProtocol?.anesthetist ?? ''} </td>

										<td style="width: 12.5%;">Midwife</td>
										<td>:</td>
										<td style="width: 12.5%;"> {item.operationProtocol?.midwife ?? ''} </td>

										<td style="width: 12.5%;">Pre Diagnosis</td>
										<td>:</td>
										<td style="width: 12.5%;">
											{item.operationProtocol?.pre_diagnosis ?? ''}
										</td>

										<td style="width: 12.5%;">Blood Less</td>
										<td>:</td>
										<td style="width: 12.5%;"> {item.operationProtocol?.blood_less ?? ''} </td>
									</tr>
									<tr>
										<td style="width: 12.5%;">Assistant Anesthetist</td>
										<td>:</td>
										<td style="width: 12.5%;">
											{item.operationProtocol?.assistant_anesthetist ?? ''}
										</td>

										<td style="width: 12.5%;">Dates</td>
										<td>:</td>
										<td style="width: 12.5%;">
											<DDMMYYYYFormat date={item.operationProtocol?.date ?? ''} style="date" />
										</td>
										<td style="width: 12.5%;">Post Diagnosis</td>
										<td>:</td>
										<td style="width: 12.5%;">
											{item.operationProtocol?.post_diagnosis ?? ''}
										</td>
										<td style="width: 12.5%;">Notes</td>
										<td>:</td>
										<td style="width: 12.5%;">
											<div class="text-break">
												{item.operationProtocol?.notes ?? ''}
											</div>
										</td>
									</tr>
								</tbody>
							</table>
						</td>
					</tr>
				</tbody>
			</table>
		{/each}
	</div>
</div>

<!-- @_Modal OperationProtocol -->
<div class="modal fade" id="create_protocol" data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-scrollabl modal-xl">
		<Form
			enctype="multipart/form-data"
			action="?/create_protocol"
			method="post"
			class="modal-content"
			bind:loading
			fnSuccess={() => {
				service_id = 0;
				document.getElementById('close_create_protocol')?.click();
			}}
		>
			<div class="modal-header">
				<h4 class="modal-title">Operation Protocol</h4>
				<button
					id="close_create_protocol"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class=" card-body pt-0">
					<div class="row pb-3">
						<div class="col-12">
							<div class="row mb-3">
								<div class="col-sm-6">
									<div>
										<label for="surgeon">Surgeon</label>
										<input
											value={find_service?.operationProtocol?.surgeon ?? ''}
											id="surgeon"
											class="form-control"
											type="text"
											name="surgeon"
										/>
									</div>
								</div>
								<div class="col-sm-6">
									<div>
										<label for="assistant_surgeon">Assistant Surgeon</label>
										<input
											value={find_service?.operationProtocol?.assistant_surgeon ?? ''}
											id="assistant_surgeon"
											class="form-control"
											type="text"
											name="assistant_surgeon"
										/>
									</div>
								</div>
							</div>
							<div class="row mb-3">
								<div class="col-sm-6">
									<div>
										<label for="anesthetist">Anesthetist</label>
										<input
											value={find_service?.operationProtocol?.anesthetist ?? ''}
											id="anesthetist"
											class="form-control"
											type="text"
											name="anesthetist"
										/>
									</div>
								</div>
								<div class="col-sm-6">
									<div>
										<label for="assistant_anesthetist">Assistant Anesthetist</label>
										<input
											value={find_service?.operationProtocol?.assistant_anesthetist ?? ''}
											id="assistant_anesthetist"
											class="form-control"
											type="text"
											name="assistant_anesthetist"
										/>
									</div>
								</div>
							</div>
							<div class="row mb-3">
								<div class="col-sm-6">
									<div>
										<label for="scrub_nurse">Scrub Nurse</label>
										<input
											value={find_service?.operationProtocol?.scrub_nurse ?? ''}
											id="scrub_nurse"
											class="form-control"
											type="text"
											name="scrub_nurse"
										/>
									</div>
								</div>
								<div class="col-sm-6">
									<div>
										<label for="cirulating_nurse_block">Circulation / Nurse block</label>
										<input
											value={find_service?.operationProtocol?.cirulating_nurse_block ?? ''}
											id="cirulating_nurse_block"
											class="form-control"
											type="text"
											name="cirulating_nurse_block"
										/>
									</div>
								</div>
							</div>
							<div>
								<label for="midwife">Midwife</label>
								<input
									value={find_service?.operationProtocol?.midwife ?? ''}
									id="midwife"
									class="form-control"
									type="text"
									name="midwife"
								/>
							</div>
						</div>
					</div>

					<hr />
					<div class="row">
						<div class="col-12">
							<div class="row mb-3">
								<div class="col-sm-4">
									<div>
										<label for="date">Dates</label>
										<input
											value={find_service?.operationProtocol?.date ?? ''}
											id="date"
											class="form-control"
											type="date"
											name="date"
										/>
									</div>
								</div>
								<div class="col-sm-4">
									<div>
										<label for="start_time">Start Time</label>
										<input
											value={find_service?.operationProtocol?.start_time?.substring(0, 5) ?? ''}
											id="start_time"
											class="form-control"
											type="time"
											name="start_time"
										/>
									</div>
								</div>
								<div class="col-sm-4">
									<div>
										<label for="finish_time">Finish Time</label>
										<input
											value={find_service?.operationProtocol?.finish_time?.substring(0, 5) ?? ''}
											id="finish_time"
											class="form-control"
											type="time"
											name="finish_time"
										/>
									</div>
								</div>
							</div>
							<div class="row mb-3">
								<div class="col-sm-4">
									<div>
										<label for="pre_diagnosis">Pre-Diagnosis</label>
										<input
											value={find_service?.operationProtocol?.pre_diagnosis ?? ''}
											id="pre_diagnosis"
											class="form-control"
											type="text"
											name="pre_diagnosis"
										/>
									</div>
								</div>
								<div class="col-sm-4">
									<div>
										<label for="post_diagnosis">Post Diagnosis</label>
										<input
											value={find_service?.operationProtocol?.post_diagnosis ?? ''}
											id="post_diagnosis"
											class="form-control"
											type="text"
											name="post_diagnosis"
										/>
									</div>
								</div>
								<div class="col-sm-4">
									<div>
										<label for="type_anesthesia">Type Anesthesia</label>
										<input
											value={find_service?.operationProtocol?.type_anesthesia ?? ''}
											id="type_anesthesia"
											class="form-control"
											type="text"
											name="type_anesthesia"
										/>
									</div>
								</div>
							</div>
						</div>
					</div>
					<hr />
					<div class="row">
						<div class="col-12 mb-3">
							<div>
								<label for="opertive_technique">Opertive Technique</label>
								<textarea
									value={find_service?.operationProtocol?.opertive_technique ?? ''}
									rows="4"
									class="form-control"
									name="opertive_technique"
									id="opertive_technique"
								></textarea>
							</div>
						</div>
						<div class="col-12 mb-3">
							<div>
								<label for="blood_less">Blood Less</label>
								<input
									value={find_service?.operationProtocol?.blood_less ?? ''}
									id="blood_less"
									class="form-control"
									type="text"
									name="blood_less"
								/>
							</div>
						</div>
						<div class="col-12 mb-3">
							<div>
								<label for="notes">Notes</label>
								<textarea
									value={find_service?.operationProtocol?.notes ?? ''}
									rows="4"
									class="form-control"
									name="notes"
									id="notes"
								></textarea>
							</div>
						</div>
					</div>
				</div>
			</div>
			<input type="hidden" name="service_id" value={find_service?.id} />
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>
<!-- @_Modal  Service -->
<div class="modal fade" id="create_service_operation" data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-scrollabl modal-xl">
		<fieldset>
			<Form
				action="?/create_service_operation"
				method="post"
				bind:loading
				fnSuccess={() => document.getElementById('close_create_service_operation')?.click()}
				class="modal-content"
			>
				<div class="modal-header">
					<h4 class="modal-title">Service</h4>
					<button
						id="close_create_service_operation"
						type="button"
						class="btn-close"
						data-bs-dismiss="modal"
						aria-label="Close"
					>
					</button>
				</div>
				<div class="modal-body">
					<div class="card-body pt-0">
						<div class="row">
							<div class="col-12">
								<div>
									<label for="product_id">Service Item</label>
									<SelectParam
										name="product_id"
										items={get_product_as_service.map((e) => ({ id: e.id, name: e.products }))}
									/>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer justify-content-end">
					<SubmitButton {loading} />
				</div>
			</Form>
		</fieldset>
	</div>
</div>
