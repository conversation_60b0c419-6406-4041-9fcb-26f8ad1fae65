import { db } from '$lib/server/db';
import { operationProtocol, product, category, service, visit } from '$lib/server/schemas';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { eq, like } from 'drizzle-orm';
import { logError } from '$lib/server/utils/telegram';
import {
	checkout,
	createProductOrder,
	deleteProductOrder,
	setChargePrice,
	updateProductOrder
} from '$lib/server/models';

export const load = (async ({ params }) => {
	const visit_id = params.id;
	const get_visit = await db.query.visit.findFirst({
		with: {
			service: {
				with: {
					product: true,
					operationProtocol: true
				}
			},
			billing: {
				with: {
					charge: {
						with: {
							productOrder: {
								with: {
									product: true
								}
							}
						}
					}
				}
			}
		},
		where: eq(visit.id, +visit_id)
	});
	const get_category = await db.query.category.findFirst({
		where: like(category.name, 'Service')
	});
	const get_currency = await db.query.currency.findFirst({});

	const get_product_as_service = await db.query.product.findMany({
		where: eq(product.category_id, get_category?.id || 0)
	});
	const charge_on_service = get_visit?.billing?.charge.find((e) => e.charge_on === 'service');
	return {
		get_product_as_service,
		get_currency: get_currency,
		get_visit: get_visit,
		charge_on_service: charge_on_service
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_service_operation: async ({ request, params, url }) => {
		const { id } = params;
		const body = await request.formData();
		const visti_id = parseInt(params.id);
		const { product_id } = Object.fromEntries(body) as Record<string, string>;
		if (!product_id) return fail(400, { errId: true });
		const get_product = await db.query.product.findFirst({ where: eq(product.id, +product_id) });
		const get_visit = await db.query.visit.findFirst({
			where: eq(visit.id, +id),
			with: {
				imagerieRequest: {
					with: {
						product: true
					}
				},
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				},
				service: true
			}
		});
		if (get_visit?.service.find((e) => e.product_id === +product_id)) {
			return fail(400, { errId: true });
		}
		const charge_on_service = get_visit?.billing?.charge.find((e) => e.charge_on === 'service');
		await db.insert(service).values({
			product_id: get_product?.id,
			visit_id: visti_id
		});
		await createProductOrder({
			charge_id: charge_on_service!.id,
			product_id: get_product!.id,
			price: get_product?.price ?? 0,
			qty: 1,
			body: body,
			url: url
		});
	},
	delete_service: async ({ request, params, url }) => {
		const { id: visit_id } = params;
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (!id) return fail(400, { errId: true });
		const get_visit = await db.query.visit.findFirst({
			where: eq(visit.id, +visit_id),
			with: {
				imagerieRequest: {
					with: {
						product: true
					}
				},
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				}
			}
		});
		const get_services = await db.query.service.findFirst({
			where: eq(service.id, +id),
			with: {
				product: true
			}
		});
		const charge_on_service = get_visit?.billing?.charge.find((e) => e.charge_on === 'service');
		const get_product_order = charge_on_service?.productOrder.find(
			(e) => e.product_id === get_services?.product_id
		);
		if (get_product_order) {
			await deleteProductOrder(get_product_order.id);
		}
		await db
			.delete(service)
			.where(eq(service.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	create_protocol: async ({ request, url }) => {
		const body = await request.formData();
		const {
			surgeon,
			assistant_surgeon,
			anesthetist,
			assistant_anesthetist,
			scrub_nurse,
			cirulating_nurse_block,
			midwife,
			date,
			start_time,
			finish_time,
			pre_diagnosis,
			post_diagnosis,
			type_anesthesia,
			opertive_technique,
			blood_less,
			notes,
			service_id
		} = Object.fromEntries(body) as Record<string, string>;
		const get_operation_protocol = await db.query.operationProtocol.findFirst({
			where: eq(operationProtocol.service_id, +service_id)
		});

		if (get_operation_protocol) {
			await db
				.update(operationProtocol)
				.set({
					anesthetist: anesthetist,
					assistant_anesthetist: assistant_anesthetist,
					assistant_surgeon: assistant_surgeon,
					blood_less: blood_less,
					cirulating_nurse_block: cirulating_nurse_block,
					date: date,
					finish_time: finish_time,
					midwife: midwife,
					notes: notes,
					opertive_technique: opertive_technique,
					post_diagnosis: post_diagnosis,
					pre_diagnosis: pre_diagnosis,
					scrub_nurse: scrub_nurse,
					start_time: start_time,
					surgeon: surgeon,
					type_anesthesia: type_anesthesia
				})
				.where(eq(operationProtocol.service_id, +service_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		} else {
			await db
				.insert(operationProtocol)
				.values({
					anesthetist: anesthetist,
					assistant_anesthetist: assistant_anesthetist,
					assistant_surgeon: assistant_surgeon,
					blood_less: blood_less,
					cirulating_nurse_block: cirulating_nurse_block,
					date: date,
					finish_time: finish_time,
					midwife: midwife,
					notes: notes,
					opertive_technique: opertive_technique,
					post_diagnosis: post_diagnosis,
					pre_diagnosis: pre_diagnosis,
					scrub_nurse: scrub_nurse,
					service_id: +service_id,
					start_time: start_time,
					surgeon: surgeon,
					type_anesthesia: type_anesthesia
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	update_total_service: async ({ request, params }) => {
		const { id } = params;
		const body = await request.formData();
		const { total_service } = Object.fromEntries(body) as Record<string, string>;
		const get_visit = await db.query.visit.findFirst({
			where: eq(visit.id, +id),
			with: {
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				}
			}
		});
		const charge_on_service = get_visit?.billing?.charge.find((e) => e.charge_on === 'service');
		if (charge_on_service) {
			await setChargePrice(charge_on_service.id, +total_service);
		}
	},
	set_price_service: async ({ request, params, url }) => {
		const { id } = params;
		const body = await request.formData();
		const { product_id, price } = Object.fromEntries(body) as Record<string, string>;
		const get_visit = await db.query.visit.findFirst({
			where: eq(visit.id, +id),
			with: {
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				}
			}
		});
		const charge_on_service = get_visit?.billing?.charge.find((e) => e.charge_on === 'service');
		const find_product_order = charge_on_service?.productOrder.find(
			(e) => e.product_id === +product_id
		);
		if (find_product_order) {
			await updateProductOrder({
				disc: '',
				price: +price,
				product_order_id: find_product_order.id,
				qty: 1,
				body: body,
				url: url
			});
		}
	},
	discount_product_order: async (e) => {
		await checkout.discount_product_order(e);
	}
};
