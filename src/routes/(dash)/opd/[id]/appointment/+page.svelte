<script lang="ts">
	import Words from '$lib/coms-cu/Words.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import SelectWords from '$lib/coms/SelectWords.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { YYYYMMDD_Format } from '$lib/helper';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_appointment, get_words } = $derived(data);
	let datetime = $state(
		data.get_appointment?.datetime ? YYYYMMDD_Format.datetime(data.get_appointment?.datetime) : ''
	);
	let loading = $state(false);
	let description = $derived(get_appointment?.description ?? '');
</script>

<Words name="Description" words={get_words} modal_name_type="description" category="appointment" />
<DeleteModal action="?/delete_appointment" id={get_appointment?.id} />
<div class="card bg-light">
	<div class="card-header fs-4">
		<span># {locale.T('appintment')}</span>
	</div>
	{#if get_appointment?.datetime_come && get_appointment?.status}
		<div class="p-2">
			<div class="alert alert-primary py-2">
				<i class="fa-solid fa-circle-check"></i> អ្នកជំងឺបានមកតាមការណាត់ជួប #<DDMMYYYYFormat
					date={get_appointment.datetime_come}
				/>
			</div>
		</div>
	{/if}
	{#if !get_appointment?.datetime_come && get_appointment?.status}
		<div class="p-2">
			<div class="alert alert-danger py-2">
				<i class="fa-solid fa-circle-check"></i> អ្នកជំងឺមិនបានមកតាមការណាត់ជួប
			</div>
		</div>
	{/if}
	<fieldset disabled={get_appointment?.status}>
		<Form bind:loading action="?/create_appointment" method="post">
			<div class="card-body">
				<div class="row">
					<div class="col-sm-4 p-2">
						<label for="datetime" class="form-label">{locale.T('date')}</label>
						<input
							bind:value={datetime}
							id="datetime"
							required
							name="datetime"
							type="datetime-local"
							class="form-control"
						/>
						<br />
						<button
							data-bs-toggle="modal"
							data-bs-target="#description"
							type="button"
							class="btn btn-outline-primary btn-sm mb-2"
							>{locale.T('description')}
						</button>
						<SelectWords
							name="description"
							rows="4"
							type="textarea"
							value={description}
							words={get_words.map((e) => e.text)}
						/>
						<div class="float-end pt-2">
							<SubmitButton {loading} />
						</div>
					</div>
					<div class="col-sm-8 p-2">
						<div class=" table-sm table-responsive">
							<table
								class="table table-head-fixed table-hover text-nowrap table-valign-middle table-light"
							>
								<thead>
									<tr>
										<th>{locale.T('date')}</th>
										<th>{locale.T('description')}</th>
										<th></th>
									</tr>
								</thead>
								<tbody class="table-sm">
									{#if get_appointment}
										<tr>
											<td style="width: 20%;">
												<DDMMYYYYFormat date={get_appointment.datetime} />
											</td>
											<td>
												<p class="text-break">
													{get_appointment?.description ?? ''}
												</p>
											</td>
											<td style="width: 10%;">
												<div>
													<button
														aria-label="deletemodal"
														type="button"
														class="btn btn-danger btn-sm"
														data-bs-toggle="modal"
														data-bs-target="#delete_modal"
														><i class="fa-solid fa-trash-can"></i>
													</button>
												</div>
											</td>
										</tr>
									{/if}
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</Form>
	</fieldset>
</div>
