<script lang="ts">
	import Currency from '$lib/coms/Currency.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import ConfirmModal from '$lib/coms-form/ConfirmModal.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import DdmmyyyyFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_visit, get_currency, get_vaccine_groups, get_injections } = $derived(data);
	let total_vaccine_service = $derived(
		data.get_visit?.billing?.charge.find((e) => e.charge_on === 'vaccine')?.price || 0
	);
	let product_id: number | null = $state(null);
	let product_name: string = $state('');
	let vaccine_id: number | null = $state(null);
	let group_id: number | null = $state(null);
	let get_dose = $derived(get_vaccine_groups?.find((e) => e.id === group_id)?.vaccineDose);
	let get_injection = $derived(get_injections?.filter((e) => e.group_id === group_id));
	let selectedVaccineId: number | null = $state(null);
	let selectedDoseId: number | null = $state(null);
</script>

<DeleteModal action="?/delete_vaccine" bind:id={vaccine_id} />
<ConfirmModal action="?/create_vaccine" id={product_id}>
	<ul class="list-group pb-2">
		<li class="list-group-item">
			<i class="fa-solid fa-crutch"></i>
			{product_name}
		</li>
	</ul>
	<input type="hidden" name="product_id" value={product_id} />
	<input type="hidden" name="group_id" value={group_id} />
	{#each get_injection || [] as v (v.id)}
		<ul class="list-group list-group-item-warning pb-2">
			{#each v?.vaccine || [] as item (item.id)}
				<li class="list-group-item">
					<label class="form-check-label">
						<input
							class="form-check-input"
							disabled={item.status}
							checked={item.status || selectedVaccineId === item.id}
							type="checkbox"
							name={selectedVaccineId === item.id ? 'vaccine_id' : undefined}
							value={item.id}
							id={`vaccine_id${item.id}`}
							onchange={() => {
								if (!item.status) {
									selectedVaccineId = selectedVaccineId === item.id ? null : item.id;
								}
							}}
						/>
						<span class="box" aria-hidden="true"></span>
						<span class="lbl">
							ចាក់លើកទី
							{item.times}
							{locale.T('date')}
							<DdmmyyyyFormat style="date" date={item.datetime_appointment} />
						</span>
					</label>
				</li>
			{/each}
		</ul>
	{/each}
	{#if !get_injection.some((e) => e.status === false)}
		<ul class="list-group">
			{#each get_dose || [] as item (item.id)}
				<li class="list-group-item">
					<input
						class="form-check-input me-1"
						checked={selectedDoseId === item.id}
						type="checkbox"
						name={selectedDoseId === item.id ? 'vaccine_dose_id' : undefined}
						value={item.id}
						id={`dose_id${item.id}`}
						onchange={() => {
							selectedDoseId = selectedDoseId === item.id ? null : item.id;
						}}
					/>
					<label class="form-check-label" for={`dose_id${item.id}`}>
						{item.times}
						{locale.T('times_')}
						{#each item.appointmentInjection || [] as iitem (iitem.id)}
							<button type="button" class="btn btn-success ms-3 btn-sm py-0">
								{iitem.days}
								{locale.T('day')}
							</button>
						{/each}
					</label>
				</li>
			{/each}
		</ul>
	{/if}
</ConfirmModal>

<fieldset disabled={get_visit?.billing?.status !== 'checking'}>
	<div class="row">
		{#each get_vaccine_groups || [] as item (item.id)}
			{@const products = item.product}
			{@const find_injection = get_injections?.find((e) => e.group_id === item.id)}
			<div class="col-md-3 pb-2">
				<div class="card bg-light h-100">
					<div class="card-header fs-5">
						{item?.name}
						{#if find_injection}
							<div class="float-end">
								<i class="fa-regular fa-circle-check text-primary"> </i>
							</div>
						{/if}
					</div>
					<div class="card-body">
						{#each products || [] as iitem (iitem.id)}
							{@const is_already = get_visit?.vaccine.some((e) => e.product_id === iitem.id)}
							{@const get_vaccine = get_visit?.vaccine.find((e) => e.product_id === iitem.id)}

							<div class="btn-group w-100">
								{#if is_already}
									<button
										aria-label="delete"
										data-bs-toggle="modal"
										data-bs-target="#delete_modal"
										type="button"
										onclick={() => {
											vaccine_id = Number(get_vaccine?.id);
										}}
										class="alert alert-danger px-2 me-1 py-1"
									>
										<i class="fa-solid fa-trash-can"></i>
									</button>
								{/if}
								<button
									type="button"
									onclick={() => {
										product_id = iitem.id;
										product_name = iitem.products;
										vaccine_id = Number(get_vaccine?.id);
										group_id = item.id;
									}}
									data-bs-toggle="modal"
									data-bs-target={!is_already && '#confirm_modal'}
									class="alert alert-primary px-2 py-1 text-start w-100"
									class:alert-danger={is_already}
								>
									<span>
										{iitem?.products}
									</span>
									<span class="text-end float-end">
										<Currency amount={iitem.price} symbol={get_currency?.currency} />
									</span>
									{#if iitem.generic_name}
										<span class="badge text-bg-primary">
											{iitem?.generic_name}
										</span>
									{/if}
								</button>
							</div>
						{/each}
					</div>
				</div>
			</div>
		{/each}
		<div class="card-footer row bg-light p-2 sticky-bottom">
			<div class="col text-end">
				<button type="button" class="btn btn-warning"
					>{locale.T('total')}
					<Currency
						class="fs-6"
						symbol={get_currency?.currency}
						amount={total_vaccine_service}
					/></button
				>
			</div>
		</div>
	</div>
</fieldset>
