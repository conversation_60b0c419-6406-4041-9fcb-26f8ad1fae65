import { db } from '$lib/server/db';
import { injection, product, vaccine, visit, group, vaccineDose } from '$lib/server/schemas';
import type { Actions, PageServerLoad } from './$types';
import { and, asc, eq } from 'drizzle-orm';
import { createProductOrder, deleteProductOrder } from '$lib/server/models';
import { DDMMYYYY_Format, YYYYMMDD_Format } from '$lib/server/utils';
import { logError, message } from '$lib/server/utils/telegram';
import { fail } from '@sveltejs/kit';
import { addDays } from '$lib/server/utils';
export const load = (async ({ params }) => {
	const id = parseInt(params.id);
	const get_currency = await db.query.currency.findFirst({});
	const get_visit = await db.query.visit.findFirst({
		with: {
			laboratoryRequest: {
				with: {
					product: true
				}
			},
			patient: true,
			staff: true,
			billing: {
				with: {
					charge: true
				}
			},
			vaccine: {
				with: {
					product: true,
					injection: true
				}
			}
		},
		where: eq(visit.id, +id)
	});
	const get_vaccine_groups = await db.query.group.findMany({
		where: eq(group.category_id, 3),
		with: {
			product: {
				orderBy: asc(product.products)
			},
			vaccineDose: {
				with: {
					appointmentInjection: true
				}
			}
		}
	});
	const get_injections = await db.query.injection.findMany({
		where: eq(injection.patient_id, get_visit?.patient_id ?? 0),
		with: {
			vaccine: true
		}
	});
	return {
		get_vaccine_groups,
		get_visit,
		get_currency,
		get_injections
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_vaccine: async ({ request, params, url }) => {
		const id = parseInt(params.id);
		const body = await request.formData();
		const { product_id, vaccine_dose_id, group_id, vaccine_id } = Object.fromEntries(
			body
		) as Record<string, string>;
		if (!product_id) return fail(400, { errId: true });
		const get_product = await db.query.product.findFirst({ where: eq(product.id, +product_id) });
		const get_visit = await db.query.visit.findFirst({
			where: eq(visit.id, +id),
			with: {
				patient: true,
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				}
			}
		});
		const get_injection = await db.query.injection.findFirst({
			where: and(
				eq(injection.patient_id, get_visit!.patient_id!),
				eq(injection.group_id, +group_id),
				eq(injection.status, false)
			)
		});
		const charge_on_vaccine = get_visit?.billing?.charge.find((e) => e.charge_on === 'vaccine');
		if (!get_injection) {
			if (!vaccine_dose_id || !group_id) return fail(400, { errId: true });
			const datetime_now = YYYYMMDD_Format.datetime(new Date());
			const get_vaccine_dose = await db.query.vaccineDose.findFirst({
				where: eq(vaccineDose.id, +vaccine_dose_id),
				with: {
					appointmentInjection: true
				}
			});
			const create_injection: { id: number }[] = await db
				.insert(injection)
				.values({
					patient_id: get_visit?.patient_id,
					datetime: YYYYMMDD_Format.datetime(new Date()),
					vaccine_dose_id: +vaccine_dose_id,
					group_id: +group_id
				})
				.$returningId()
				.catch((e) => {
					logError({ url, body, err: e });
					return [];
				});
			for (const e of get_vaccine_dose?.appointmentInjection ?? []) {
				await db
					.insert(vaccine)
					.values({
						injection_id: create_injection[0].id,
						times: e.times,
						datetime_appointment: YYYYMMDD_Format.datetime(addDays(datetime_now, e.days)),
						status: false
					})
					.catch((e) => {
						logError({ url, body, err: e });
					});
			}
		}
		const injection_ = await db.query.injection.findFirst({
			where: and(
				eq(injection.patient_id, get_visit!.patient_id!),
				eq(injection.group_id, +group_id),
				eq(injection.status, false)
			),
			with: {
				vaccine: true
			}
		});
		const vaccines = injection_?.vaccine || [];
		const times = vaccines.find((e) => e.id === +vaccine_id)?.times;
		if (vaccine_id && times) {
			for (let index = 0; index < vaccines.length; index++) {
				const element = vaccines[index];
				if (element.times < times) {
					await db
						.update(vaccine)
						.set({
							status: true
						})
						.where(eq(vaccine.id, element.id))
						.catch((e) => {
							logError({ url, body, err: e });
						});
				}
				if (element.times === times) {
					await db
						.update(vaccine)
						.set({
							visit_id: +id,
							product_id: +product_id
						})
						.where(eq(vaccine.id, element.id))
						.catch((e) => {
							logError({ url, body, err: e });
						});
				}
			}
		} else {
			for (let index = 0; index < vaccines.length; index++) {
				const element = vaccines[index];
				if (element.status === false) {
					await db
						.update(vaccine)
						.set({
							visit_id: +id,
							product_id: +product_id
						})
						.where(eq(vaccine.id, element.id))
						.catch((e) => {
							logError({ url, body, err: e });
						});
					break;
				}
			}
		}
		await createProductOrder({
			charge_id: Number(charge_on_vaccine?.id),
			price: Number(get_product?.price),
			product_id: Number(+product_id),
			qty: 1,
			body: body,
			url: url
		});
		const khemr_date = 'កាលបរិច្ឆេទ៖ '.concat(
			DDMMYYYY_Format(new Date().toISOString(), 'datetime')
		);
		const text = khemr_date
			.concat('\n')
			.concat('ឈ្មោះអ្នកជំងឺ៖ ')
			.concat(`${get_visit?.patient?.name_khmer}(${get_visit?.patient?.name_latin})`)
			.concat('\n')
			.concat('សេវាកម្ម៖ ')
			.concat(get_product?.products ?? '')
			.concat('\n')
			.concat('ID៖ ')
			.concat(`VC${injection_?.id}`);
		await message(text, 'VACCINE');
	},
	delete_vaccine: async ({ request, url, params }) => {
		const body = await request.formData();
		const visit_id = parseInt(params.id);
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (!id) return fail(400, { errId: true });
		const get_visit = await db.query.visit.findFirst({
			where: eq(visit.id, +visit_id),
			with: {
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				}
			}
		});
		const charge_on_vaccine = get_visit?.billing?.charge.find((e) => e.charge_on === 'vaccine');
		const get_vaccine = await db.query.vaccine.findFirst({
			where: eq(vaccine.id, +id),
			with: {
				injection: {
					with: {
						vaccine: true
					}
				}
			}
		});

		const product_order_ = charge_on_vaccine?.productOrder.find(
			(ee) => ee.product_id === get_vaccine?.product_id
		);
		if (get_vaccine?.injection?.vaccine.every((e) => e.status === false)) {
			await db
				.delete(injection)
				.where(eq(injection.id, Number(get_vaccine?.injection_id)))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		} else {
			await db
				.delete(vaccine)
				.where(eq(vaccine.id, +id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		await deleteProductOrder(Number(product_order_?.id));
	}
};
