<script lang="ts">
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import type { PageServerData } from './$types';
	import Words from '$lib/coms-cu/Words.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import SelectWords from '$lib/coms/SelectWords.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_words: get_words_subjective } = $derived(data);
	let loading = $state(false);
	let get_sujective = $state(data.get_visit?.subjective);
	let cheif_complaint = $state(get_sujective?.cheif_complaint || '');
	let history_of_present_illness = $state(get_sujective?.history_of_present_illness || '');
	let current_medication = $state(get_sujective?.current_medication || '');
	let past_medical_history = $state(get_sujective?.past_medical_history || '');
	let allesgy_medicine = $state(get_sujective?.allesgy_medicine || '');
	let surgical_history = $state(get_sujective?.surgical_history || '');
	let family_and_social_history = $state(get_sujective?.family_and_social_history || '');
	let pre_diagnosis = $state(get_sujective?.pre_diagnosis || '');
	let words_cheif_complaint = $derived(
		get_words_subjective.filter((e) => e.type === 'cheif_complaint')
	);
	let words_history_of_present_illness = $derived(
		get_words_subjective.filter((e) => e.type === 'history_of_present_illness')
	);
	let words_current_medication = $derived(
		get_words_subjective.filter((e) => e.type === 'current_medication')
	);
	let words_past_medical_history = $derived(
		get_words_subjective.filter((e) => e.type === 'past_medical_history')
	);
	let words_allesgy_medicine = $derived(
		get_words_subjective.filter((e) => e.type === 'allesgy_medicine')
	);
	let words_surgical_history = $derived(
		get_words_subjective.filter((e) => e.type === 'surgical_history')
	);
	let words_family_and_social_history = $derived(
		get_words_subjective.filter((e) => e.type === 'family_and_social_history')
	);
	let words_pre_diagnosis = $derived(
		get_words_subjective.filter((e) => e.type === 'pre_diagnosis')
	);
	let name = $state('');
	let type = $state('');
	let find_word = $derived(get_words_subjective.filter((e) => e.type === type));
</script>

<Words {name} {type} words={find_word} modal_name_type="word_modal" category="subjective" />

<div class="card bg-light">
	<div class="card-header fs-5">
		<span>#Subjective</span>
	</div>

	<Form reset={false} method="post" action="?/create_subjective" bind:loading>
		<div class="card-body">
			<div class=" row pb-2 pt-3">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#word_modal"
						onclick={() => {
							name = 'Cheif complaint';
							type = 'cheif_complaint';
						}}
						type="button"
						class="btn btn-outline-primary btn-sm">Cheif complaint</button
					>
				</div>
				<!-- <label for="cheif_coplaint" class="col-sm-3 col-form-label">Cheif complaint</label> -->
				<div class="col-sm-9">
					<!-- <textarea bind:value={cheif_complaint} rows="4" class="form-control" name="cheif_complaint">
				</textarea> -->
					<SelectWords
						name="cheif_complaint"
						type="textarea"
						rows="4"
						value={cheif_complaint}
						words={words_cheif_complaint.map((e) => e.text)}
					/>
				</div>
			</div>
			<div class=" row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#word_modal"
						onclick={() => {
							name = 'History of Present illness';
							type = 'history_of_present_illness';
						}}
						type="button"
						class="btn btn-outline-primary btn-sm">History of Present illness</button
					>
				</div>
				<!-- <label for="cheif_coplaint" class="col-sm-3 col-form-label">Cheif complaint</label> -->
				<div class="col-sm-9">
					<SelectWords
						name="history_of_present_illness"
						type="textarea"
						rows="4"
						value={history_of_present_illness}
						words={words_history_of_present_illness.map((e) => e.text)}
					/>
				</div>
			</div>
			<span class="btn btn-sm btn-info">Past Medicine History</span>
			<hr />
			<div class=" row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#word_modal"
						type="button"
						onclick={() => {
							name = 'Current Medication';
							type = 'current_medication';
						}}
						class="btn btn-outline-primary btn-sm">Current Medication</button
					>
				</div>
				<div class="col-sm-9">
					<div class="input-group">
						<SelectWords
							name="current_medication"
							value={current_medication}
							words={words_current_medication.map((e) => e.text)}
						/>
					</div>
				</div>
			</div>
			<div class=" row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#past_medical_history"
						onclick={() => {
							name = 'Past medical history';
							type = 'past_medical_history';
						}}
						type="button"
						class="btn btn-outline-primary btn-sm">Past medical history</button
					>
				</div>
				<div class="col-sm-9">
					<div class="input-group">
						<SelectWords
							name="past_medical_history"
							value={past_medical_history}
							words={words_past_medical_history.map((e) => e.text)}
						/>
					</div>
				</div>
			</div>
			<div class="row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#word_modal"
						onclick={() => {
							name = 'Allergy medicine';
							type = 'allesgy_medicine';
						}}
						type="button"
						class="btn btn-outline-primary btn-sm">Allergy medicine</button
					>
				</div>

				<div class="col-sm-9">
					<div class="input-group">
						<SelectWords
							name="allesgy_medicine"
							value={allesgy_medicine}
							words={words_allesgy_medicine.map((e) => e.text)}
						/>
					</div>
				</div>
			</div>
			<div class=" row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#word_modal"
						onclick={() => {
							name = 'Surgical history';
							type = 'surgical_history';
						}}
						type="button"
						class="btn btn-outline-primary btn-sm">Surgical history</button
					>
				</div>

				<div class="col-sm-9">
					<div class="input-group">
						<SelectWords
							name="surgical_history"
							value={surgical_history}
							words={words_surgical_history.map((e) => e.text)}
						/>
					</div>
				</div>
			</div>
			<div class=" row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#word_modal"
						onclick={() => {
							name = 'Family and social history';
							type = 'family_and_social_history';
						}}
						type="button"
						class="btn btn-outline-primary btn-sm">Family and social history</button
					>
				</div>

				<div class="col-sm-9">
					<div class="input-group">
						<SelectWords
							name="family_and_social_history"
							value={family_and_social_history}
							words={words_family_and_social_history.map((e) => e.text)}
						/>
					</div>
				</div>
			</div>
			<div class=" row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#word_modal"
						onclick={() => {
							name = 'Pre-Diagnosis';
							type = 'pre_diagnosis';
						}}
						type="button"
						class="btn btn-success btn-sm">Pre-Diagnosis</button
					>
				</div>

				<div class="col-sm-9">
					<div class="input-group">
						<SelectWords
							name="pre_diagnosis"
							value={pre_diagnosis}
							words={words_pre_diagnosis.map((e) => e.text)}
						/>
					</div>
				</div>
			</div>
		</div>
		<div class="card-footer">
			<div class="d-grid gap-2 d-md-flex justify-content-md-end">
				<SubmitButton {loading} />
			</div>
		</div>
	</Form>
</div>
