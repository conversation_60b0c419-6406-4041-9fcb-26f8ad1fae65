import type { LayoutServerLoad } from './$types';
import { db } from '$lib/server/db';
import { and, eq, isNull } from 'drizzle-orm';
import { progressNote, uploads } from '$lib/server/schemas';
import { redirect } from '@sveltejs/kit';

export const load: LayoutServerLoad = async ({ locals, cookies }) => {
	if (!locals.session || !locals.user || !locals.roles?.length) {
		redirect(302, '/login');
	}
	const get_upload_ = () =>
		db.query.uploads.findFirst({
			where: and(eq(uploads.mimeType, 'logo0'), eq(uploads.related_type, 'clinicinfo'))
		});
	const get_currency_ = () => db.query.currency.findFirst({});
	const lang = cookies.get('lang');
	const get_clinich_info_ = () => db.query.clinicinfo.findFirst({});
	const get_progress_note_ = () =>
		db.query.progressNote.findMany({
			where: isNull(progressNote.date_checkout),
			with: {
				patient: true,
				activeBed: {
					with: {
						bed: {
							with: {
								room: {
									with: {
										product: true
									}
								}
							}
						}
					}
				}
			}
		});
	const get_wards_ = () =>
		db.query.ward.findMany({
			with: {
				room: {
					with: {
						department: true,
						product: true,
						bed: true
					}
				}
			}
		});
	const [get_currency, get_clinich_info, get_progress_note, get_wards, get_upload] =
		await Promise.all([
			get_currency_(),
			get_clinich_info_(),
			get_progress_note_(),
			get_wards_(),
			get_upload_()
		]);
	return {
		user: locals.user,
		lang: lang,
		get_clinich_info,
		get_progress_note,
		get_wards,
		get_currency,
		get_upload
	};
};
