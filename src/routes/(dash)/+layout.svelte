<script lang="ts">
	import type { LayoutServerData } from './$types';
	import { page } from '$app/state';
	import { store } from '$lib/store/store.svelte';
	import ViewRoom from '$lib/coms-report/ViewRoom.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import { DDMMYYYY_Format } from '$lib/helper';
	import NavigationLoader from '$lib/coms/NavigationLoader.svelte';
	import { invalidateAll } from '$app/navigation';
	interface Props {
		data: LayoutServerData;
		children?: import('svelte').Snippet;
	}
	let { data, children }: Props = $props();
	let { get_clinich_info, get_progress_note, get_currency, get_wards, user, get_upload } =
		$derived(data);

	// client-side reactive copies for live updates
	let count_opd: number = $state(0);
	let count_ipd: number = $state(0);
	let count_pay_opd: number = $state(0);
	let count_pay_ipd: number = $state(0);
	let count_appoinment: number = $state(0);
	let count_laboratory: number = $state(0);
	let count_imagrie: number = $state(0);
	let count_vaccine: number = $state(0);
	$effect(() => {
		locale.L = (data?.lang as 'en' | 'km') || 'km';
	});

	// Function to save collapse state to localStorage
	function saveCollapseState(collapseId: string, isExpanded: boolean) {
		if (typeof window !== 'undefined') {
			localStorage.setItem(`collapse_${collapseId}`, isExpanded.toString());
		}
	}

	// Function to get collapse state from localStorage
	function getCollapseState(collapseId: string): boolean {
		if (typeof window !== 'undefined') {
			const saved = localStorage.getItem(`collapse_${collapseId}`);
			return saved === 'true';
		}
		return false;
	}

	// Reactive collapse states
	let collapseStates = $state({
		collapseLayouts: false,
		reprot_collapse: false,
		inventory: false,
		collapsemedecine: false,
		birth_death_collapse: false,
		collapsesetup: false
	});

	// Restore collapse states on mount
	$effect(() => {
		if (typeof window !== 'undefined') {
			// Use requestAnimationFrame for better performance
			requestAnimationFrame(() => {
				const collapseIds = [
					'collapseLayouts',
					'reprot_collapse',
					'inventory',
					'collapsemedecine',
					'birth_death_collapse',
					'collapsesetup'
				];

				collapseIds.forEach((collapseId) => {
					const isExpanded = getCollapseState(collapseId);

					// Update reactive state immediately
					collapseStates[collapseId as keyof typeof collapseStates] = isExpanded;

					// Defer DOM operations
					requestAnimationFrame(() => {
						const collapseElement = document.getElementById(collapseId);
						const toggleButton = document.querySelector(`[data-bs-target="#${collapseId}"]`);

						if (collapseElement && toggleButton) {
							if (isExpanded) {
								collapseElement.classList.add('show');
								toggleButton.setAttribute('aria-expanded', 'true');
								toggleButton.classList.remove('collapsed');
							} else {
								collapseElement.classList.remove('show');
								toggleButton.setAttribute('aria-expanded', 'false');
								toggleButton.classList.add('collapsed');
							}
						}
					});
				});

				// Add event listeners to save state when collapsed/expanded
				collapseIds.forEach((collapseId) => {
					const collapseElement = document.getElementById(collapseId);
					if (collapseElement) {
						collapseElement.addEventListener('shown.bs.collapse', () => {
							saveCollapseState(collapseId, true);
							collapseStates[collapseId as keyof typeof collapseStates] = true;
						});
						collapseElement.addEventListener('hidden.bs.collapse', () => {
							saveCollapseState(collapseId, false);
							collapseStates[collapseId as keyof typeof collapseStates] = false;
						});
					}
				});
			}, 100);
		}
	});
	let date = $state(
		DDMMYYYY_Format(new Date().toISOString(), 'datetime')
			.replace('pm', locale.T('pm'))
			.replace('am', locale.T('am'))
	);
	$effect(() => {
		store.inerHight = (window.innerHeight - 239).toString().concat('px');
		const interval = setInterval(() => {
			date = DDMMYYYY_Format(new Date().toISOString(), 'datetime')
				.replace('pm', locale.T('pm'))
				.replace('am', locale.T('am'));
		}, 5000);

		return () => {
			clearInterval(interval);
		};
	});

	function toggleSidebar() {
		const value = localStorage.getItem('sb|sidebar-toggle');
		document.body.classList.toggle('sb-sidenav-toggled');
		localStorage.setItem('sb|sidebar-toggle', value === 'false' ? 'true' : 'false');
	}

	// Close sidebar on outside click (mobile)
	function handleDocumentClick(event: MouseEvent) {
		const sidebar = document.getElementById('layoutSidenav_nav');
		const toggleBtn = document.getElementById('sidebarToggle');
		const target = event.target as Node | null;

		// Ignore clicks inside the sidebar or on the toggle button
		if (target && (sidebar?.contains(target) || toggleBtn?.contains(target))) return;

		// Only apply this behavior on small screens
		if (window.matchMedia('(max-width: 991.98px)').matches) {
			// If sidebar is open (toggled), close it
			if (document.body.classList.contains('sb-sidenav-toggled')) {
				document.body.classList.remove('sb-sidenav-toggled');
				localStorage.setItem('sb|sidebar-toggle', 'false');
			}
		}
	}

	$effect(() => {
		document.addEventListener('click', handleDocumentClick);
		return () => document.removeEventListener('click', handleDocumentClick);
	});

	// Subscribe to server-sent events for real-time counts
	$effect(() => {
		if (typeof window === 'undefined') return;

		// Delay EventSource to avoid blocking initial render
		const timeoutId = setTimeout(() => {
			const es = new EventSource('/api/stream/counts');
			es.onmessage = (e) => {
				try {
					const payload = JSON.parse(e.data);
					if (typeof payload.count_opd === 'number') count_opd = payload.count_opd;
					if (typeof payload.count_ipd === 'number') count_ipd = payload.count_ipd;
					if (typeof payload.count_pay_opd === 'number') count_pay_opd = payload.count_pay_opd;
					if (typeof payload.count_pay_ipd === 'number') count_pay_ipd = payload.count_pay_ipd;
					if (typeof payload.count_appoinment === 'number')
						count_appoinment = payload.count_appoinment;
					if (typeof payload.count_laboratory === 'number')
						count_laboratory = payload.count_laboratory;
					if (typeof payload.count_imagrie === 'number') count_imagrie = payload.count_imagrie;
					if (typeof payload.count_vaccine === 'number') count_vaccine = payload.count_vaccine;

					// Debounce invalidateAll
					requestAnimationFrame(() => invalidateAll());
				} catch (err) {
					console.warn('EventSource parse error:', err);
				}
			};
			es.onerror = () => es.close();

			return () => es.close();
		}, 1000);

		return () => clearTimeout(timeoutId);
	});
</script>

<svelte:head>
	<meta
		name="description"
		content="Hospital Management System - Comprehensive healthcare management solution"
	/>
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<meta name="theme-color" content="#0d6efd" />
	<meta name="robots" content="noindex, nofollow" />
	<link rel="preconnect" href="https://fonts.googleapis.com" />
	<link rel="preconnect" href="https://cdnjs.cloudflare.com" />
	<link rel="dns-prefetch" href="https://fonts.gstatic.com" />
	<title>Hospital Management System</title>
</svelte:head>

<ViewRoom data={{ get_progress_note, get_wards }} />
<!-- <LoginTracker /> -->
<!-- svelte-ignore a11y_no_redundant_roles -->
<nav
	class="sb-topnav navbar navbar-expand navbar-dark bg-primary"
	role="navigation"
	aria-label="Main navigation"
>
	<!-- Logo -->
	<img
		class="img-fluid rounded bg-white rounded-circle ms-2"
		loading="eager"
		src={get_upload?.filename || '/logo_krupet.png'}
		width="45"
		height="45"
		alt="Hospital Logo"
	/>

	<!-- Navbar Brand-->
	<div style="width: 169px;" class="d-sm-none d-md-block d-none d-sm-block text-truncate">
		<div class="navbar-brand fs-6">
		
				<!-- svelte-ignore a11y_distracting_elements -->
				<marquee>
					{#if locale.L === 'en'}
						{get_clinich_info?.title_eng || 'Hospital Management System'}
					{:else}
						{get_clinich_info?.title_khm || 'ប្រព័ន្ធគ្រប់គ្រងមន្ទីរពេទ្យ'}
					{/if}
					{get_clinich_info?.address ? ` - ${get_clinich_info.address}` : ''}
					{get_clinich_info?.contact ? ` - ${get_clinich_info.contact}` : ''}
				</marquee>
		
		</div>
	</div>
	<!-- Sidebar Toggle-->
	<!-- Navbar Search-->
	<button
		aria-label="Toggle sidebar navigation"
		onclick={toggleSidebar}
		class="btn btn-link text-light"
		id="sidebarToggle"
		type="button"
		><i class="fas fa-bars" aria-hidden="true"></i>
	</button>
	<div class="d-none d-md-inline-block form-inline ms-auto me-0 me-md-3 my-2 my-md-0"></div>
	<ul class="navbar-nav ms-auto">
		<Form showToast={false} method="post">
			{#if locale.L === 'km'}
				<li class="nav-item">
					<button
						formaction="/?/lang_en"
						class="nav-link btn btn-link border btn-sm py-0 ms-3"
						type="submit"
						aria-label="Switch to English"
					>
						<img height="15" width="20" src="/english.ico" alt="English language flag" />
					</button>
				</li>
			{/if}
			{#if locale.L === 'en'}
				<li class="nav-item">
					<button
						formaction="/?/lang_km"
						class="nav-link btn btn-link border btn-sm py-0 ms-3"
						type="submit"
						aria-label="Switch to Khmer"
					>
						<img height="15" width="20" src="/khmer.ico" alt="Khmer language flag" />
					</button>
				</li>
			{/if}
		</Form>

		<li class="nav-item position-relative">
			<button
				data-bs-toggle="modal"
				data-bs-target="#view_room"
				class="nav-link btn btn-link border btn-sm py-0 ms-2 active"
				type="button"
				aria-label="View rooms ({count_ipd} active)"
			>
				<i class="fa-solid fa-bed" aria-hidden="true"></i>
				<span
					class="position-absolute top-0 mt-0 start-100 translate-middle badge rounded-pill bg-danger"
					aria-label="{count_ipd} active rooms"
				>
					{count_ipd ?? ''}
				</span>
			</button>
		</li>

		<li class="nav-item dropdown">
			<button
				aria-label="User menu for {user?.username}"
				class="nav-link btn btn-link border btn-sm py-0 ms-3 active"
				type="button"
				data-bs-toggle="dropdown"
				aria-expanded="false"
			>
				<i class="fas fa-user fa-fw" aria-hidden="true"></i>
				{user?.username}
			</button>
			<ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
				<li>
					<a class="dropdown-item" href="/staff/create/?staff_id={user.staff_id}">
						<i class="fa-regular fa-user" aria-hidden="true"></i>
						{locale.T('user')} : {user?.username}
					</a>
				</li>
				<li><hr class="dropdown-divider" /></li>
				<li>
					<a data-sveltekit-preload-data="off" class="dropdown-item" href="/logout">
						<i class="fa-solid fa-right-from-bracket" aria-hidden="true"></i>
						{locale.T('logout')}
					</a>
				</li>
			</ul>
		</li>

		<li class="nav-item">
			<a
				href="/setting"
				class="nav-link btn btn-link border btn-sm py-0 mx-2 active"
				aria-label="Settings - Currency: {get_currency?.currency ?? ''}"
			>
				<i class="fa-solid fa-gear" aria-hidden="true"></i>
			</a>
		</li>
		<div class="d-sm-none d-md-block d-none d-sm-block">
			<li class="nav-item">
				<button
					class="nav-link btn btn-link border btn-sm py-0 me-2 active"
					type="button"
					aria-label="Current time: {date}"
				>
					<i class="fa-regular fa-clock fa-spin" aria-hidden="true"></i>
					{date}
				</button>
			</li>
		</div>
	</ul>
</nav>
<div id="layoutSidenav">
	<div class=" border-end" id="layoutSidenav_nav">
		<!-- svelte-ignore a11y_no_redundant_roles -->
		<nav
			class="sb-sidenav accordion sb-sidenav-light"
			id="sidenavAccordion"
			role="navigation"
			aria-label="Sidebar navigation"
		>
			<div class="sb-sidenav-menu">
				<div class="nav pt-2">
					<a
						href="/dashboard"
						class={page.url.pathname === '/dashboard' ? 'nav-link text-primary' : 'nav-link'}
						aria-current={page.url.pathname === '/dashboard' ? 'page' : undefined}
					>
						<i class="fa-solid fa-chart-pie" aria-hidden="true"></i>
						&nbsp;
						{locale.T('dashboard')}
					</a>
					<a
						class:active={page.url.pathname === '/billing/pos'}
						href="/billing/pos"
						class="nav-link"
					>
						<i class="fa-solid fa-cart-shopping"></i>
						&nbsp;
						{locale.T('pos')}
					</a>
					<!-- Billing  -->
					<button
						class:text-primary={page.url.pathname.includes('/billing') &&
							!page.url.pathname.includes('/billing/pos')}
						class="nav-link btn btn-link collapsed"
						type="button"
						data-bs-toggle="collapse"
						data-bs-target="#collapseLayouts"
						aria-expanded={collapseStates.collapseLayouts}
						aria-controls="collapseLayouts"
					>
						<div>
							<i class="fas fa-money-bills"></i>
							&nbsp;
						</div>
						{locale.T('billing')}
						{#if count_pay_ipd + count_pay_opd > 0}
							<span
								style="position: absolute; top: 10px; right: 19px;"
								class=" text-end float-end translate-middle text-bg-danger rounded-1 rounded-pill badge"
								>{count_pay_ipd + count_pay_opd}</span
							>
						{/if}
						<div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
					</button>
					<div
						class="collapse"
						id="collapseLayouts"
						aria-labelledby="headingOne"
						data-bs-parent="#sidenavAccordion"
					>
						<nav class="sb-sidenav-menu-nested nav">
							<a
								class:active={page.url.pathname === '/billing/opd'}
								href="/billing/opd"
								class="nav-link justify-content-between"
							>
								<span>
									<i class=" fas fa-stethoscope"></i>&nbsp;
									{locale.T('billing_opd')}
								</span>
								{#if count_pay_opd}
									<span class="translate-middle text-bg-danger rounded-1 rounded-pill badge"
										>{count_pay_opd}</span
									>
								{/if}
							</a>
							<a
								class:active={page.url.pathname === '/billing/ipd'}
								href="/billing/ipd"
								class="nav-link justify-content-between"
							>
								<span>
									<i class=" fas fa-procedures"></i>&nbsp;
									{locale.T('billing_ipd')}
								</span>
								{#if count_pay_ipd}
									<span class="translate-middle text-bg-danger rounded-1 rounded-pill badge"
										>{count_pay_ipd}</span
									>
								{/if}
							</a>
							<a
								class:active={page.url.pathname === '/billing/report'}
								href="/billing/report"
								class="nav-link"
							>
								<i class="fa-solid fa-sack-dollar"></i>&nbsp;
								{locale.T('sale_reprot')}
							</a>
						</nav>
					</div>
					<!-- End Billing  -->
					<!-- Report  -->
					<button
						class:text-primary={page.url.pathname.includes('/report/')}
						class="nav-link btn btn-link collapsed"
						type="button"
						data-bs-toggle="collapse"
						data-bs-target="#reprot_collapse"
						aria-expanded={collapseStates.reprot_collapse}
						aria-controls="reprot_collapse"
					>
						<div>
							<i class="fa-solid fa-receipt"></i>
							&nbsp;
						</div>
						{locale.T('report')}

						<div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
					</button>
					<div
						class="collapse"
						id="reprot_collapse"
						aria-labelledby="headingOne"
						data-bs-parent="#sidenavAccordion"
					>
						<nav class="sb-sidenav-menu-nested nav">
							<a
								class:active={page.url.pathname === '/report/daily'}
								href="/report/daily"
								class="nav-link justify-content-between"
							>
								<span>
									<i class="fa-solid fa-calendar-day"></i>&nbsp;
									{locale.T('daily_report')}
								</span>
							</a>
							<a
								class:active={page.url.pathname === '/report/monthly'}
								href="/report/monthly"
								class="nav-link justify-content-between"
							>
								<span>
									<i class="fa-solid fa-calendar-days"></i>&nbsp;
									{locale.T('monthly_report')}
								</span>
							</a>
						</nav>
					</div>
					<!-- End report  -->
					<!-- inventory  -->
					<button
						class:text-primary={page.url.pathname.includes('/product')}
						class="nav-link btn btn-link collapsed"
						type="button"
						data-bs-toggle="collapse"
						data-bs-target="#inventory"
						aria-expanded={collapseStates.inventory}
						aria-controls="inventory"
					>
						<div class:text-primary={page.url.pathname.includes('/product')}>
							<i class="fa-regular fa-folder-open"></i>
							&nbsp;
						</div>
						{locale.T('inventory')}
						<div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
					</button>

					<div
						class="collapse"
						id="inventory"
						aria-labelledby="headingOne"
						data-bs-parent="#sidenavAccordion"
					>
						<nav class="sb-sidenav-menu-nested nav">
							<a class:active={page.url.pathname === '/product'} href="/product" class="nav-link">
								<i class="fa-solid fa-briefcase-medical"></i> &nbsp;
								{locale.T('products')}
							</a>
							<a
								class:active={page.url.pathname === '/product/category'}
								href="/product/category"
								class="nav-link"
							>
								<i class="fa-solid fa-folder-tree"></i> &nbsp;
								{locale.T('category')}
							</a>
							<a
								class:active={page.url.pathname === '/product/supplier'}
								href="/product/supplier"
								class="nav-link"
							>
								<i class="fa-solid fa-people-carry-box"></i> &nbsp;
								{locale.T('supplier')}
							</a>
							<a
								class:active={page.url.pathname.includes('/product/purchase')}
								href="/product/purchase"
								class="nav-link"
							>
								<i class="fa-solid fa-basket-shopping"></i> &nbsp;
								{locale.T('purchase')}
							</a>
							<a
								class:active={page.url.pathname.includes('/product/exspend')}
								href="/product/exspend"
								class="nav-link"
							>
								<i class="fa-solid fa-percent"></i> &nbsp;
								{locale.T('exspend')}
							</a>
						</nav>
					</div>
					<!-- End Inventory  -->

					<a
						class:active={page.url.pathname === '/patient/all'}
						href="/patient/all"
						class="nav-link"
					>
						<i class="fas fa-restroom"></i> &nbsp;
						{locale.T('register_patient')}
					</a>
					<a
						class:active={page.url.pathname === '/patient/appointment'}
						href="/patient/appointment"
						class="nav-link justify-content-between"
					>
						<span>
							<i class="fa-regular fa-calendar"></i> &nbsp;
							{locale.T('appintment')}
						</span>
						{#if count_appoinment}
							<span class="translate-middle text-bg-danger rounded-1 rounded-pill badge"
								>{count_appoinment}</span
							>
						{/if}
					</a>
					<a
						class:active={page.url.pathname === '/patient/dialy'}
						href="/patient/dialy"
						class="nav-link justify-content-between"
					>
						<span>
							<i class="fa-solid fa-hospital-user"></i> &nbsp;
							{locale.T('dialy_visi_consult')}
						</span>
						{#if count_ipd + count_opd > 0}
							<span class="translate-middle text-bg-danger rounded-1 rounded-pill badge"
								>{count_ipd + count_opd}</span
							>
						{/if}
					</a>
					<a
						class:active={page.url.pathname === '/patient/opd' && !page.url.searchParams.has('emr')}
						href="/patient/opd"
						class="nav-link justify-content-between"
					>
						<span>
							<i class=" fas fa-stethoscope"></i>&nbsp;
							{locale.T('patients_opd')}
						</span>
						{#if count_opd}
							<span class="translate-middle text-bg-danger rounded-1 rounded-pill badge"
								>{count_opd}</span
							>
						{/if}
					</a>
					<a
						class:active={page.url.pathname === '/patient/ipd'}
						href="/patient/ipd"
						class="nav-link justify-content-between"
					>
						<span>
							<i class="fa-solid fa-bed"></i>&nbsp;
							{locale.T('patients_ipd')}
						</span>
						{#if count_ipd}
							<span class="translate-middle text-bg-danger rounded-1 rounded-pill badge"
								>{count_ipd}</span
							>
						{/if}
					</a>

					<!-- End patient  -->
					<!-- Medicine  -->
					<button
						class:text-primary={page.url.pathname.includes('/product/exspire') ||
							page.url.search.includes('?qcategory_id=1')}
						class="nav-link collapsed btn btn-link"
						type="button"
						data-bs-toggle="collapse"
						data-bs-target="#collapsemedecine"
						aria-expanded={collapseStates.collapsemedecine}
						aria-controls="collapsemedecine"
					>
						<div
							class:text-primary={page.url.pathname.includes('/product/exspire') ||
								page.url.search.includes('?qcategory_id=1')}
						>
							<i class="fas fa-pills"></i>
							&nbsp;
						</div>
						{locale.T('pharmacy')}
						<div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
					</button>

					<div
						class="collapse"
						id="collapsemedecine"
						aria-labelledby="headingOne"
						data-bs-parent="#sidenavAccordion"
					>
						<nav class="sb-sidenav-menu-nested nav">
							<a
								class:active={page.url.search.includes('?qcategory_id=1')}
								href="/product?qcategory_id=1"
								class="nav-link"
							>
								<i class="fas fa-tablets"></i> &nbsp;
								{locale.T('drug_list')}
							</a>
							<a
								class:active={page.url.pathname === '/product/exspire'}
								href="/product/exspire"
								class="nav-link"
							>
								<i class="fa-solid fa-business-time"></i> &nbsp;
								{locale.T('exspire_list')}
							</a>
						</nav>
					</div>
					<!-- End medicine -->

					<a
						class:active={page.url.pathname === '/imagerie'}
						href="/imagerie"
						class="nav-link justify-content-between"
					>
						<span>
							<i class=" fas fa-image"></i>
							&nbsp;
							{locale.T('imagerie')}
						</span>
						{#if count_imagrie}
							<span class="translate-middle text-bg-danger rounded-1 rounded-pill badge"
								>{count_imagrie}</span
							>
						{/if}
					</a>
					<a
						class:active={page.url.pathname === '/laboratory'}
						href="/laboratory"
						class="nav-link justify-content-between"
					>
						<span>
							<i class="fas fa-flask nav-icon"></i>
							&nbsp;
							{locale.T('laboratory')}
						</span>
						{#if count_laboratory}
							<span class="translate-middle text-bg-danger rounded-1 rounded-pill badge"
								>{count_laboratory}</span
							>
						{/if}
					</a>
					<a
						class:active={page.url.pathname === '/vaccine'}
						href="/vaccine"
						class="nav-link justify-content-between"
					>
						<span>
							<i class="fas fa-syringe"></i>
							&nbsp;
							{locale.T('vaccine')}
						</span>
						{#if count_vaccine}
							<span class="translate-middle text-bg-danger rounded-1 rounded-pill badge"
								>{count_vaccine}</span
							>
						{/if}
					</a>
					<!-- Birth and Dead  -->
					<button
						class:text-primary={page.url.pathname.includes('/birth-death')}
						class="nav-link btn btn-link collapsed"
						type="button"
						data-bs-toggle="collapse"
						data-bs-target="#birth_death_collapse"
						aria-expanded={collapseStates.birth_death_collapse}
						aria-controls="birth_death_collapse"
					>
						<div>
							<i class="fa-solid fa-cake-candles"></i>
							&nbsp;
						</div>
						{locale.T('birth')} - {locale.T('death')}

						<div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
					</button>
					<div
						class="collapse"
						id="birth_death_collapse"
						aria-labelledby="headingOne"
						data-bs-parent="#sidenavAccordion"
					>
						<nav class="sb-sidenav-menu-nested nav">
							<a
								class:active={page.url.pathname === '/birth-death/birth'}
								href="/birth-death/birth"
								class="nav-link justify-content-between"
							>
								<span>
									<i class="fa-regular fa-file-lines"></i>&nbsp;
									{locale.T('birth_report')}
								</span>
							</a>
							<a
								class:active={page.url.pathname === '/birth-death/death'}
								href="/birth-death/death"
								class="nav-link justify-content-between"
							>
								<span>
									<i class="fa-regular fa-file-excel"></i>&nbsp;
									{locale.T('death_report')}
								</span>
							</a>
						</nav>
					</div>
					<!-- End Birth and Dead   -->

					<a class:active={page.url.pathname === '/human'} href="/human" class="nav-link">
						<i class="fas fa-users"></i>
						&nbsp;
						{locale.T('hr')}
					</a>
					<button
						class:text-primary={page.url.pathname.includes('/settup')}
						class="nav-link btn btn-link collapsed"
						type="button"
						data-bs-toggle="collapse"
						data-bs-target="#collapsesetup"
						aria-expanded={collapseStates.collapsesetup}
						aria-controls="collapsesetup"
					>
						<div class:text-primary={page.url.pathname.includes('/settup')}>
							<i class="fas fa-tools"></i>
							&nbsp;
						</div>
						{locale.T('settup')}
						<div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
					</button>

					<div
						class="collapse"
						id="collapsesetup"
						aria-labelledby="headingOne"
						data-bs-parent="#sidenavAccordion"
					>
						<nav class="sb-sidenav-menu-nested nav">
							<a
								class:active={page.url.pathname === '/settup/lab-group'}
								href="/settup/lab-group"
								class="nav-link"
							>
								<i class="fas fa-vials"></i> &nbsp;
								{locale.T('lab_group')}
							</a>
							<a
								class:active={page.url.pathname.includes('/settup/parameter/')}
								href="/settup/parameter/group"
								class="nav-link"
							>
								<i class="fas fa-vial"></i>&nbsp;
								{locale.T('parameter')}
							</a>
							<a
								class:active={page.url.pathname === '/settup/img-list'}
								href="/settup/img-list"
								class="nav-link"
							>
								<i class="fa-regular fa-image"></i>&nbsp;
								{locale.T('imagerie_list')}
							</a>

							<a
								class:active={page.url.pathname === '/settup/img-template'}
								href="/settup/img-template"
								class="nav-link"
							>
								<i class="fas fa-sticky-note"></i>&nbsp;
								{locale.T('template_imagerie')}
							</a>

							<a
								class:active={page.url.pathname === '/settup/physical-exam'}
								href="/settup/physical-exam"
								class="nav-link"
							>
								<i class="fas fa-book-open"></i>&nbsp;
								{locale.T('physical_exam')}
							</a>

							<a
								class:active={page.url.pathname === '/settup/ward'}
								href="/settup/ward"
								class="nav-link"
							>
								<i class="fa-solid fa-hotel"></i>&nbsp; {locale.T('ward')}/{locale.T('room')}
							</a>

							<a
								class:active={page.url.pathname === '/settup/para-unit'}
								href="/settup/para-unit"
								class="nav-link"
							>
								<i class="fa-solid fa-layer-group"></i>&nbsp;
								{locale.T('para_unit')}
							</a>
							<a
								class:active={page.url.pathname === '/settup/clinic-info'}
								href="/settup/clinic-info"
								class="nav-link"
							>
								<i class=" fa-solid fa-circle-h"></i>&nbsp;Clinic-info
							</a>
						</nav>
					</div>
				</div>

				<!-- <div class="sb-sidenav-footer">
				<div class="small">Logged in as:</div>
				Start Bootstrap
			</div> -->
			</div>
		</nav>
	</div>
	<div class="bg-light" id="layoutSidenav_content">
		<!-- svelte-ignore a11y_no_redundant_roles -->
		<main class="container-fluid pt-3" role="main">
			<NavigationLoader />
			{@render children?.()}
		</main>
		<!-- <footer class="py-4 bg-light mt-auto">
				<div class="container-fluid px-4">
					<div class="d-flex align-items-center justify-content-between small">
						<div class="text-muted">Copyright &copy; Your Website 2023</div>
						<div>
							<a href={"#"}>Privacy Policy</a>
							&middot;
							<a href={"#"}>Terms &amp; Conditions</a>
						</div>
					</div>
				</div>
			</footer> -->
	</div>
</div>
