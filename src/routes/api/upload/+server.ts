import { db } from '$lib/server/db';
import { uploads } from '$lib/server/schemas';
import { and, eq } from 'drizzle-orm';
import type { RequestHand<PERSON> } from './$types';
import type { related_type } from '$lib/server/schemas/uploads';
import { json } from '@sveltejs/kit';
import { fileHandle } from '$lib/server/upload';
export const POST: RequestHandler = async ({ request, locals }) => {
	if (locals.user === null) {
		return json({
			status: 401,
			message: 'fail',
			error: 'Unauthorized'
		});
	}
	const body = await request.formData();
	const { related_id, related_type, old_filename } = Object.fromEntries(body) as Record<
		string,
		string
	>;
	if (!related_id || !related_type) {
		return json({
			status: 500,
			message: 'fail'
		});
	}
	const uuid = crypto.randomUUID();
	const get_upload = await db.query.uploads.findFirst({
		where: and(
			eq(uploads.related_id, +related_id),
			eq(uploads.related_type, related_type as related_type),
			eq(uploads.filename, old_filename)
		)
	});
	if (get_upload) {
		await db
			.update(uploads)
			.set({
				mimeType: uuid
			})
			.where(eq(uploads.id, get_upload.id));
		return json({
			status: 200,
			message: 'success',
			uuid: uuid,
			related_id: get_upload?.related_id,
			related_type: get_upload?.related_type
		});
	} else {
		const create_upload: { id: number }[] = await db
			.insert(uploads)
			.values({
				related_id: +related_id,
				related_type: related_type as related_type,
				filename: old_filename,
				mimeType: uuid
			})
			.$returningId();
		const get_upload = await db.query.uploads.findFirst({
			where: eq(uploads.id, create_upload[0].id)
		});
		return json({
			status: 200,
			message: 'success',
			uuid: get_upload?.mimeType,
			related_id: get_upload?.related_id,
			related_type: get_upload?.related_type
		});
	}
};
export const DELETE: RequestHandler = async ({ request, locals }) => {
	if (locals.user === null) {
		return json({
			status: 401,
			message: 'fail',
			error: 'Unauthorized'
		});
	}
	const body = await request.formData();
	const { filename } = Object.fromEntries(body) as Record<string, string>;
	if (!filename) {
		return json({
			status: 500,
			message: 'fail'
		});
	}
	await fileHandle.drop(filename);
	return json({
		status: 200,
		message: 'success'
	});
};
