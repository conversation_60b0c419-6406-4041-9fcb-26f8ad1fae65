import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { db } from '$lib/server/db';
import { and, eq, desc } from 'drizzle-orm';
import { uploads } from '$lib/server/schemas/uploads';
import type { related_type } from '$lib/server/schemas/uploads';
import { createSSE } from '$lib/server/utils/sse';

export const GET: RequestHandler = (event) => {
	const { locals, url } = event;
	if (!locals.user) {
		return new Response('Unauthorized', { status: 401 });
	}

	const related_id_param = url.searchParams.get('related_id');
	const related_type_param = url.searchParams.get('related_type');

	const related_id = related_id_param ? Number(related_id_param) : NaN;
	const related_type_value = (related_type_param ?? '') as related_type;

	if (!Number.isFinite(related_id) || !related_type_param) {
		return new Response('Bad Request', { status: 400 });
	}

	// Use the shared SSE utility; payload captures URL params via closure
	const payload = async () => {
		const rows = await db.query.uploads.findMany({
			where: and(eq(uploads.related_id, related_id), eq(uploads.related_type, related_type_value)),
			orderBy: [desc(uploads.id)],
			limit: 1
		});
		const upload = rows[0] ?? null;
		return { upload };
	};

	return createSSE(payload)(event);
};
