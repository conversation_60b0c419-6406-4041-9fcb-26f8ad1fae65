import { db } from '$lib/server/db';
import { and, eq, isNotNull, isNull, ne, or } from 'drizzle-orm';
import {
	billing,
	laboratory,
	progressNote,
	visit,
	imagerieRequest,
	vaccine
} from '$lib/server/schemas';
import { appointment } from '$lib/server/schemas/appointment';
import { createSSE } from '$lib/server/utils/sse';
import type { RequestHandler } from '@sveltejs/kit';

export const GET: RequestHandler = (event) => {
	const { locals } = event;
	if (!locals.user) {
		return new Response('Unauthorized', { status: 401 });
	}
	async function getCounts() {
		const count_opd_ = () =>
			db.$count(visit, and(eq(visit.checkin_type, 'OPD'), ne(visit.status, 'DONE')));
		const count_ipd_ = () => db.$count(progressNote, isNull(progressNote.date_checkout));
		const count_pay_opd_ = () =>
			db.$count(billing, and(eq(billing.status, 'paying'), eq(billing.billing_type, 'OPD')));
		const count_pay_ipd_ = () =>
			db.$count(
				billing,
				and(
					eq(billing.status, 'paying'),
					or(eq(billing.billing_type, 'IPD'), eq(billing.billing_type, 'CHECKING'))
				)
			);
		const count_appoinment_ = () => db.$count(appointment, eq(appointment.status, false));
		const count_laboratory_ = () => db.$count(laboratory, eq(laboratory.status, false));
		const count_imagrie_ = () => db.$count(imagerieRequest, eq(imagerieRequest.status, false));
		const count_vaccine_ = () =>
			db.$count(
				vaccine,
				and(eq(vaccine.status, false), isNotNull(vaccine.visit_id), isNotNull(vaccine.product_id))
			);

		const [
			count_opd,
			count_ipd,
			count_pay_opd,
			count_pay_ipd,
			count_appoinment,
			count_laboratory,
			count_imagrie,
			count_vaccine
		] = await Promise.all([
			count_opd_(),
			count_ipd_(),
			count_pay_opd_(),
			count_pay_ipd_(),
			count_appoinment_(),
			count_laboratory_(),
			count_imagrie_(),
			count_vaccine_()
		]);

		return {
			count_opd,
			count_ipd,
			count_pay_opd,
			count_pay_ipd,
			count_appoinment,
			count_laboratory,
			count_imagrie,
			count_vaccine
		};
	}
	return createSSE(getCounts)(event);
};
