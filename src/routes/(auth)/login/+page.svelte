<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { ActionData, PageServerData } from './$types';
	interface Props {
		form: ActionData;
		data: PageServerData;
	}
	let { form, data }: Props = $props();
	let { get_clinichinfo, get_upload } = $derived(data);
	let loading = $state(false);
	let show = $state(false);
</script>

<svelte:head>
	<title>Login - Hospital Management System</title>
	<meta name="description" content="Login to access the hospital management system" />
</svelte:head>

<section class="p-3 p-md-4 p-xl-5">
	<div class="container">
		{#if form?.message}
			<div class="alert alert-primary" role="alert">{form.message}</div>
		{/if}
		<div class="card border-light-subtle shadow-sm">
			<div class="row g-0">
				<div class="col-12 col-md-6 text-bg-primary">
					<div class="d-flex align-items-center justify-content-center h-100">
						<div class="col-10 col-xl-8 py-3 text-center justify-content-between">
							<img
								class="img-fluid rounded mb-4 bg-white rounded-circle"
								loading="lazy"
								src={get_upload?.filename}
								width="245"
								height="80"
								alt=""
							/>
							<hr class="border-primary-subtle mb-4" />
							<span class="  fs-2 mb-4 kh_font_muol">{get_clinichinfo?.title_khm}</span> <br />
							<span class="fs-3 mb-4">{get_clinichinfo?.title_eng}</span>
							<hr />
							<p class="lead text-start">
								{get_clinichinfo?.detail}
							</p>
						</div>
					</div>
				</div>
				<div class="col-12 col-md-6">
					<div class="card-body p-3 p-md-4 p-xl-5">
						<div class="row">
							<div class="col-12">
								<div class="mb-5">
									<h3>
										{#if locale.L === 'en'}
											<span>Hospital system</span>
										{:else}
											<span>ប្រព័ន្ធគ្រប់គ្រងមន្ទីរពេទ្យ</span>
										{/if}
									</h3>
								</div>
							</div>
						</div>
						<Form bind:loading showToast={false} action="?/login" method="post">
							<div class="row gy-3 gy-md-4 overflow-hidden">
								<div class="col-12">
									<label for="email" class="form-label"
										>{locale.T('username')} <span class="text-danger">*</span></label
									>
									<input
										autocomplete="off"
										type="text"
										class="form-control"
										name="username"
										id="username"
										placeholder="Username"
										required
									/>
								</div>
								<div class="col-12">
									<label for="password" class="form-label"
										>{locale.T('password')} <span class="text-danger">*</span></label
									>
									<div class="input-group">
										<input
											autocomplete="off"
											type={show ? 'text' : 'password'}
											placeholder="*****"
											class="form-control"
											name="password"
											id="password"
											value=""
											required
										/>
										<!-- svelte-ignore a11y_no_noninteractive_element_to_interactive_role -->
										<!-- svelte-ignore a11y_click_events_have_key_events -->
										<!-- svelte-ignore a11y_label_has_associated_control -->
										<label role="button" onclick={() => (show = !show)} class=" input-group-text">
											{#if show}
												<i class="fa-regular fa-eye"></i>
											{:else}
												<i class="fa-regular fa-eye-slash"></i>
											{/if}
										</label>
									</div>
								</div>

								<div class="col-12">
									<div class="float-end">
										<button disabled={loading} class="btn btn-primary" type="submit">
											{#if loading}
												<i class="fa-solid fa-spinner fa-spin"></i>
											{:else}
												<i class="fa-solid fa-arrow-right-to-bracket"></i>
											{/if}
											{locale.T('login')}
										</button>
										<br />
									</div>
									<!-- <button
										formaction="?/register"
										disabled={loading}
										class="btn bsb-btn-xl btn-primary"
										type="submit">{locale.T('register')}</button
									> -->
								</div>
							</div>
						</Form>
						<div class="row">
							<div class="col-12">
								<hr class="mt-5 mb-4 border-secondary-subtle" />
								<div class="d-flex gap-2 gap-md-4 flex-column flex-md-row justify-content-md-end">
									<a href="#!" class="link-secondary text-decoration-none">Create new account</a>
									<a href="#!" class="link-secondary text-decoration-none">Forgot password</a>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
