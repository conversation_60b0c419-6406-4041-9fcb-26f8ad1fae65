<script lang="ts">
	import type { PageServerData } from './$types';
	import { addDays, dobToAge } from '$lib/helper';
	import Renderhtml from '$lib/coms/Renderhtml.svelte';
	import ClinichInfo from '$lib/coms-report/ClinichInfo.svelte';
	import { page } from '$app/state';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Sign from '$lib/coms-report/Sign.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_imagerie_request, get_clinic_info, get_upload, get_inputer_sign } = $derived(data);
	let get_visit = $derived(get_imagerie_request?.visit);
	let age_p_visit = $derived(
		dobToAge(
			get_imagerie_request?.visit?.patient?.dob ?? '',
			get_imagerie_request?.visit?.date_checkup ?? ''
		)
	);
	let isPrint = page.url.searchParams.get('print');
	let row = page.url.searchParams.get('row');
	$effect(() => {
		document.addEventListener('keydown', function (event) {
			window.scrollTo({ top: 0, behavior: 'smooth' });
			if (event.ctrlKey && event.key === 'p') {
				// event.preventDefault();
				// alert('View only');
			}
		});

		if (isPrint === 'true') {
			setTimeout(async () => {
				window.print();
				window.close();
			}, 300);
		}
	});
</script>

<div class="header">
	<ClinichInfo data={{ get_clinic_info, get_upload }} />
	<div class="border table-responsive border-dark-subtle border-2 p-2">
		<table class="table table-borderless m-0">
			<tbody>
				<tr>
					<td style="width: 190px;" class="text-bold kh_font_battambang py-0">ឈ្មោះអ្នកជំងឺ</td>
					<td class="py-0">:</td>
					<td class="py-0">{get_visit?.patient?.name_khmer}</td>
					<td class="text-bold kh_font_battambang py-0">ភេទ</td>
					<td class="py-0">:</td>
					<td class="py-0"
						>{get_visit?.patient?.gender.toLowerCase() === 'female'
							? 'ស្រី'
							: get_visit?.patient?.gender.toLowerCase() === 'male'
								? 'ប្រុស'
								: ''}</td
					>
					<td class="text-bold kh_font_battambang py-0">លេខកូដ</td>
					<td class="py-0">:</td>
					<td class="py-0"
						>PT{get_visit?.patient_id}, IM{get_imagerie_request.id ?? ''},VS{get_visit?.id}
					</td>
				</tr>
				<tr class="py-0">
					<td class="text-bold kh_font_battambang py-0">ឈ្មោះជាឡាតាំង</td>
					<td class="py-0">:</td>
					<td class="py-0">{get_visit?.patient?.name_latin}</td>
					<td class="text-bold kh_font_battambang py-0">អាយុ</td>
					<td class="py-0">:</td>
					<td class="py-0">
						{age_p_visit}
					</td>
					<td style="font-size: 110%;" class="text-bold en_font_times_new_roman py-0">Visit</td>
					<td class="py-0">:</td>
					<td style="font-size: 110%;" class="en_font_times_new_roman py-0"
						>{get_visit?.checkin_type ?? ''}</td
					>
				</tr>
				<tr class="py-0">
					<td class="text-bold kh_font_battambang py-0">ទំនាក់ទំនង</td>
					<td class="py-0">:</td>
					<td style="font-size: 110%;" class="en_font_times_new_roman py-0">
						{get_visit?.patient?.telephone ?? ''}
					</td>
					<td class="text-bold kh_font_battambang py-0">អាសយដ្ឋាន</td>
					<td class="py-0">:</td>
					<td colspan="6" class="kh_font_battambang py-0">
						{get_visit?.patient?.village?.type ?? ''}
						{get_visit?.patient?.village?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.commune?.type ?? ''}
						{get_visit?.patient?.commune?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.district?.type ?? ''}
						{get_visit?.patient?.district?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.provice?.type ?? ''}
						{get_visit?.patient?.provice?.name_khmer ?? ''}
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</div>
<table class="w-100">
	<thead>
		<tr>
			<td>
				<div class="header-space">&nbsp;</div>
			</td>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td>
				<div class="border table-responsive border-dark-subtle border-2 p-2 mb-2">
					<table class="table table-borderless m-0">
						<thead>
							<tr class="py-0">
								<td style="width: 190px;" class="text-bold kh_font_battambang py-0"
									>គ្រូពេទ្យស្នើរសុំ</td
								>
								<td class="py-0">:</td>
								<td class="py-0"
									>{get_visit?.staff?.title?.kh} {get_visit?.staff?.name_khmer ?? ''}</td
								>

								<td style="font-size: 110%;" class="text-bold kh_font_battambang py-0"
									>កាលបរិច្ឆេទស្នើរសុំ</td
								>
								<td class="py-0">:</td>
								<td class="py-0">
									<DDMMYYYYFormat date={get_imagerie_request?.request_datetime} />
								</td>
							</tr>
							<tr class="py-0">
								<td class="text-bold kh_font_battambang py-0">ស្នើរសុំពិនិត្យ</td>
								<td class="py-0">:</td>
								<td class="py-0">
									{get_imagerie_request.product?.products ?? ''}
								</td>

								<td style="font-size: 110%;" class="text-bold kh_font_battambang py-0"
									>កាលបរិច្ឆេទលទ្ធផល</td
								>
								<td class="py-0">:</td>
								<td class="py-0">
									<DDMMYYYYFormat date={get_imagerie_request?.finish_datetime} />
								</td>
							</tr>
							<tr>
								<td class="text-bold kh_font_battambang py-0">Clinical Complaints</td>
								<td>:</td>
								<td colspan="7">
									<span class="text-break">
										{get_imagerie_request?.note ?? ''}
									</span>
								</td>
							</tr>
						</thead>
					</table>
				</div>
				<u>
					<h1 style="font-size: 130%;" class="text-center kh_font_muol">
						លទ្ធផលរូបភាពវេជ្ជសាស្ត្រ
					</h1>
					<h1 style="font-size: 120%;" class="text-center en_font_times_new_roman">
						Imagerie Report
					</h1>
				</u>
			</td>
		</tr>
		<tr>
			<td>
				{#if row === 'true'}
					<div style="width: 100%;">
						<!-- {get_imagerie_request?.is_ob_form} -->
						{#if get_imagerie_request?.is_ob_form}
							<div class="row g-0">
								{#each get_imagerie_request.resultImagerie as item}
									{#if item.result}
										<div class="col-5">
											<div class="row justify-content-between">
												<div class="col-auto">
													<span style="color:#0000FF" class="form-control border-0 fs-5 text-break">
														{item.resultForm?.name ?? ''}
													</span>
												</div>
												<div class="col-2">
													<span style="color:#0000FF" class="text-end form-control border-0 fs-5">
														:
													</span>
												</div>
											</div>
										</div>
										<div class="col-7">
											<span class="form-control border-0 fs-5 text-break">
												{#if item.resultForm?.type === 'datetime-local'}
													<DDMMYYYYFormat date={item.result} style="date" />
												{:else}
													{item.result || 'none'}
												{/if}
												{#if item.resultForm?.options.length === 1}
													<span style="color:#0000FF" class="fs-5 border-0">
														{item.resultForm?.options[0]?.name}
														{#if item.resultForm.name === 'Period'}
															<DDMMYYYYFormat
																style="date"
																date={addDays(new Date(item.result), 280).toString()}
															/>
														{/if}
													</span>
												{/if}
											</span>
										</div>
									{/if}
								{/each}
							</div>
						{:else}
							<Renderhtml setWidth="100%" value={get_imagerie_request?.result ?? ''} />
						{/if}
					</div>
					<div class="row">
						{#each get_imagerie_request?.uploads || [] as item}
							<div class="col-4">
								<img
									class="img-fluid"
									style="width: 100%;height: 100%;"
									src={item.filename}
									alt=""
								/>
							</div>
						{/each}
					</div>
				{:else if row === 'false'}
					<div class="row g-0">
						<div class="col-4">
							{#each get_imagerie_request?.uploads || [] as item}
								<div class="me-2">
									<img
										class="img-fluid"
										style="width: 100%;height: 100%;"
										src={item.filename}
										alt=""
									/>
								</div>
							{/each}
						</div>
						<div style="zoom: 90%;" class="col-8">
							{#if get_imagerie_request?.is_ob_form}
								<div class="row g-0">
									{#each get_imagerie_request.resultImagerie as item}
										{#if item.result}
											<div class="col-5">
												<div class="row justify-content-between">
													<div class="col-auto">
														<span
															style="color:#0000FF"
															class="form-control border-0 fs-5 text-break"
														>
															{item.resultForm?.name ?? ''}
														</span>
													</div>
													<div class="col-2">
														<span style="color:#0000FF" class="text-end form-control border-0 fs-5">
															:
														</span>
													</div>
												</div>
											</div>
											<div class="col-7">
												<span class="form-control border-0 fs-5 text-break">
													{#if item.resultForm?.type === 'datetime-local'}
														<DDMMYYYYFormat date={item.result} style="date" />
													{:else}
														{item.result || 'none'}
													{/if}
													{#if item.resultForm?.options.length === 1}
														<span style="color:#0000FF" class="fs-5 border-0">
															{item.resultForm?.options[0]?.name}
															{#if item.resultForm.name === 'Period'}
																<DDMMYYYYFormat
																	style="date"
																	date={addDays(new Date(item.result), 280).toString()}
																/>
															{/if}
														</span>
													{/if}
												</span>
											</div>
										{/if}
									{/each}
								</div>
							{:else}
								<Renderhtml setWidth="100%" value={get_imagerie_request?.result ?? ''} />
							{/if}
						</div>
					</div>
				{:else}
					<div class="">
						{#each get_imagerie_request?.uploads || [] as item}
							<div class="">
								<img
									class="img-fluid"
									style="width: 100%;height: 100%;"
									src={item.filename}
									alt=""
								/>
							</div>
						{/each}
						<Renderhtml setWidth="100%" value={get_imagerie_request?.result ?? ''} />
					</div>
				{/if}
			</td>
		</tr>
	</tbody>
	<tfoot>
		<tr>
			<td>
				<div class="footer-space">&nbsp;</div>
			</td>
		</tr>
	</tfoot>
</table>
<div class="footer">
	<Sign
		right={{
			date: get_imagerie_request?.finish_datetime,
			img: get_inputer_sign?.filename,
			name: `${get_imagerie_request?.inputBy?.title?.kh ?? ''} ${get_imagerie_request?.inputBy?.name_khmer ?? ''}`,
			role: `ហត្ថលេខាគ្រូពេទ្យ`
		}}
		qr={page.url.href}
	/>
	<hr />
	<h6 style="color:#0000FF" class="text-center">
		{get_clinic_info?.address ?? ''}
	</h6>
</div>

<style>
	/* @page {
		size: A4;
		margin: 7mm;
		margin-bottom: 3mm;
	} */
	@media print {
		.footer,
		.footer-space {
			height: 300px;
		}
		.header,
		.header-space {
			height: 250px;
		}
		.header {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
		}
		.footer {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
		}
	}
</style>
