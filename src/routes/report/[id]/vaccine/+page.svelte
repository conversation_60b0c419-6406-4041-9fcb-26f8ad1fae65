<script lang="ts">
	import { page } from '$app/state';
	import ClinichInfo from '$lib/coms-report/ClinichInfo.svelte';
	import Sign from '$lib/coms-report/Sign.svelte';
	import DdmmyyyyFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Name from '$lib/coms/Name.svelte';
	import { dobToAge } from '$lib/helper';
	import type { PageServerData } from './$types';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_injection, get_clinic_info, get_upload } = $derived(data);
	let get_visit = $derived(get_injection?.vaccine.find((e) => e.times === 1)?.visit);
	let age_p_visit = $derived(
		dobToAge(get_visit?.patient?.dob ?? '', get_visit?.date_checkup ?? '')
	);
</script>

<div class="header">
	<ClinichInfo data={{ get_clinic_info, get_upload }} />
	<div class="border table-responsive border-dark-subtle border-2 p-2">
		<table class="table table-borderless m-0">
			<thead>
				<tr>
					<td style="width: 190px;" class="text-bold kh_font_battambang py-0">ឈ្មោះអ្នកជំងឺ</td>
					<td class="py-0">:</td>
					<td class="py-0">{get_visit?.patient?.name_khmer}</td>
					<td class="text-bold kh_font_battambang py-0">ភេទ</td>
					<td class="py-0">:</td>
					<td class="py-0"
						>{get_visit?.patient?.gender.toLowerCase() === 'female'
							? 'ស្រី'
							: get_visit?.patient?.gender.toLowerCase() === 'male'
								? 'ប្រុស'
								: ''}</td
					>
					<td class="text-bold kh_font_battambang py-0">លេខកូដ</td>
					<td class="py-0">:</td>
					<td class="py-0">PT{get_visit?.patient_id}, VC{get_injection?.id ?? ''} </td>
				</tr>
				<tr class="py-0">
					<td class="text-bold kh_font_battambang py-0">ឈ្មោះជាឡាតាំង</td>
					<td class="py-0">:</td>
					<td class="py-0">{get_visit?.patient?.name_latin}</td>
					<td class="text-bold kh_font_battambang py-0">អាយុ</td>
					<td class="py-0">:</td>
					<td class="py-0">
						{age_p_visit}
					</td>
					<td style="font-size: 110%;" class="text-bold en_font_times_new_roman py-0"
						>កាលបរិច្ឆេទស្នើរសុំ</td
					>
					<td class="py-0">:</td>
					<td class="py-0">
						<DdmmyyyyFormat date={get_visit?.date_checkup} />
					</td>
				</tr>
				<tr class="py-0">
					<td class="text-bold kh_font_battambang py-0">គ្រូពេទ្យស្នើរសុំ</td>
					<td class="py-0">:</td>
					<td class="py-0">{get_visit?.staff?.title?.kh} {get_visit?.staff?.name_khmer ?? ''}</td>
					<td style="font-size: 110%;" class="text-bold en_font_times_new_roman py-0"
						>ប្រភេទវ៉ាក់សាំង</td
					>
					<td class="py-0">:</td>
					<td colspan="4" style="font-size: 110%;" class="en_font_times_new_roman py-0"
						>{get_injection?.group?.name}</td
					>
				</tr>
				<tr class="py-0">
					<td class="text-bold kh_font_battambang py-0">ទំនាក់ទំនង</td>
					<td class="py-0">:</td>
					<td style="font-size: 110%;" class="en_font_times_new_roman py-0">
						{get_visit?.patient?.telephone ?? ''}
					</td>
					<td class="text-bold kh_font_battambang py-0">អាសយដ្ឋាន</td>
					<td class="py-0">:</td>
					<td colspan="6" class="kh_font_battambang py-0">
						{get_visit?.patient?.village?.type ?? ''}
						{get_visit?.patient?.village?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.commune?.type ?? ''}
						{get_visit?.patient?.commune?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.district?.type ?? ''}
						{get_visit?.patient?.district?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.provice?.type ?? ''}
						{get_visit?.patient?.provice?.name_khmer ?? ''}
					</td>
				</tr>
			</thead>
		</table>
	</div>
</div>

<table class="w-100">
	<thead>
		<tr>
			<td>
				<div class="header-space">&nbsp;</div>
			</td>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td>
				<u>
					<h1 style="font-size: 130%;" class="text-center kh_font_muol">
						ប័ណ្ណសម្គាល់ចាក់វ៉ាក់សាំង
					</h1>
					<h1 style="font-size: 120%;" class="text-center en_font_times_new_roman">
						Vaccine Injection Report
					</h1>
				</u>
			</td>
		</tr>
		<tr>
			<td>
				<div class="col-md-12">
					<div class="table-responsive">
						<table class="table table-bordered text-nowrap text-center">
							<thead>
								<tr class="table-primary">
									<th colspan="7"> កាលវិភាគ - {get_injection?.group?.name} </th>
								</tr>
								<tr class="table-active">
									<th>ចំនួនដង</th>
									<th>កាលបរិច្ឆេទណាត់ជួប</th>
									<th>កាលបរិច្ឆេទចាក់ថ្នាំ</th>
									<th>ស្នើសុំដោយគ្រូពេទ្យ</th>
									<th>បានចាក់ថ្នាំដោយគ្រូពេទ្យ</th>
									<th>ឈ្មោះវ៉ាក់សាំង</th>
									<th style="width: fit-content">ស្ថានភាព</th>
								</tr>
							</thead>
							<tbody>
								{#each get_injection?.vaccine || [] as iitem}
									<tr class="table-warning">
										<td>លើកទី {iitem.times}</td>
										<td>
											<DdmmyyyyFormat style="date" date={iitem.datetime_appointment} />
										</td>
										<td>
											<DdmmyyyyFormat style="date" date={iitem.datetime_inject} />
										</td>

										<td>
											<Name both name={iitem?.visit?.staff} title={iitem?.visit?.staff?.title} />
										</td>
										<td>
											<Name both name={iitem?.injecter} title={iitem?.injecter?.title} />
										</td>
										<td>
											{iitem?.product?.products}
										</td>
										<td class="text-center">
											{#if iitem.status}
												&#10003;
											{:else}
												x
											{/if}
										</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</div>
				</div>
			</td>
		</tr>
	</tbody>
	<tfoot>
		<tr>
			<td>
				<div class="footer-space">&nbsp;</div>
			</td>
		</tr>
	</tfoot>
</table>
<div class="footer">
	<Sign qr={page.url.href} />
	<hr />
	<h6 style="color:#0000FF" class="text-center">
		{get_clinic_info?.address ?? ''}
	</h6>
</div>

<style>
	@media print {
		.footer,
		.footer-space {
			height: 280px;
		}
		.header,
		.header-space {
			height: 280px;
		}
	}
</style>
