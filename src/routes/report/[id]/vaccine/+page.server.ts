import { db } from '$lib/server/db';
import { injection, uploads, vaccine } from '$lib/server/schemas';
import { and, asc, desc, eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

export const load = (async ({ params }) => {
	const { id: injection_id } = params;
	const get_injection = await db.query.injection.findFirst({
		where: eq(injection.id, +injection_id),
		with: {
			group: true,
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			vaccine: {
				with: {
					injecter: {
						with: {
							title: true
						}
					},
					product: true,
					visit: {
						with: {
							staff: {
								with: {
									title: true
								}
							},
							patient: {
								with: {
									commune: true,
									district: true,
									provice: true,
									village: true
								}
							}
						}
					}
				},
				orderBy: asc(vaccine.id)
			}
		},
		orderBy: desc(injection.datetime)
	});
	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.mimeType, 'logo0'), eq(uploads.related_type, 'clinicinfo'))
	});
	return {
		get_clinic_info,
		get_upload,
		get_injection
	};
}) satisfies PageServerLoad;
