<script lang="ts">
	import { page } from '$app/state';
	import ClinichInfo from '$lib/coms-report/ClinichInfo.svelte';
	import Sign from '$lib/coms-report/Sign.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { dobToAge } from '$lib/helper';
	import type { PageServerData } from './$types';
	let { data }: { data: PageServerData } = $props();
	let { get_clinic_info, get_upload, get_sign, info } = $derived(data);
	let age_p_visit = $derived(dobToAge(info?.patient?.dob ?? '', info?.check_out ?? ''));
</script>

<div class="header">
	<ClinichInfo data={{ get_clinic_info, get_upload }} />
	<div class="border table-responsive border-dark-subtle border-2 p-2">
		<table class="table table-borderless m-0">
			<tbody>
				<tr>
					<td style="width: 190px;" class="text-bold kh_font_battambang py-0">ឈ្មោះអ្នកជំងឺ</td>
					<td class="py-0">:</td>
					<td class="py-0">{info?.patient?.name_khmer}</td>
					<td class="text-bold kh_font_battambang py-0">ភេទ</td>
					<td class="py-0">:</td>
					<td class="py-0"
						>{info?.patient?.gender.toLowerCase() === 'female'
							? 'ស្រី'
							: info?.patient?.gender.toLowerCase() === 'male'
								? 'ប្រុស'
								: ''}</td
					>
					<td class="text-bold kh_font_battambang py-0">លេខកូដ</td>
					<td class="py-0">:</td>
					<td class="py-0">PT{info?.patient?.id},VS{info?.id} </td>
				</tr>
				<tr class="py-0">
					<td class="text-bold kh_font_battambang py-0">ឈ្មោះជាឡាតាំង</td>
					<td class="py-0">:</td>
					<td class="py-0">{info?.patient?.name_latin}</td>
					<td class="text-bold kh_font_battambang py-0">អាយុ</td>
					<td class="py-0">:</td>
					<td class="py-0">
						{age_p_visit}
					</td>
					<td style="font-size: 110%;" class="text-bold en_font_times_new_roman py-0">Visit</td>
					<td class="py-0">:</td>
					<td style="font-size: 110%;" class="en_font_times_new_roman py-0"
						>{info?.checkin_type ?? ''}</td
					>
				</tr>
				<tr class="py-0">
					<td class="text-bold kh_font_battambang py-0">ទំនាក់ទំនង</td>
					<td class="py-0">:</td>
					<td style="font-size: 110%;" class="en_font_times_new_roman py-0">
						{info?.patient?.telephone ?? ''}
					</td>
					<td class="text-bold kh_font_battambang py-0">អាសយដ្ឋាន</td>
					<td class="py-0">:</td>
					<td colspan="6" class="kh_font_battambang py-0">
						{info?.patient?.village?.type ?? ''}
						{info?.patient?.village?.name_khmer.concat(',') ?? ''}
						{info?.patient?.commune?.type ?? ''}
						{info?.patient?.commune?.name_khmer.concat(',') ?? ''}
						{info?.patient?.district?.type ?? ''}
						{info?.patient?.district?.name_khmer.concat(',') ?? ''}
						{info?.patient?.provice?.type ?? ''}
						{info?.patient?.provice?.name_khmer ?? ''}
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="border table-responsive border-dark-subtle border-2 p-2 mb-2 mt-2">
		<table class="table table-borderless m-0">
			<thead>
				<tr class="py-0">
					<td style="width: 190px;" class="text-bold kh_font_battambang py-0">គ្រូពេទ្យពិនិត្យ</td>
					<td class="py-0">:</td>
					<td class="py-0">{info?.staff?.title?.kh} {info?.staff?.name_khmer ?? ''}</td>

					<td class="text-bold kh_font_battambang py-0">កាលបរិច្ឆេទ-ពិនិត្យ</td>
					<td class="py-0">:</td>
					<td class="py-0">
						<DDMMYYYYFormat date={info?.check_out} />
					</td>
				</tr>
				<tr class="py-0">
					<td class="text-bold en_font_times_new_roman py-0">Diagnosis (Dx)</td>
					<td class="py-0"> : </td>
					<td colspan="4" class="kh_font_battambang py-0 text-break"
						>{info?.accessment?.diagnosis_or_problem}
					</td>
				</tr>
			</thead>
		</table>
	</div>
</div>

<table class="w-100 '\n'">
	<thead>
		<tr>
			<td>
				<div class="header-space">&nbsp;</div>
			</td>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td>
				<h4 class="kh_font_muol_light text-center pt-1">វេជ្ជបញ្ជា</h4>
				<h4 class="en_font_times_new_roman text-center pt-1">Prescription (Rx)'}</h4>
			</td>
		</tr>
		<tr>
			<td>
				<div class="card-body mt-0 pt-0 zoom p-0">
					<table class="table mb-0 table-bordered table-hover text-wrap text-break '\n'">
						<thead class="">
							<tr class="text-center table-active">
								<th> ល.រ </th>
								<th> ប្រភេទឱសថ </th>
								<th> ឈ្មោះឱសថ </th>
								<th> របៀបប្រើ </th>
								<th> ពេលវេលាប្រើ </th>
								<th> រយៈពេល </th>
								<th> ចំនួន </th>
							</tr>
						</thead>
						<tbody>
							{#each info?.presrciption || [] as item, i}
								<tr class="text-center">
									<td>{i + 1}</td>
									<td>{item?.product?.group?.name ?? ''}</td>
									<td class="text-start">
										<span>{item.product?.products ?? ''}</span>
										{#if item.product?.generic_name}
											<br />
											<span class="">
												{`(${item.product?.generic_name ?? ''})`}
											</span>
										{/if}
									</td>
									<td>{item?.use}</td>
									<td class="text-start">
										<div>
											<span class="">
												{#if item.morning !== 0}
													ពេលព្រឹក {item.morning},
												{/if}
											</span>
											<span class="">
												{#if item.noon !== 0}
													ពេលថ្ងៃត្រង់ {item.noon},
												{/if}
											</span>
											<span class="">
												{#if item.afternoon !== 0}
													ពេលរសៀល {item.afternoon},
												{/if}
											</span>
											<span class="">
												{#if item.evening !== 0}
													ពេលល្ងាច {item.evening},
												{/if}
											</span>
											<span class="">
												{#if item.night !== 0}
													ពេលយប់ {item.night}
												{/if}
											</span>
										</div>
									</td>
									<td>{item?.duration}</td>
									<td>
										{item?.amount}
										{item.product?.unit?.unit ?? ''}
									</td>
								</tr>
							{/each}
							<tr class="text-center table-active">
								<td colspan="7">
									ឱសថសរុបចំនួន
									{info?.presrciption?.length ?? 0}
									មុខ។
								</td>
							</tr>
						</tbody>
					</table>
					<br />
					{#if info?.adviceTeaching}
						<h6 class="text-break kh_font_muol_light">ដំបូន្មានឬការណែនាំរបស់គ្រូពេទ្យ:</h6>
						<h6 class="text-break">
							{info?.adviceTeaching?.description}
						</h6>
					{/if}
					{#if info?.appointment}
						<hr />
						<h6>
							ណាត់ជួបពិនិត្យលើកក្រោយ
							<i class="text-decoration-underline">
								<DDMMYYYYFormat date={info?.appointment?.datetime} style="date" />
							</i>
							{info?.appointment?.description}
						</h6>
					{/if}
					<strong>សូមយកវេជ្ជបញ្ជាមកពេលពិនិត្យលើកក្រោយ!</strong>
				</div>
			</td>
		</tr>
	</tbody>
	<tfoot>
		<tr>
			<td>
				<div class="footer-space">&nbsp;</div>
			</td>
		</tr>
	</tfoot>
</table>

<div class="footer">
	<Sign
		qr={page.url.href}
		right={{
			date: info?.check_out,
			img: get_sign?.filename,
			name: `${info?.staff?.title?.kh ?? ''} ${info?.staff?.name_khmer ?? ''}`,
			role: `ហត្ថលេខាគ្រូពេទ្យព្យាបាល`
		}}
	/>
	<div>
		<hr />
		<h6 style="color:#0000FF" class="text-center">
			{get_clinic_info?.address ?? ''}
		</h6>
	</div>
</div>

<style>
	@media print {
		.footer,
		.footer-space {
			height: 300px;
		}
		.header,
		.header-space {
			height: 310px;
		}
	}
</style>
