import { db } from '$lib/server/db';
import { and, eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { progressNote, uploads, visit } from '$lib/server/schemas';
import { error } from '@sveltejs/kit';
import { YYYYMMDD_Format } from '$lib/server/utils';

export const load = (async ({ params, url }) => {
	const { id } = params;
	const visit_type = url.searchParams.get('visit_type') ?? '';
	if (!['ipd', 'opd'].includes(visit_type)) error(404, 'Not Found');
	const get_visit = await db.query.visit.findFirst({
		where: eq(visit.id, +id),
		with: {
			presrciption: {
				with: {
					product: {
						with: {
							group: true,
							unit: true
						}
					}
				}
			},
			patient: {
				with: {
					village: true,
					district: true,
					commune: true,
					provice: true
				}
			},
			staff: {
				with: {
					title: true
				}
			},
			accessment: true,
			adviceTeaching: true,
			appointment: true
		}
	});
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +id),
		with: {
			presrciption: {
				with: {
					product: {
						with: {
							group: true,
							unit: true
						}
					}
				}
			},
			patient: {
				with: {
					village: true,
					district: true,
					commune: true,
					provice: true
				}
			},
			accessment: true,
			adviceTeaching: true,
			appointment: true,
			pres_open: {
				with: {
					title: true
				}
			}
		}
	});
	const get_sign = await db.query.uploads.findFirst({
		where: and(
			eq(uploads.related_type, 'staffSign'),
			eq(
				uploads.related_id,
				visit_type === 'opd' ? Number(get_visit?.staff_id) : Number(get_progress_note?.pres_open_id)
			)
		)
	});
	let info = {
		check_out: YYYYMMDD_Format.datetime(get_visit?.date_checkup),
		patient: get_visit?.patient,
		staff: get_visit?.staff,
		presrciption: get_visit?.presrciption,
		accessment: get_visit?.accessment,
		adviceTeaching: get_visit?.adviceTeaching,
		appointment: get_visit?.appointment,
		get_sign: get_sign,
		id: get_visit?.id,
		checkin_type: 'OPD'
	};

	if (visit_type === 'ipd') {
		info = {
			check_out: YYYYMMDD_Format.datetime(get_progress_note?.date_checkout),
			patient: get_progress_note?.patient,
			staff: get_progress_note?.pres_open,
			presrciption: get_progress_note?.presrciption,
			accessment: get_progress_note?.accessment,
			adviceTeaching: get_progress_note?.adviceTeaching,
			appointment: get_progress_note?.appointment,
			get_sign: get_sign,
			id: get_progress_note?.id,
			checkin_type: 'IPD'
		};
	}

	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.mimeType, 'logo0'), eq(uploads.related_type, 'clinicinfo'))
	});
	return { get_clinic_info, get_upload, get_sign, info };
}) satisfies PageServerLoad;
