<!-- @migration-task Error while migrating Svelte code: `<div>` is invalid inside `<thead>` -->
<script lang="ts">
	import type { PageServerData } from './$types';
	import ClinichInfo from '$lib/coms-report/ClinichInfo.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { dobToAge } from '$lib/helper';
	import Currency from '$lib/coms/Currency.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_clinic_info, get_currency, previous_due, patient, get_visit, get_upload } =
		$derived(data);
	let get_billing = $derived(get_visit?.billing);
	let laboratory = $derived(
		get_billing?.charge.filter((e) => e.charge_on === 'laboratory').flatMap((e) => e)
	);
	let prescription = $derived(
		get_billing?.charge.filter((e) => e.charge_on === 'prescription').flatMap((e) => e.productOrder)
	);
	let products = $derived(
		get_billing?.charge
			.filter((e) => e.charge_on !== 'laboratory' && e.charge_on !== 'prescription')
			.flatMap((e) => e.productOrder)
	);
</script>

<ClinichInfo data={{ get_clinic_info, get_upload }} />
<hr />
<div style="font-size: 110%;" class="row invoice-info">
	<div class=" invoice-col text-center">
		<span class="fs-4 kh_font_muol_light">វិក្កយបត្រ</span><br />
		<span class="fs-5 en_font_times_new_roman">INVOICE</span>
	</div>
	<div>
		<table class="table table-sm table-responsive table-borderless border">
			<tbody>
				<tr>
					<td>អតិថិជន</td>
					<td> : </td>
					<td>
						{#if patient?.patient?.name_khmer}
							<span>{patient?.patient?.name_khmer ?? ''}</span>
						{/if}
						{#if patient?.patient?.name_latin}
							<span>{`( ${patient?.patient?.name_latin ?? ''} )`} </span>
						{/if}
						{#if !patient?.patient_id}
							<span>General</span>
						{/if}
					</td>
					<td>ភេទ</td>
					<td> : </td>
					<td>
						<span>
							{patient?.patient?.gender === 'Male' ? 'ប្រុស' : 'ស្រី'}
						</span></td
					>
					<td>អាយុ</td>
					<td> : </td>
					<td>
						{dobToAge(patient?.patient?.dob ?? '', new Date().toString())}
					</td>
					<td class="text-end">
						<span>លេខរៀងវិក្កយបត្រ #000{get_billing?.id} </span>
					</td>
				</tr>
				<tr>
					<td>
						<span>អាសយដ្ឋាន</span>
					</td>
					<td> : </td>
					<td colspan="7">
						<span>
							{patient?.patient?.village?.type ?? ''}
							{patient?.patient?.village?.name_khmer ?? ''}
							{patient?.patient?.commune?.type ?? ''}
							{patient?.patient?.commune?.name_khmer ?? ''}
							{patient?.patient?.district?.type ?? ''}
							{patient?.patient?.district?.name_khmer ?? ''}
							{patient?.patient?.provice?.type ?? ''}
							{patient?.patient?.provice?.name_khmer ?? ''}
						</span>
					</td>
					<td colspan="2" class="text-end">
						<span>កាលបរិច្ឆេទ <DDMMYYYYFormat date={patient?.date_checkout} /> </span>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</div>

<table class="table table-bordered ">
	<thead class="table-active">
		<tr class="text-center">
			<th style="width: 5%;">ល.រ <br /> No. </th>
			<th style="width: 50%;">
				ឈ្មោះទំនិញ <br />
				Name of Products
			</th>
			<th style="width: 15%;">
				ចំនួន <br />
				Quanity
			</th>
			<th style="width: 15%;">
				តម្លៃរាយ <br />
				Unit Price
			</th>
			<th style="width: 15%;">
				តម្លៃសរុប <br />
				Amount
			</th>
		</tr>
	</thead>
	<tbody>
		{#each products || [] as item, index}
			<tr>
				<td class="text-center">{index + 1}</td>
				<td>
					{item.product.products}
				</td>
				<td class="text-center">{item.qty} {item.unit?.unit ?? ''} </td>
				<td class="text-center">
					<Currency class="" amount={item.price} symbol={get_currency?.currency} />
				</td>
				<td class="text-center">
					<Currency class="" amount={item.total} symbol={get_currency?.currency} /></td
				>
			</tr>
		{/each}
		{#if get_billing?.billing_type !== 'CHECKING' && prescription?.length}
			<tr class="text-center">
				<td colspan="5"> វេជ្ជបញ្ជា </td>
			</tr>
		{/if}
		{#each prescription || [] as item_1, index_1}
			<tr>
				<td class="text-center">{index_1 + 1 + Number(products?.length)}</td>
				<td>
					{item_1.product.products}
				</td>
				<td class="text-center">{item_1.qty} {item_1.unit?.unit ?? ''} </td>
				<td class="text-center">
					<Currency class="" amount={item_1.price} symbol={get_currency?.currency} />
				</td>
				<td class="text-center">
					<Currency class="" amount={item_1.total} symbol={get_currency?.currency} /></td
				>
			</tr>
		{/each}
		{#if laboratory?.length && get_billing?.billing_type !== 'CHECKING'}
			<tr class="text-center">
				<td colspan="5"> ពិនិត្យឈាម ធ្វើតេស្តមន្ទីរពិសោធន៍ </td>
			</tr>
		{/if}
		{#each laboratory || [] as item_2, index}
			{@const laboratory_product = item_2.productOrder}
			{#each laboratory_product as item_3, index_3}
				<tr>
					<td class="text-center"
						>{index_3 +
							1 +
							index +
							Number(products?.length || 0 + laboratory_product?.length || 0) +
							Number(prescription?.length)}</td
					>
					<td>
						{item_3.product?.products ?? ''}
					</td>
					<td class="text-center">{item_3.qty} {item_3.unit?.unit ?? ''} </td>
					<td class="text-center">
						<Currency class="" amount={item_3.price} symbol={get_currency?.currency} />
					</td>
					<td class="text-center">
						<Currency class="" amount={item_3.total} symbol={get_currency?.currency} /></td
					>
				</tr>
			{/each}
		{/each}
		<tr class="border-bottom-0">
			<th colspan="4" class="text-end border-0">សរុប / Total {`(${get_currency?.currency})`} </th>
			<th class="border text-center">
				<Currency class="" amount={get_billing?.amount} symbol={get_currency?.currency} />
			</th>
		</tr>
		<tr class="border-0">
			<th colspan="4" class="text-end border-0">
				សរុប / Total {`(${get_currency?.exchang_to})`}
			</th>
			<th class="border text-center">
				<Currency
					class=""
					{get_currency}
					amount={get_billing?.amount}
					symbol={get_currency?.exchang_to}
				/>
			</th>
		</tr>
		{#if get_billing?.discount.includes('%') || Number(get_billing?.discount) > 0}
			<tr class="border-0">
				<th colspan="4" class="text-end border-0"> បញ្ចុះតម្លៃ / Discount </th>
				<th class="border text-center">
					{#if get_billing?.discount.includes('%')}
						{get_billing?.discount}
					{:else}
						<Currency
							class=""
							amount={Number(get_billing?.discount)}
							symbol={get_currency?.currency}
						/>
					{/if}
				</th>
			</tr>
		{/if}

		{#if Number(get_billing?.tax) > 0}
			<tr class="border-0">
				<th colspan="4" class="text-end border-0">ពន្ធ / Tax </th>
				<th class="border text-center">
					<Currency class="" amount={Number(get_billing?.tax)} symbol={get_currency?.currency} />
				</th>
			</tr>
		{/if}

		<tr class="border-0">
			<th colspan="4" class="text-end border-0">សរុបចុងក្រោយ / Grand Total </th>
			<th class="border text-center">
				<Currency class="" amount={get_billing?.total} symbol={get_currency?.currency} />
			</th>
		</tr>
		<tr class="border-0">
			<th colspan="4" class="text-end border-0">បានបង់ប្រាក់ / Paid Amount </th>
			<th class="border text-center">
				<Currency class="" amount={get_billing?.paid} symbol={get_currency?.currency} />
			</th>
		</tr>
		{#if Number(get_billing?.balance) > 0}
			<tr class="border-0">
				<th colspan="4" class="text-end border-0">ប្រាក់ជំពាក់ / Due </th>
				<th class="border text-center">
					<Currency class="" amount={get_billing?.balance} symbol={get_currency?.currency} />
				</th>
			</tr>
		{/if}
		{#if previous_due > 0}
			<tr class="border-0">
				<th colspan="4" class="text-end border-0">ប្រាក់ជំពាក់លើកមុន / Previous Due </th>
				<th class="border text-center">
					<Currency class="" amount={previous_due} symbol={get_currency?.currency} />
				</th>
			</tr>
		{/if}
		{#if previous_due + Number(get_billing?.balance) > 0}
			<tr class="border-0">
				<th colspan="4" class="text-end border-0">ប្រាក់ជំពាក់សរុប / Total Due </th>
				<th class="border text-center">
					<Currency
						class=""
						amount={previous_due + Number(get_billing?.balance)}
						symbol={get_currency?.currency}
					/>
				</th>
			</tr>
		{/if}
	</tbody>
</table>

<div>
	<div style="font-size: 110%;" class="en_font_times_new_roman row">
		<div class="col-6 text-center">
			<span>------------------------</span>
			<br />
			<span>ហត្ថលេខានឹងឈ្មោះអ្នកបង់ប្រាក់</span>
			<br />
			<span>Sign's Payment and Name</span>
		</div>
		<div class="col-6 text-center">
			<span>------------------------</span>
			<br />
			<span>ហត្ថលេខានឹងឈ្មោះអ្នកទទួលប្រាក់</span>
			<br />
			<span>Sign's Cashier and Name</span>
		</div>
	</div>
</div>
