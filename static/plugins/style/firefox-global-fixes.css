/* Firefox-specific global fixes
   This file is safe to include globally; rules inside apply only in Firefox
   via @supports (-moz-appearance: none).
*/

@supports (-moz-appearance: none) {
  /* Normalize form controls appearance */
  button,
  input,
  select,
  textarea {
    font: inherit;
  }
  input,
  select,
  textarea {
    line-height: 1.3;
  }

  /* Use consistent number/search input appearances in Firefox */
  input[type='number'] {
    -moz-appearance: textfield;
  }
  input[type='search'] {
    -moz-appearance: none;
  }

  /* Remove double focus outlines when custom focus styles exist */
  .btn:focus,
  .form-control:focus,
  .form-select:focus {
    outline: none;
  }

  /* Checkbox scale alignment (your app scales checkboxes) */
  input[type='checkbox'] {
    transform-origin: center;
    vertical-align: middle;
  }

  /* Select arrow spacing normalization */
  .form-select {
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    padding-right: 2rem;
  }

  /* Sticky elements reliability in Firefox */
  .sticky-top {
    position: sticky;
    top: 0;
    z-index: 1030;
  }

  /* Robust word wrapping */
  .text-break {
    overflow-wrap: anywhere;
    word-break: break-word;
  }

  /* Native scrollbar styling for Firefox to match WebKit look */
  * {
    scrollbar-width: thin;
    scrollbar-color: darkgrey #f1f1f1;
  }

  /* Font rendering tweak for complex scripts to reduce clipping */
  body {
    text-rendering: optimizeLegibility;
  }
}
