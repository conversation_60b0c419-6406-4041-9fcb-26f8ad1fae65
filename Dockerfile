# Secure, minimal Dockerfile using Node.js LTS (22) on Alpine and pnpm

# ---------- Build stage ----------
FROM node:22-alpine AS builder

WORKDIR /app

# Enable pnpm via corepack
ENV PNPM_HOME=/pnpm
ENV PATH=$PNPM_HOME:$PATH
RUN corepack enable && corepack prepare pnpm@latest --activate

# Copy lockfiles and install deps (dev deps included for build)
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

# Copy source and build
COPY . .
# Preload dotenv if build depends on .env
RUN NODE_OPTIONS=--require=dotenv/config pnpm run build

# Prune dev dependencies to reduce runtime attack surface
RUN pnpm prune --prod


# ---------- Runtime stage ----------
FROM node:22-alpine AS runner

# Install only needed system packages with no cache
# mariadb-client provides the MySQL CLI on Alpine; add tzdata for timezone handling
RUN apk add --no-cache mariadb-client tzdata

ENV NODE_ENV=production \
   TZ=Asia/Phnom_Penh

WORKDIR /app

# Copy production artifacts
COPY --from=builder /app/build ./build
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

# Drop privileges
USER node

EXPOSE 3000
CMD ["node", "build/index.js"]
