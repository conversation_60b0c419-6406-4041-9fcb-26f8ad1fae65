ALTER TABLE `appointment_injection` DROP FOREIGN KEY `appointment_injection_requester_id_staff_id_fk`;
--> statement-breakpoint
ALTER TABLE `appointment_injection` DROP FOREIGN KEY `appointment_injection_injecter_id_staff_id_fk`;
--> statement-breakpoint
ALTER TABLE `appointment_injection` DROP FOREIGN KEY `appointment_injection_injection_id_injection_id_fk`;
--> statement-breakpoint
ALTER TABLE `appointment_injection` DROP COLUMN `requester_id`;--> statement-breakpoint
ALTER TABLE `appointment_injection` DROP COLUMN `injecter_id`;--> statement-breakpoint
<PERSON>TER TABLE `appointment_injection` DROP COLUMN `datetime_inject`;--> statement-breakpoint
<PERSON>TER TABLE `appointment_injection` DROP COLUMN `status`;--> statement-breakpoint
ALTER TABLE `appointment_injection` DROP COLUMN `discription`;--> statement-breakpoint
<PERSON>TER TABLE `appointment_injection` DROP COLUMN `injection_id`;