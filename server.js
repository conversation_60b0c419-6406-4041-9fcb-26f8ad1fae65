import { handler } from './build/handler.js';
import express from 'express';
const app = express();
import dotenv from 'dotenv';
dotenv.config();

app.use('/uploads', express.static('uploads'));
// let SvelteKit handle everything else, including serving prerendered pages and static assets

app.use(handler);

// Newly exported function for grabbing the server
app.listen(3000, () => {
	console.log(`listening on port http://localhost:3000`);
});
