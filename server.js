import { handler } from './build/handler.js';
import express from 'express';
import compression from 'compression';
const app = express();
import dotenv from 'dotenv';
dotenv.config();

// Enable gzip compression for all responses
app.use(
	compression({
		level: 6, // Compression level (1-9, 6 is default)
		threshold: 1024, // Only compress responses larger than 1KB
		filter: (req, res) => {
			// Don't compress responses with this request header
			if (req.headers['x-no-compression']) {
				return false;
			}
			// Use compression filter function
			return compression.filter(req, res);
		}
	})
);

app.use('/uploads', express.static('uploads'));
// let SvelteKit handle everything else, including serving prerendered pages and static assets

app.use(handler);

// Newly exported function for grabbing the server
app.listen(3000, () => {
	console.log(`listening on port http://localhost:3000`);
});
